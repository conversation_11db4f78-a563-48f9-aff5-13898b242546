import React, { useCallback, useMemo, useRef } from 'react';
import {
  Box,
  FormControl,
  Select,
  MenuItem,
  CircularProgress
} from '@mui/material';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import { API_BASE_URL } from '../../../config';
import bookService from '../../../services/bookService';
import chatService from '../../../services/chatService';
import authService from '../../../services/authService';

const BookSelect = React.memo(({ 
  value, 
  onChange, 
  disabled, 
  books,
  isLoading
}) => {
  const selectedBook = useMemo(() => 
    books?.find(book => book.id === value),
    [books, value]
  );

  const selectWidth = useMemo(() => {
    if (!selectedBook) return '400px';
    const fullText = `${selectedBook.title} by ${selectedBook.author}`;
    const minWidth = 400;
    const maxWidth = 800;
    const charWidth = 11;
    const padding = 5;
    const calculatedWidth = Math.min(maxWidth, Math.max(minWidth, (fullText.length * charWidth) + padding));
    return `${calculatedWidth}px`;
  }, [selectedBook]);

  // Get auth headers using the centralized authService
  const getAuthHeaders = useCallback(async () => {
    try {
      const token = await authService.getToken();
      if (token) {
        return {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        };
      }
      return { 'Content-Type': 'application/json' };
    } catch (err) {
      console.error('Error getting auth token:', err);
      return { 'Content-Type': 'application/json' };
    }
  }, []);

  // Track if component is mounted to avoid state updates after unmount
  const isMountedRef = useRef(true);
  React.useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const handleChange = useCallback(async (event) => {
    if (!event.target.value) return;
    const bookId = event.target.value;
    const selectedBook = books.find(book => book.id === bookId);
    if (!selectedBook) return;
    
    try {
      // First notify of book change with loading state
      onChange({
        ...selectedBook,
        isLoading: true
      });
      
      // Fetch all data in parallel rather than series
      const [switchResponse, headers] = await Promise.all([
        bookService.switchBook(selectedBook.id),
        getAuthHeaders()
      ]);
      
      // Fetch preferences in parallel with chat history
      const [history, prefsResponse] = await Promise.all([
        chatService.getChatHistory(selectedBook.id),
        fetch(`${API_BASE_URL}/api/preferences/companion`, { headers })
      ]);
      
      // Only continue if component is still mounted
      if (!isMountedRef.current) return;
      
      let companionPreference = 'DEFAULT';
      if (prefsResponse.ok) {
        const prefsData = await prefsResponse.json();
        companionPreference = prefsData.companion;
      }
      
      onChange({
        ...selectedBook,
        chatHistory: history,
        companion: companionPreference,
        isLoading: false
      });
    } catch (error) {
      console.error("Error adding book to chat:", error);
      if (isMountedRef.current) {
        onChange({
          ...selectedBook,
          isLoading: false,
          error: error.message
        });
      }
    }
  }, [books, onChange, getAuthHeaders]);

  const bookItems = useMemo(() => books?.map((book) => (
    <MenuItem 
      key={book.id} 
      value={book.id}
    >
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <MenuBookIcon sx={{ width: 24, height: 24, mr: 1 }} />
        {book.title} by {book.author}
      </Box>
    </MenuItem>
  )) || [], [books]);

  return (
    <FormControl 
      disabled={disabled} 
      sx={{ 
        width: selectWidth,
        '& .MuiSelect-select': {
          paddingRight: '32px'
        }
      }}
    >
      <Select
        value={value || ''}
        onChange={handleChange}
        displayEmpty
        disabled={disabled}
      >
        <MenuItem value="">
          <em>Select a book</em>
        </MenuItem>
        {bookItems}
      </Select>
      {isLoading && (
        <CircularProgress 
          size={24}
          sx={{
            position: 'absolute',
            right: 32,
            top: '50%',
            marginTop: '-12px'
          }}
        />
      )}
    </FormControl>
  );
}, (prevProps, nextProps) => {
  // More precise equality comparison
  if (prevProps.disabled !== nextProps.disabled || 
      prevProps.isLoading !== nextProps.isLoading) {
    return false;
  }
  
  // Check if books array reference changed
  if (prevProps.books !== nextProps.books) {
    // If arrays are different lengths, they're different
    if (!prevProps.books || !nextProps.books || 
        prevProps.books.length !== nextProps.books.length) {
      return false;
    }
    // Only do deep comparison if absolutely necessary
  }
  
  // For values, just check equality of IDs
  return prevProps.value === nextProps.value;
});

export default BookSelect;
