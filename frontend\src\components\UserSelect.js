import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Typography,
  CircularProgress,
  Avatar,
  Chip,
  Tooltip,
  Paper
} from '@mui/material';
import { useAppContext } from '../context/AppContext';
import LoginPopup from './LoginPopup';
import RegisterPopup from './RegisterPopup';
import SuccessPopup from './SuccessPopup';
import LoginIcon from '@mui/icons-material/Login';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import LogoutIcon from '@mui/icons-material/Logout';
import PersonIcon from '@mui/icons-material/Person';

function UserSelect() {
  const { currentUser, authLoading, handleLogout, lastAuthError } = useAppContext();
  const [showLoginPopup, setShowLoginPopup] = useState(false);
  const [showRegisterPopup, setShowRegisterPopup] = useState(false);
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);

  // Message for registration success
  const successMessage = 'Check your email to login.';

  const handleLoginButtonClick = () => {
    setShowLoginPopup(true);
  };

  const handleRegisterButtonClick = () => {
    setShowRegisterPopup(true);
  };

  // Handler to trigger after successful registration
  const handleRegisterSuccess = () => {
    console.log('[DEBUG] handleRegisterSuccess called: closing register popup, opening success popup');
    setShowRegisterPopup(false);
    setTimeout(() => {
      console.log('[DEBUG] setShowSuccessPopup(true) called');
      setShowSuccessPopup(true);
    }, 300); // Small delay for smooth UX
  };

  useEffect(() => {
    console.log('[DEBUG] showRegisterPopup:', showRegisterPopup, 'showSuccessPopup:', showSuccessPopup);
  }, [showRegisterPopup, showSuccessPopup]);

  // Helper function to get user initials for avatar
  const getUserInitials = (username) => {
    if (!username) return '?';
    return username.substring(0, 2).toUpperCase();
  };

  // Generate avatar background color based on username
  const getAvatarColor = (username) => {
    if (!username) return '#C4A68A';
    
    let hash = 0;
    for (let i = 0; i < username.length; i++) {
      hash = username.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    const colors = [
      '#C4A68A', // primary
      '#E6B8A8', // secondary
      '#AB8B6E', // brown
      '#D19B88', // coral
      '#B48E7E', // tan
    ];
    
    const index = Math.abs(hash) % colors.length;
    return colors[index];
  };

  if (!currentUser && lastAuthError) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2, ml: 10 }}>
        <Typography color="error">Auth Error: {lastAuthError}</Typography>
        <Button
          variant="contained"
          color="primary"
          onClick={handleLoginButtonClick}
          sx={{
            py: 1,
            px: 2,
            borderRadius: '8px',
            background: 'linear-gradient(45deg, #AB8B6E 30%, #C4A68A 90%)',
            boxShadow: '0 3px 5px 2px rgba(196, 166, 138, .3)',
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: '0 6px 10px 2px rgba(196, 166, 138, .3)',
            }
          }}
          startIcon={<LoginIcon />}
        >
          Try Log In Again
        </Button>
        {showLoginPopup && 
          <LoginPopup 
            open={showLoginPopup} 
            setOpen={setShowLoginPopup} 
          />
        }
        {showRegisterPopup && 
          <RegisterPopup 
            open={showRegisterPopup} 
            setOpen={setShowRegisterPopup} 
            onSuccess={handleRegisterSuccess}
          />
        }
        <SuccessPopup
          open={showSuccessPopup}
          onClose={() => setShowSuccessPopup(false)}
          message={successMessage}
        />
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2, ml: 4 }}>
      {authLoading ? (
        <CircularProgress size={24} />
      ) : currentUser ? (
        <Paper 
          elevation={1}
          sx={{
            display: 'flex',
            alignItems: 'center',
            p: '6px 12px',
            pl: '6px',
            borderRadius: '24px',
            bgcolor: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(196, 166, 138, 0.3)',
            boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
            transition: 'all 0.3s ease',
            '&:hover': {
              boxShadow: '0 4px 12px rgba(0,0,0,0.12)',
              transform: 'translateY(-2px)'
            }
          }}
        >
          <Avatar
            sx={{
              width: 38,
              height: 38,
              bgcolor: getAvatarColor(currentUser.username),
              color: '#fff',
              fontWeight: 'bold',
              fontSize: '1rem',
              mr: 1.5,
              border: '2px solid #fff',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}
          >
            {getUserInitials(currentUser.username)}
          </Avatar>
          <Box sx={{ display: 'flex', flexDirection: 'column', mr: 1.5 }}>
            <Typography 
              variant="body2" 
              sx={{ 
                color: 'rgba(0, 0, 0, 0.6)',
                fontSize: '0.7rem',
                fontWeight: 500,
                lineHeight: 1,
                mb: 0.3,
                letterSpacing: '0.5px',
                textTransform: 'uppercase'
              }}
            >
              Current User
            </Typography>
            <Typography 
              variant="body1" 
              sx={{ 
                fontWeight: 600,
                fontSize: '0.95rem',
                lineHeight: 1.2,
                color: 'text.primary'
              }}
            >
              {currentUser.username}
            </Typography>
          </Box>
          <Tooltip title="Logout">
            <Button 
              variant="outlined" 
              color="secondary" 
              size="small" 
              onClick={handleLogout}
              sx={{
                minWidth: 0,
                p: '4px',
                borderRadius: '50%',
                border: '1px solid rgba(230, 184, 168, 0.5)',
                '&:hover': {
                  backgroundColor: 'rgba(230, 184, 168, 0.1)',
                  borderColor: 'rgba(230, 184, 168, 0.8)'
                }
              }}
            >
              <LogoutIcon fontSize="small" />
            </Button>
          </Tooltip>
        </Paper>
      ) : (
        <>
          <Button
            variant="contained"
            color="primary"
            onClick={handleLoginButtonClick}
            sx={{
              py: 1,
              px: 2,
              borderRadius: '8px',
              background: 'linear-gradient(45deg, #AB8B6E 30%, #C4A68A 90%)',
              boxShadow: '0 3px 5px 2px rgba(196, 166, 138, .3)',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 6px 10px 2px rgba(196, 166, 138, .3)',
              }
            }}
            startIcon={<LoginIcon />}
          >
            Log In
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleRegisterButtonClick}
            sx={{
              py: 1.5,
              borderRadius: '8px',
              background: 'linear-gradient(45deg, #E6B8A8 30%, #D19B88 90%)',
              boxShadow: '0 3px 5px 2px rgba(3, 3, 3, .3)',
              fontWeight: 'bold',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 6px 10px 2px rgba(3, 3, 3, .3)',
              }
            }}
            startIcon={<PersonAddIcon />}
          >
            Register
          </Button>
          {showLoginPopup && 
            <LoginPopup 
              open={showLoginPopup} 
              setOpen={setShowLoginPopup} 
            />
          }
          {showRegisterPopup && 
            <RegisterPopup 
              open={showRegisterPopup} 
              setOpen={setShowRegisterPopup} 
              onSuccess={handleRegisterSuccess}
            />
          }
          <SuccessPopup
            open={showSuccessPopup}
            onClose={() => setShowSuccessPopup(false)}
            message={successMessage}
          />
        </>
      )}
    </Box>
  );
}

export default UserSelect;
