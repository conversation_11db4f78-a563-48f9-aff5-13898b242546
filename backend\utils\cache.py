"""
Simple in-memory cache for chat history and other frequently accessed data
"""
import time
from typing import Any, Optional, Dict, Tuple
import logging

logger = logging.getLogger(__name__)

class SimpleCache:
    def __init__(self, default_ttl: int = 300):  # 5 minutes default TTL
        self.cache: Dict[str, Tuple[Any, float]] = {}
        self.default_ttl = default_ttl
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache if it exists and hasn't expired"""
        if key in self.cache:
            value, expiry = self.cache[key]
            if time.time() < expiry:
                logger.debug(f"Cache hit for key: {key}")
                return value
            else:
                # Remove expired entry
                del self.cache[key]
                logger.debug(f"Cache expired for key: {key}")
        
        logger.debug(f"Cache miss for key: {key}")
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache with optional TTL"""
        if ttl is None:
            ttl = self.default_ttl
        
        expiry = time.time() + ttl
        self.cache[key] = (value, expiry)
        logger.debug(f"Cache set for key: {key}, TTL: {ttl}s")
    
    def delete(self, key: str) -> None:
        """Delete specific key from cache"""
        if key in self.cache:
            del self.cache[key]
            logger.debug(f"Cache deleted for key: {key}")
    
    def clear(self) -> None:
        """Clear all cache entries"""
        self.cache.clear()
        logger.debug("Cache cleared")
    
    def cleanup_expired(self) -> None:
        """Remove all expired entries"""
        current_time = time.time()
        expired_keys = [
            key for key, (_, expiry) in self.cache.items() 
            if current_time >= expiry
        ]
        
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")

# Global cache instances
chat_cache = SimpleCache(default_ttl=180)  # 3 minutes for chat history
book_cache = SimpleCache(default_ttl=600)  # 10 minutes for book data
user_cache = SimpleCache(default_ttl=300)  # 5 minutes for user data

def get_chat_cache_key(user_id: int, book_id: Optional[int] = None, 
                      chat_id: Optional[str] = None, character: Optional[str] = None) -> str:
    """Generate cache key for chat history"""
    return f"chat:{user_id}:{book_id}:{chat_id}:{character}"

def get_book_cache_key(book_id: int) -> str:
    """Generate cache key for book data"""
    return f"book:{book_id}"

def get_user_cache_key(user_id: int) -> str:
    """Generate cache key for user data"""
    return f"user:{user_id}" 