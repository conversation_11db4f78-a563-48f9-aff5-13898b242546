"""Utility functions for handling AI prompt context insertion."""

def insert_context(prompt: str, book_context: str = "", chat_context: str = "") -> str:
    """
    Insert book and chat context into the character prompt.
    
    Args:
        prompt (str): The original character prompt with placeholders
        book_context (str): Information about relevant books from the library
        chat_context (str): Previous chat history/context
        
    Returns:
        str: The prompt with context inserted
    """
    # Replace placeholders with actual context
    # If no context is provided, remove the placeholder line entirely
    if book_context:
        prompt = prompt.replace("[Book Context Placeholder]", f"Book Context:\n{book_context}")
    else:
        prompt = prompt.replace("[Book Context Placeholder]\n", "")
        
    if chat_context:
        prompt = prompt.replace("[Previous Chat Context Placeholder]", f"Previous Chat Context:\n{chat_context}")
    else:
        prompt = prompt.replace("[Previous Chat Context Placeholder]\n", "")
        
    return prompt


def format_book_context(books: list) -> str:
    """
    Format book information into a string suitable for insertion into the prompt.
    
    Args:
        books (list): List of book objects containing relevant information
        
    Returns:
        str: Formatted book context string
    """
    if not books:
        return ""
        
    context_lines = []
    for book in books:
        # Build comprehensive book context with all available information
        book_info = [f"Title: {book.get('title', 'Unknown')}", f"Author: {book.get('author', 'Unknown')}"]
        
        # Add summary if available
        if 'summary' in book and book['summary']:
            book_info.append(f"Summary: {book['summary']}")
        
        # Add content if available (limit to reasonable size)
        if 'content' in book and book['content']:
            content = book['content']
            # Limit content to a reasonable size if needed
            max_content_length = 5000  # Adjust based on your model's context window
            if len(content) > max_content_length:
                content = content[:max_content_length] + "... [content truncated]"
            book_info.append(f"Content: {content}")
            
        # Add any other relevant book metadata
        for key, value in book.items():
            if key not in ('title', 'author', 'summary', 'content') and value:
                book_info.append(f"{key.capitalize()}: {value}")
                
        context_lines.append("\n".join(book_info))
        
    return "\n\n".join(context_lines)


def format_chat_context(messages: list, max_messages: int = 5) -> str:
    """
    Format previous chat messages into a string suitable for insertion into the prompt.
    
    Args:
        messages (list): List of previous chat message objects
        max_messages (int): Maximum number of previous messages to include
        
    Returns:
        str: Formatted chat context string
    """
    if not messages:
        return ""
        
    # Take the most recent messages up to max_messages
    recent_messages = messages[-max_messages:]
    
    context_lines = []
    for msg in recent_messages:
        # Assuming each message has role (user/assistant) and content
        formatted_msg = f"{msg.get('role', 'unknown').title()}: {msg.get('content', '')}"
        context_lines.append(formatted_msg)
        
    return "\n".join(context_lines)
