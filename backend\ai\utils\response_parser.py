"""Utilities for parsing AI responses."""
import re
import json
import logging
from typing import Dict, Any, List, Optional, Union


def extract_json_from_text(text: str, logger: Optional[logging.Logger] = None) -> Optional[Dict[str, Any]]:
    """Extract a JSON object from text that might contain other content.
    
    Args:
        text: The text containing JSON
        logger: Optional logger instance
        
    Returns:
        Extracted JSON as a dictionary or None if extraction failed
    """
    logger = logger or logging.getLogger(__name__)
    
    try:
        # Try to find JSON object using regex
        json_match = re.search(r'(\{.*\})', text, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
            return json.loads(json_str)
        
        # Try to find JSON array
        json_array_match = re.search(r'(\[.*\])', text, re.DOTALL)
        if json_array_match:
            json_str = json_array_match.group(1)
            return json.loads(json_str)
        
        # If no JSON pattern found, try parsing the whole text
        return json.loads(text)
    except Exception as e:
        logger.error(f"Failed to extract JSON from text: {str(e)}")
        logger.debug(f"Text that failed JSON extraction: {text[:200]}...")
        return None


def extract_code_blocks(text: str, language: Optional[str] = None) -> List[str]:
    """Extract code blocks from markdown text.
    
    Args:
        text: The markdown text containing code blocks
        language: Optional language identifier to filter by
        
    Returns:
        List of extracted code blocks
    """
    if language:
        pattern = r'```(?:' + language + r'|)\s*(.*?)```'
    else:
        pattern = r'```(?:\w*)\s*(.*?)```'
    
    matches = re.findall(pattern, text, re.DOTALL)
    return [match.strip() for match in matches]


def extract_list_items(text: str) -> List[str]:
    """Extract list items from markdown text.
    
    Args:
        text: The markdown text containing list items
        
    Returns:
        List of extracted items
    """
    # Match both numbered and bullet lists
    pattern = r'(?:^|\n)(?:\d+\.|\*|\-)\s*(.*?)(?=(?:\n(?:\d+\.|\*|\-)|$))'
    matches = re.findall(pattern, text, re.DOTALL)
    return [match.strip() for match in matches]


def clean_response(text: str) -> str:
    """Clean an AI response by removing common artifacts.
    
    Args:
        text: The text to clean
        
    Returns:
        Cleaned text
    """
    # Remove markdown code block syntax
    text = re.sub(r'```(?:\w*)\n', '', text)
    text = re.sub(r'```', '', text)
    
    # Remove AI self-references
    text = re.sub(r'(?i)As an AI|As a language model|As your AI assistant', '', text)
    
    # Remove excessive newlines
    text = re.sub(r'\n{3,}', '\n\n', text)
    
    return text.strip()


def parse_structured_response(text: str, expected_format: Dict[str, str], 
                              logger: Optional[logging.Logger] = None) -> Dict[str, str]:
    """Parse a response that should follow a specific format with labeled sections.
    
    Args:
        text: The response text
        expected_format: Dictionary mapping section names to regex patterns
        logger: Optional logger instance
        
    Returns:
        Dictionary with extracted sections
    """
    logger = logger or logging.getLogger(__name__)
    result = {}
    
    for section_name, pattern in expected_format.items():
        match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if match:
            result[section_name] = match.group(1).strip()
        else:
            logger.warning(f"Failed to extract '{section_name}' section from response")
            result[section_name] = ""
    
    return result
