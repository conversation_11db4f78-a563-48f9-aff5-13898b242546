import React, { useState, useCallback, useEffect } from 'react';
import { Box, Paper, Typography } from '@mui/material';
import ChatInterface from '../components/chat/ChatInterface';
import chatService from '../services/chatService';

/**
 * Chat Section component
 */
const ChatSection = ({ 
  selectedBook, 
  selectedCharacter, 
  setSelectedCharacter, 
  currentUser, 
  books, 
  handleBookSelect 
}) => {
  // States needed for the chat functionality
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [currentContext, setCurrentContext] = useState({
    chatId: null,
    book: selectedBook,
    character: selected<PERSON>haracter
  });
  
  // Format messages from backend to frontend format
  const formatMessagesFromBackend = (backendMessages) => {
    return backendMessages.map(msg => ({
      id: msg.id,
      content: msg.message,
      text: msg.message,
      is_user: msg.is_user,
      type: msg.is_user ? 'user' : 'ai',
      character: msg.character,
      timestamp: msg.timestamp,
      chat_id: msg.chat_id,
      book_id: msg.book_id
    }));
  };
  
  // The critical onSend function that was missing
  const onSend = useCallback(async (message, context) => {
    if (!message || !currentUser?.id) return;

    const messageObj = {
      id: Date.now(),
      content: message,
      text: message,
      is_user: true,
      type: 'user',
      timestamp: new Date().toISOString()
    };

    try {
      setMessages(prev => [...prev, messageObj]);
      setIsLoading(true);

      const response = await chatService.sendMessage({
        message,
        userId: currentUser.id,
        bookId: context?.book?.id,
        characterId: context?.character?.id,
        chatId: context?.chatId,
        bookTitle: context?.book?.title,
        bookAuthor: context?.book?.author
      });

      if (response) {
        const aiMessage = {
          id: response.id || Date.now() + 1,
          content: response.text,
          text: response.text,
          is_user: false,
          type: 'ai',
          timestamp: response.timestamp || new Date().toISOString(),
          character: context?.character
        };
        
        // Check if the AI message has actual content
        if (aiMessage.content && aiMessage.content.trim().length > 0 && aiMessage.content !== aiMessage.character?.name) {
          setMessages(prev => [...prev, aiMessage]);
        }
        
        // Update context with the new chat ID if available
        if (response.chatId) {
          setCurrentContext(prev => ({
            ...prev,
            chatId: response.chatId
          }));
        }
      }
    } catch (err) {
      console.error('Error sending message:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [currentUser?.id]);
  
  // Implement loadMoreMessages function
  const loadMoreMessages = useCallback(async () => {
    if (!selectedBook || !selectedCharacter || !currentUser?.id || !hasMore || isLoadingMore) return;
    
    try {
      setIsLoadingMore(true);
      const oldestMessageId = messages[0]?.id;
      
      const response = await chatService.getChatHistory({
        userId: currentUser.id,
        bookId: selectedBook.id,
        characterId: selectedCharacter.id,
        before: oldestMessageId
      });
      
      if (response && response.length > 0) {
        const formattedMessages = formatMessagesFromBackend(response);
        setMessages(prev => [...formattedMessages, ...prev]);
        setHasMore(response.length >= 50);
      } else {
        setHasMore(false);
      }
    } catch (err) {
      console.error('Error loading more messages:', err);
      setError(err.message);
    } finally {
      setIsLoadingMore(false);
    }
  }, [selectedBook, selectedCharacter, currentUser?.id, messages, hasMore, isLoadingMore]);
  
  // Implement clearChat function
  const clearChat = useCallback(async () => {
    try {
      if (currentUser?.id && selectedBook?.id) {
        await chatService.clearChatHistory({
          bookId: selectedBook.id,
          chatId: currentContext?.chatId
        });
      }
      setMessages([]);
      setCurrentContext(prev => ({
        ...prev,
        chatId: null
      }));
    } catch (err) {
      console.error('Error clearing chat:', err);
      setError(err.message);
    }
  }, [currentUser?.id, selectedBook?.id, currentContext?.chatId]);
  
  // Load initial chat history when user or book changes
  useEffect(() => {
    const loadHistory = async () => {
      if (!currentUser?.id || !selectedBook?.id) return;
      setIsLoading(true);
      try {
        const history = await chatService.getChatHistory({ bookId: selectedBook.id });
        const formatted = formatMessagesFromBackend(history);
        setMessages(formatted);
        if (formatted.length > 0) {
          const lastMsg = formatted[formatted.length - 1];
          setCurrentContext(prev => ({
            ...prev,
            chatId: lastMsg.chat_id,
            book: selectedBook,
            character: selectedCharacter
          }));
        }
      } catch (err) {
        console.error('Error loading chat history:', err);
        setError(err.message || 'Failed to load chat history');
      } finally {
        setIsLoading(false);
      }
    };
    loadHistory();
  }, [selectedBook?.id, currentUser?.id]);
  
  if (!currentUser) {
    return (
      <Box sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h6" color="error" gutterBottom>
          You need to log in to access the chat
        </Typography>
      </Box>
    );
  }
  
  return (
    <Paper sx={{ p: 0, bgcolor: '#fafafa', borderRadius: '16px' }}>
      <Box sx={{ bgcolor: 'primary.main', p: 2, borderTopLeftRadius: '16px', borderTopRightRadius: '16px' }}>
        <Typography variant="h6" align="center" sx={{ color: 'black' }}>
          Chat with {selectedCharacter ? selectedCharacter.name : 'AI'}
        </Typography>
      </Box>
      <Box sx={{ p: 3 }}>
        <ChatInterface 
          selectedBook={selectedBook} 
          selectedCharacter={selectedCharacter}
          userId={currentUser?.id}
          onCharacterChange={(character) => setSelectedCharacter(character)}
          books={books}
          onBookChange={handleBookSelect}
          // Pass all the needed chat props
          messages={messages}
          isLoading={isLoading}
          error={error}
          onSend={onSend}
          isLoadingMore={isLoadingMore}
          hasMore={hasMore}
          loadMoreMessages={loadMoreMessages}
          clearChat={clearChat}
          currentContext={currentContext}
        />
      </Box>
    </Paper>
  );
};

export default ChatSection; 