import React, { useEffect, useRef } from 'react';
import { Box } from '@mui/material';
import ChatMessage from './ChatMessage';
import { MESSAGE_LIST_STYLES } from '../constants/styleConstants';
import { scrollToBottom } from '../utils/chatUtils';

const ChatMessageList = React.memo(({
  messages,
  isLoading,
  selectedCharacter
}) => {
  const messagesEndRef = useRef(null);

  useEffect(() => {
    scrollToBottom(messagesEndRef);
  }, [messages]);

  return (
    <Box sx={MESSAGE_LIST_STYLES} ref={messagesEndRef}>
      {messages.map((message, index) => (
        <ChatMessage
          key={`${message.timestamp}-${index}`}
          message={message}
          selectedCharacter={selectedCharacter}
        />
      ))}
      {isLoading && (
        <ChatMessage
          message={{
            content: 'Thinking...',
            type: 'loading'
          }}
        />
      )}
    </Box>
  );
});

export default ChatMessageList;
