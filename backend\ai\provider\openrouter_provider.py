"""OpenRouter provider implementation for the BookWorm application."""
from openai import OpenAI
import traceback
from typing import Dict, List, Any, Generator, Optional
import logging
from backend.ai.provider.base_provider import BaseProvider


class OpenRouterProvider(BaseProvider):
    """Provider implementation for OpenRouter API."""
    
    def __init__(self, api_key: str, model: str, base_url: str = "https://openrouter.ai/api/v1", 
                 logger: Optional[logging.Logger] = None):
        """Initialize the OpenRouter provider.
        
        Args:
            api_key: OpenRouter API key
            model: Model name to use
            base_url: Base URL for the OpenRouter API
            logger: Logger instance
        """
        super().__init__(model, logger or logging.getLogger(__name__))
        self.client = OpenAI(api_key=api_key, base_url=base_url)
        self.base_url = base_url
        self.api_key = api_key  # Store the API key as an attribute
        self.logger.info("OpenRouter client initialized successfully")
    
    def chat(self, messages: List[Dict[str, str]], system_prompt: str = None, max_tokens: int = 1000, temperature: float = 0.7, stream: bool = False) -> Any:
        """Send a chat request to the OpenRouter API."""
        try:
            self.logger.debug(f"Sending chat request to OpenRouter with {len(messages)} messages")
            self.logger.debug(f"OpenRouter API key: {self.client.api_key[:5]}...")
            self.logger.debug(f"OpenRouter base URL: {self.base_url}")
            
            # Ensure we have a valid model
            model_to_use = self.model
            if not model_to_use or model_to_use == "default":
                model_to_use = "google/gemini-2.0-flash-001"
                self.logger.info(f"Using default model for OpenRouter: {model_to_use}")
            
            # Add system prompt if provided
            if system_prompt:
                messages = [{"role": "system", "content": system_prompt}] + messages
            
            response = self.client.chat.completions.create(
                model=model_to_use,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=stream
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"OpenRouter API call error: {str(e)}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            
            if "api key" in str(e).lower():
                return f"Invalid API key for OpenRouter"
            elif "rate limit" in str(e).lower():
                return f"Rate limit exceeded for OpenRouter"
            else:
                return f"Error calling OpenRouter API: {str(e)}"
    
    def process_response(self, response: Any) -> str:
        """Process the response from the OpenRouter API.
        
        Args:
            response: The response from the OpenRouter API
            
        Returns:
            The processed response as a string
        """
        try:
            self.logger.debug(f"Processing OpenRouter response of type: {type(response)}")
            
            # Handle error responses
            if isinstance(response, str) and ("error" in response.lower() or "invalid" in response.lower()):
                return response
            
            # Handle OpenRouter response object (similar to OpenAI)
            if hasattr(response, 'choices') and len(response.choices) > 0:
                message = response.choices[0].message
                if hasattr(message, 'content'):
                    return message.content
                else:
                    self.logger.warning(f"Message missing content attribute: {message}")
                    return str(message)
            
            # Fallback for unexpected response format
            self.logger.warning(f"Unexpected response format: {response}")
            if isinstance(response, dict):
                if 'content' in response:
                    return response['content']
                elif 'message' in response:
                    return response['message']
                elif 'choices' in response and len(response['choices']) > 0:
                    choice = response['choices'][0]
                    if 'message' in choice and 'content' in choice['message']:
                        return choice['message']['content']
            
            # Last resort fallback
            return "I'm sorry, I couldn't generate a proper response. Please try again."
        
        except Exception as e:
            self.logger.error(f"Error processing OpenRouter response: {str(e)}")
            self.logger.error(f"Response that caused error: {response}")
            return f"Error processing response: {str(e)}"
    
    def process_stream(self, response: Any, chat_id: Optional[str] = None) -> Generator[Dict[str, Any], None, None]:
        """Process a streaming response from the OpenRouter API.
        
        Args:
            response: The streaming response from the OpenRouter API
            chat_id: Optional chat ID to include in the final chunk
            
        Returns:
            Generator yielding chunks of the response
        """
        for chunk in response:
            if hasattr(chunk, 'choices') and len(chunk.choices) > 0:
                delta = chunk.choices[0].delta
                if hasattr(delta, 'content') and delta.content:
                    yield {'content': delta.content}
                elif not hasattr(delta, 'content') or delta.content == "":
                    # This is likely the final chunk
                    yield {'done': True, 'chat_id': chat_id}
            else:
                # Handle dictionary format
                if isinstance(chunk, dict):
                    if 'choices' in chunk and len(chunk['choices']) > 0:
                        delta = chunk['choices'][0].get('delta', {})
                        if 'content' in delta and delta['content']:
                            yield {'content': delta['content']}
                        elif 'content' not in delta or not delta['content']:
                            # This is likely the final chunk
                            yield {'done': True, 'chat_id': chat_id}
