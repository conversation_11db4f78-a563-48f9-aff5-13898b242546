from fastapi import FastAPI

from .auth import router as auth_router
from .books import router as books_router
from .chat import router as chat_router
from .companions import router as companions_router
from .preferences import router as preferences_router
from .suggestions import router as suggestions_router
from .ai_book import router as ai_book_router
from .admin import router as admin_router
from .contact import router as contact_router
from .book_covers import router as book_covers_router


def register_routers(app: FastAPI):
    """
    Include all routers in the FastAPI app
    """
    app.include_router(auth_router)
    app.include_router(books_router)
    app.include_router(chat_router)
    app.include_router(companions_router)
    app.include_router(preferences_router)
    app.include_router(suggestions_router)
    app.include_router(ai_book_router)
    app.include_router(admin_router)
    app.include_router(contact_router)
    app.include_router(book_covers_router)
