import { useCallback } from 'react';
import chatService from '../../../services/chatService';
import { prepareMessageForSending, formatResponseMessage } from '../utils/messageFormatters';

const useChatActions = (chatState) => {
  const {
    addMessage,
    setMessageHistory,
    setIsLoading,
    setError
  } = chatState;

  const sendMessage = useCallback(async (content, userId, bookId, character) => {
    if (!content.trim()) return;

    const message = prepareMessageForSending(content, userId, bookId);
    addMessage(message);
    setIsLoading(true);
    setError(null);

    try {
      console.log('Sending message:', JSON.stringify({ bookId, content, character }));
      // message is the object created by prepareMessageForSending
      // It should have properties: message, userId, bookId, characterId, chatId
      const reader = await chatService.sendMessage(message.bookId, message.message, message.characterId);
      let responseContent = '';
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        // Convert the Uint8Array to text
        const chunk = new TextDecoder().decode(value);
        try {
          const data = JSON.parse(chunk);
          if (data.content) {
            responseContent += data.content;
            const partialResponse = formatResponseMessage({
              content: responseContent,
              character: character
            }, bookId);
            addMessage(partialResponse);
          }
        } catch (e) {
          console.warn('Error parsing chunk:', e);
        }
      }

      // Final message update
      if (responseContent) {
        const finalResponse = formatResponseMessage({
          content: responseContent,
          character: character
        }, bookId);
        addMessage(finalResponse);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setError('Failed to send message. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [addMessage, setIsLoading, setError]);

  const loadChatHistory = useCallback(async (bookId, character) => {
    if (!bookId) return;
    
    setIsLoading(true);
    setError(null);

    try {
      const history = await chatService.getChatHistory(bookId, undefined, character);
      return history;
    } catch (error) {
      console.error('Error loading chat history:', error);
      setError('Failed to load chat history.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [setIsLoading, setError]);

  return {
    sendMessage,
    loadChatHistory
  };
};

export default useChatActions;
