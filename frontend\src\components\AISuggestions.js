import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  Paper,
  CircularProgress,
  Button,
  Alert,
  Fade,
  Grow,
  Snackbar,
  useTheme,
  CardMedia,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton
} from '@mui/material';
import { 
  Refresh as RefreshIcon, 
  CheckCircleOutline as CheckIcon, 
  Book as BookIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import suggestionsService from '../services/suggestionsService';
import bookService from '../services/bookService';

/**
 * AISuggestions component displays AI-generated book recommendations based on the user's library
 * and reading preferences. It integrates with the backend suggestions API and handles rate limiting.
 */
function AISuggestions({ userId, onRefreshRequest, onBookAdded }) {
  const theme = useTheme();
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [rateLimited, setRateLimited] = useState(false);
  const [addingBook, setAddingBook] = useState(null);
  const [fetchingNewSuggestion, setFetchingNewSuggestion] = useState(false);
  const [noSuggestionsMessage, setNoSuggestionsMessage] = useState('');
  const [bookCovers, setBookCovers] = useState({});
  const [coverLoadErrors, setCoverLoadErrors] = useState({});
  const [selectedSuggestion, setSelectedSuggestion] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  // Example suggestions for non-authenticated users
  const generateExampleSuggestions = () => {
    return [
      {
        id: 'suggest-1',
        title: 'Dune',
        author: 'Frank Herbert',
        description: 'Set on the desert planet Arrakis, Dune tells the story of Paul Atreides, whose family accepts stewardship of the planet that produces the "spice", a substance essential for interstellar travel.'
      },
      {
        id: 'suggest-2',
        title: 'The Alchemist',
        author: 'Paulo Coelho',
        description: 'A philosophical novel about a young Andalusian shepherd named Santiago and his journey to find a hidden treasure at the Egyptian pyramids.'
      },
      {
        id: 'suggest-3',
        title: 'The Silent Patient',
        author: 'Alex Michaelides',
        description: 'A psychological thriller about Alicia Berenson, a famous painter who has been silent since being accused of murdering her husband.'
      }
    ];
  };

  const fetchSuggestions = useCallback(async (forceRefresh = false) => {
    console.log('[AISuggestions] fetchSuggestions called. userId:', userId, 'forceRefresh:', forceRefresh);
    if (!userId) {
      console.log('[AISuggestions] fetchSuggestions aborted: no userId');
      return;
    }

    // For demo user, return example suggestions
    if (userId === 'example-user') {
      setLoading(true);
      // Simulate API delay
      setTimeout(() => {
        setSuggestions(generateExampleSuggestions());
        setLastUpdated(new Date().toLocaleTimeString());
        setLoading(false);
      }, 800);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setRateLimited(false);

      let data;
      if (forceRefresh) {
        // Use POST endpoint to force refresh suggestions
        console.log('[AISuggestions] Forcing refresh of suggestions');
        data = await suggestionsService.refreshSuggestions(userId);
      } else {
        // Use regular GET endpoint for normal fetching
        data = await suggestionsService.getSuggestions();
      }

      console.log('[AISuggestions] Fetched suggestions response:', data);
      const suggestionsArray = Array.isArray(data) ? data : (data.suggestions || []);
      setSuggestions(suggestionsArray);
      setLastUpdated(new Date().toLocaleTimeString());
      // Set noSuggestionsMessage if present and suggestions are empty
      if ((Array.isArray(data.suggestions) && data.suggestions.length === 0 && data.message) || (Array.isArray(data) && data.length === 0 && data.message)) {
        setNoSuggestionsMessage(data.message);
      } else {
        setNoSuggestionsMessage('');
      }
    } catch (error) {
      console.error('[AISuggestions] Error fetching suggestions:', error);
      if (error.response?.status === 429) {
        setRateLimited(true);
        setError('Rate limit reached. Please try again later.');
      } else if (error.message?.includes('No preferred companion set')) {
        setError('Please set a preferred companion in your preferences to get personalized book suggestions.');
      } else {
        setError(error.message || 'Failed to load suggestions');
      }
      setSuggestions([]);
      setNoSuggestionsMessage('');
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // Fetch book covers when suggestions change
  useEffect(() => {
    const fetchBookCovers = async () => {
      if (!suggestions || suggestions.length === 0) return;
      
      const newBookCovers = { ...bookCovers };
      const newCoverLoadErrors = { ...coverLoadErrors };
      
      // Only fetch covers for suggestions we don't already have
      const suggestionsToFetch = suggestions.filter(
        suggestion => !newBookCovers[suggestion.title] && !newCoverLoadErrors[suggestion.title]
      );
      
      await Promise.all(
        suggestionsToFetch.map(async suggestion => {
          try {
            // Try to get cover by title
            const coverData = await bookService.getBookCover(
              encodeURIComponent(suggestion.title),
              'title',
              'M'
            );

            if (coverData.success && coverData.cover_url) {
              newBookCovers[suggestion.title] = coverData.cover_url;
            } else {
              // Mark as error so we don't keep trying
              newCoverLoadErrors[suggestion.title] = true;
            }
          } catch (error) {
            console.error(`Error fetching cover for suggestion ${suggestion.title}:`, error);
            newCoverLoadErrors[suggestion.title] = true;
          }
        })
      );
      
      if (Object.keys(newBookCovers).length > Object.keys(bookCovers).length) {
        setBookCovers(newBookCovers);
      }
      
      if (Object.keys(newCoverLoadErrors).length > Object.keys(coverLoadErrors).length) {
        setCoverLoadErrors(newCoverLoadErrors);
      }
    };
    
    fetchBookCovers();
  }, [suggestions]);
  
  // Clean up object URLs when component unmounts
  useEffect(() => {
    return () => {
      // Revoke all object URLs to prevent memory leaks
      Object.values(bookCovers).forEach(url => {
        if (url && url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, []);

  useEffect(() => {
    console.log('[AISuggestions] useEffect triggered. userId:', userId);
    fetchSuggestions();
  }, [userId, fetchSuggestions]);

  useEffect(() => {
    if (onRefreshRequest) {
      onRefreshRequest(() => fetchSuggestions(true));
    }
  }, [onRefreshRequest, fetchSuggestions]);

  const handleManualRefresh = () => {
    fetchSuggestions(true);
  };

  // Additional example suggestions for replacement
  const additionalExampleSuggestions = [
    {
      id: 'suggest-extra-1',
      title: 'Sapiens: A Brief History of Humankind',
      author: 'Yuval Noah Harari',
      description: 'A survey of the history of humankind from the evolution of archaic human species in the Stone Age up to the twenty-first century.'
    },
    {
      id: 'suggest-extra-2',
      title: 'The Road',
      author: 'Cormac McCarthy',
      description: 'A post-apocalyptic novel following a father and his young son\'s journey across a desolate landscape a few years after an extinction event.'
    },
    {
      id: 'suggest-extra-3',
      title: 'The Night Circus',
      author: 'Erin Morgenstern',
      description: 'A fantasy novel about a mysterious circus that arrives without warning and is only open at night.'
    },
    {
      id: 'suggest-extra-4',
      title: 'The Power of Habit',
      author: 'Charles Duhigg',
      description: 'Explores the science behind habit creation and reformation, explaining how habits work and how they can be changed.'
    }
  ];

  /**
   * Fetches a single new suggestion to replace the one that was added
   */
  const fetchNewSuggestion = useCallback(async () => {
    if (!userId) return null;

    // For demo user, return a random example suggestion
    if (userId === 'example-user') {
      setFetchingNewSuggestion(true);
      return new Promise(resolve => {
        setTimeout(() => {
          setFetchingNewSuggestion(false);
          const randomIndex = Math.floor(Math.random() * additionalExampleSuggestions.length);
          resolve(additionalExampleSuggestions[randomIndex]);
        }, 500);
      });
    }

    try {
      setFetchingNewSuggestion(true);
      const data = await suggestionsService.getNewSuggestion();

      // The response from the /api/suggestions/single endpoint is the suggestion itself
      // not wrapped in a suggestion property
      console.log('[AISuggestions] Fetched new suggestion:', data);
      if (data && data.title) {
        return data;
      }
      return null;
    } catch (error) {
      console.error('Error fetching new suggestion:', error);
      return null;
    } finally {
      setFetchingNewSuggestion(false);
    }
  }, [userId]);

  const handleAddToLibrary = async (suggestion) => {
    if (!userId) {
      alert('Please select a user first before adding a book');
      return;
    }
    
    // For demo users, simulate adding a book
    if (userId === 'example-user') {
      setAddingBook(suggestion.title);
      setTimeout(() => {
        setSuccess(`This is a demo. Sign up to add "${suggestion.title}" to your library!`);
        setTimeout(() => setSuccess(null), 5000);
        
        // Get a new suggestion and update list
        setTimeout(async () => {
          setAddingBook(null);
          const newSuggestion = await fetchNewSuggestion();
          
          if (newSuggestion) {
            setSuggestions(prev => {
              const updatedSuggestions = prev.filter(s => s.title !== suggestion.title);
              const insertIndex = Math.floor(Math.random() * (updatedSuggestions.length + 1));
              updatedSuggestions.splice(insertIndex, 0, newSuggestion);
              return updatedSuggestions;
            });
          } else {
            setSuggestions(prev => prev.filter(s => s.title !== suggestion.title));
            if (suggestions.length <= 2) {
              setTimeout(() => fetchSuggestions(true), 500);
            }
          }
        }, 800);
      }, 1000);
      return;
    }
    
    try {
      // Set the current book as being added for animation
      setAddingBook(suggestion.title);

      const response = await bookService.addBook({
        title: suggestion.title,
        author: suggestion.author || 'Unknown',
        description: suggestion.description,
        user_id: userId,
        use_ai_suggestions: true
      });

      // Check if the book already existed in the library
      if (response.already_exists) {
        setError(`"${suggestion.title}" is already in your library.`);
        setTimeout(() => setError(null), 3000);

        // Remove the suggestion from the list
        setSuggestions(prev => prev.filter(s => s.title !== suggestion.title));

        // Trigger a refresh of suggestions after a short delay
        setTimeout(() => fetchSuggestions(true), 3000);
        return;
      }

      // Check if we got AI changes that need a second request
      if (response.ai_changes) {
        // Use the AI-corrected data to add the book
        // Validate to ensure "No match found" isn't used
        let suggestedTitle = response.ai_changes.corrected_title;
        let suggestedAuthor = response.ai_changes.generated_author;
        
        // If AI returned "No match found", use the original suggestion instead
        if (suggestedTitle === "No match found") {
          suggestedTitle = suggestion.title;
        }
        
        if (suggestedAuthor === "No match found") {
          suggestedAuthor = suggestion.author || 'Unknown';
        }
        
        const finalData = {
          title: suggestedTitle || suggestion.title,
          author: suggestedAuthor || suggestion.author || 'Unknown',
          description: suggestion.description,
          user_id: userId,
          use_ai_suggestions: false // Skip AI check on second request
        };

        // Make the actual book addition request
        const finalResponse = await bookService.addBook(finalData);

        // Check if the book already existed in the library after AI correction
        if (finalResponse.already_exists) {
          setError(`"${finalResponse.title}" is already in your library.`);
          setTimeout(() => setError(null), 3000);
        } else {
          console.log('Book added successfully:', finalResponse);

          // Show success message
          setSuccess(`Book added successfully! ${
            response.ai_changes.corrected_title !== suggestion.title ?
            `\nTitle corrected to: ${response.ai_changes.corrected_title}` : ''
          }${
            response.ai_changes.generated_author !== suggestion.author ?
            `\nAuthor updated to: ${response.ai_changes.generated_author}` : ''
          }`);
        }

        // Notify parent component about book addition
        if (onBookAdded) {
          onBookAdded(finalResponse);
        }
      } else {
        // Book was added directly
        console.log('Book added successfully:', response);
        setSuccess('Book added successfully!');

        // Notify parent component about book addition
        if (onBookAdded) {
          onBookAdded(response);
        }
      }

      // Clear error state if there was any
      setError(null);

      // Set timers to clear states
      setTimeout(() => setSuccess(null), 5000); // Clear success message after 5 seconds

      // Remove the suggestion from the list with a slight delay for animation
      setTimeout(async () => {
        setAddingBook(null);

        // Try to get a new suggestion to replace the one that was added
        const newSuggestion = await fetchNewSuggestion();

        if (newSuggestion) {
          // Replace the old suggestion with the new one
          setSuggestions(prev => {
            const updatedSuggestions = prev.filter(s => s.title !== suggestion.title);
            // Add the new suggestion at a random position to make it feel more natural
            const insertIndex = Math.floor(Math.random() * (updatedSuggestions.length + 1));
            updatedSuggestions.splice(insertIndex, 0, newSuggestion);
            return updatedSuggestions;
          });
        } else {
          // If we couldn't get a new suggestion, just remove the old one
          setSuggestions(prev => prev.filter(s => s.title !== suggestion.title));

          // If we have less than 2 suggestions left, refresh the full list
          if (suggestions.length <= 2) {
            setTimeout(() => fetchSuggestions(true), 500);
          }
        }
      }, 800);
    } catch (error) {
      console.error('Error adding book:', error);
      setError(error.message || 'Failed to add book to library');
      setAddingBook(null); // Clear the adding state
      setTimeout(() => setError(null), 3000);
    }
  };

  // Handle opening the dialog with a specific suggestion
  const handleOpenDialog = (suggestion) => {
    setSelectedSuggestion(suggestion);
    setDialogOpen(true);
  };

  // Handle closing the dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  // Add to library from the dialog
  const handleAddFromDialog = () => {
    if (selectedSuggestion) {
      handleAddToLibrary(selectedSuggestion);
      setDialogOpen(false);
    }
  };

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert
          severity="error"
          action={
            <Button
              color="inherit"
              size="small"
              onClick={handleManualRefresh}
              disabled={rateLimited}
            >
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Paper
      elevation={2}
      sx={{
        p: 2,
        minWidth: 340,
        maxWidth: 420,
        width: '100%',
        height: '100%',
        bgcolor: theme.palette.background.paper,
        borderRadius: theme.shape.borderRadius,
        position: 'relative',
        mx: 'auto'
      }}
    >
      {loading ? (
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: 200
        }}>
          <CircularProgress />
        </Box>
      ) : suggestions.length > 0 ? (
        <Fade in={true}>
          <List>
            {suggestions.slice(0, 3).map((suggestion, index) => (
              <Grow
                key={suggestion.id || index}
                in={true}
                style={{ transformOrigin: '0 0 0' }}
                {...(addingBook === suggestion.title ? { timeout: 800 } : {})}
              >
                <ListItem
                  sx={{
                    mb: 2,
                    bgcolor: theme.palette.background.default,
                    borderRadius: 1,
                    transition: theme.transitions.create(['background-color', 'transform', 'opacity']),
                    display: 'block',
                    p: 2,
                    '&:hover': {
                      bgcolor: theme.palette.action.hover,
                      cursor: 'pointer'
                    },
                    ...(addingBook === suggestion.title && {
                      opacity: 0,
                      transform: 'scale(0.8) translateY(-20px)',
                    })
                  }}
                  onClick={() => handleOpenDialog(suggestion)}
                >
                  <Grid container spacing={2}>
                    <Grid item xs={4} sm={3}>
                      {bookCovers[suggestion.title] ? (
                        <Box
                          sx={{
                            width: '100%',
                            height: '120px',
                            borderRadius: '8px',
                            overflow: 'hidden',
                            boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                          }}
                        >
                          <CardMedia
                            component="img"
                            image={bookCovers[suggestion.title]}
                            alt={suggestion.title}
                            sx={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                              borderRadius: '8px',
                            }}
                            onError={() => {
                              setCoverLoadErrors(prev => ({ ...prev, [suggestion.title]: true }));
                            }}
                          />
                        </Box>
                      ) : (
                        <Box
                          sx={{
                            width: '100%',
                            height: '120px',
                            borderRadius: '8px',
                            bgcolor: 'primary.light',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                          }}
                        >
                          <BookIcon sx={{ fontSize: 36, color: 'primary.dark' }} />
                        </Box>
                      )}
                    </Grid>
                    <Grid item xs={8} sm={9}>
                      <Typography variant="h6" component="h3">
                        {suggestion.title}
                      </Typography>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        {suggestion.author || 'Unknown Author'}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="textSecondary"
                        sx={{
                          mt: 0.5,
                          fontSize: '0.875rem',
                          lineHeight: 1.43,
                          mb: 2,
                          display: '-webkit-box',
                          WebkitLineClamp: 3,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis'
                        }}
                      >
                        {suggestion.description}
                      </Typography>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAddToLibrary(suggestion);
                        }}
                        disabled={addingBook === suggestion.title}
                        size="small"
                      >
                        {addingBook === suggestion.title ? 'Adding...' : 'Add to Library'}
                      </Button>
                    </Grid>
                  </Grid>
                </ListItem>
              </Grow>
            ))}
          </List>
        </Fade>
      ) : (
        <Box sx={{
          textAlign: 'center',
          py: 4,
          minHeight: 200,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Typography color="textSecondary">
            {noSuggestionsMessage || 'No suggestions available at the moment'}
          </Typography>
        </Box>
      )}

      {lastUpdated && (
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{
            display: 'block',
            textAlign: 'right',
            mt: 2,
            fontSize: '0.75rem'
          }}
        >
          Last updated: {lastUpdated}
        </Typography>
      )}

      {/* Success Snackbar */}
      <Snackbar
        open={!!success}
        autoHideDuration={5000}
        onClose={() => setSuccess(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          severity="success"
          icon={<CheckIcon fontSize="inherit" />}
          onClose={() => setSuccess(null)}
          sx={{ width: '100%' }}
        >
          {success}
        </Alert>
      </Snackbar>

      {/* Suggestion Detail Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            p: 1
          }
        }}
      >
        {selectedSuggestion && (
          <>
            <DialogContent>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={4} sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                  {bookCovers[selectedSuggestion.title] ? (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        maxWidth: '180px',
                        maxHeight: '270px',
                        borderRadius: '8px',
                        overflow: 'hidden',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                        p: 0,
                        m: 0
                      }}
                    >
                      <CardMedia
                        component="img"
                        image={bookCovers[selectedSuggestion.title]}
                        alt={selectedSuggestion.title}
                        sx={{
                          width: '100%',
                          height: 'auto',
                          maxWidth: '180px',
                          maxHeight: '270px',
                          objectFit: 'contain',
                          borderRadius: '8px',
                        }}
                      />
                    </Box>
                  ) : (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        maxWidth: '180px',
                        maxHeight: '270px',
                        borderRadius: '8px',
                        bgcolor: 'primary.light',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                      }}
                    >
                      <BookIcon sx={{ fontSize: 64, color: 'primary.dark' }} />
                    </Box>
                  )}
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Box sx={{ position: 'relative', pr: 4 }}>
                    <Typography variant="h5" gutterBottom>
                      {selectedSuggestion.title}
                      <IconButton
                        aria-label="close"
                        onClick={handleCloseDialog}
                        sx={{
                          position: 'absolute',
                          right: -8,
                          top: -8,
                          color: (theme) => theme.palette.grey[500],
                        }}
                      >
                        <CloseIcon />
                      </IconButton>
                    </Typography>
                    <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                      By {selectedSuggestion.author || 'Unknown Author'}
                    </Typography>
                    <Typography variant="body1" paragraph sx={{ mt: 1 }}>
                      {selectedSuggestion.description}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions sx={{ px: 3, py: 2 }}>
              <Button onClick={handleCloseDialog} color="inherit">
                Close
              </Button>
              <Button 
                onClick={handleAddFromDialog} 
                variant="contained" 
                color="primary"
                disabled={addingBook === selectedSuggestion.title}
              >
                {addingBook === selectedSuggestion.title ? 'Adding...' : 'Add to Library'}
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Paper>
  );
}

AISuggestions.propTypes = {
  userId: PropTypes.string.isRequired,
  onRefreshRequest: PropTypes.func,
  onBookAdded: PropTypes.func
};

export default AISuggestions;
