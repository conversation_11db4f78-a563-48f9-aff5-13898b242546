import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Box,
  CircularProgress,
  Button
} from '@mui/material';

function CharacterSelection({ onSelectCharacter, selectedCharacter, companions = [] }) {
  const handleCharacterSelect = React.useCallback((companion) => {
    onSelectCharacter(companion);
  }, [onSelectCharacter]);

  // Debug companions data
  React.useEffect(() => {
    console.log('CharacterSelection received companions:', companions);
  }, [companions]);

  if (!companions || !companions.length) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <Typography>No companions available.</Typography>
      </Box>
    );
  }

  return (
    <Grid container spacing={3}>
      {companions.map((companion, idx) => (
        <Grid item xs={12} sm={6} md={4} key={companion.id || idx}>
          <Card
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              cursor: 'pointer',
              transition: 'transform 0.2s',
              '&:hover': {
                transform: 'scale(1.02)',
              },
              border: selectedCharacter?.id === companion.id ? '2px solid #C4A68A' : 'none',
            }}
            onClick={() => handleCharacterSelect(companion)}
          >
            <CardMedia
              component="img"
              height="200"
              image={companion.avatar || `/avatars/${companion.name}.png`}
              alt={companion.name}
              sx={{ objectFit: 'cover' }}
            />
            <CardContent>
              <Typography gutterBottom variant="h5" component="div" sx={{ textAlign: 'center' }}>
                {companion.name}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {companion.description}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  mt: 1,
                  fontStyle: 'italic',
                  color: 'text.secondary',
                }}
              >
                Personality: {companion.personality}
              </Typography>
              {companion.warning && (
                <Typography
                  variant="body2"
                  sx={{
                    mt: 2,
                    color: 'warning.dark',
                    p: 1,
                    borderRadius: 1,
                    fontWeight: 'medium'
                  }}
                >
                  {companion.warning}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
}

export default CharacterSelection;
