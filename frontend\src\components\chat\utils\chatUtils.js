// Debounce utility function
export const debounce = (func, wait) => {
  let timeout;
  return (...args) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

export const scrollToBottom = (containerRef) => {
  if (containerRef.current) {
    containerRef.current.scrollTop = containerRef.current.scrollHeight;
  }
};

export const formatTimestamp = (timestamp) => {
  if (!timestamp) {
    console.warn('Empty timestamp received');
    return '';
  }
  
  try {
    console.log('Formatting timestamp:', timestamp);
    const date = new Date(timestamp);
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
      console.error('Invalid timestamp:', timestamp);
      return '';
    }
    
    return date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('Error formatting timestamp:', error, 'Timestamp was:', timestamp);
    return '';
  }
};

// Calculate width for select components based on text length
export const calculateSelectWidth = (text) => {
  const minWidth = 400;
  const maxWidth = 800;
  const charWidth = 11;
  const padding = 5;
  const calculatedWidth = Math.min(maxWidth, Math.max(minWidth, (text.length * charWidth) + padding));
  return `${calculatedWidth}px`;
};
