import { API_BASE_URL } from '../config';
import RateLimiter, { rateLimitConfig } from '../utils/rateLimiter';
import api from './axiosConfig';

const companionRateLimiter = new RateLimiter(rateLimitConfig.companions.fetch);

const companionsService = {
  // Get companion characters
  getCompanions: async () => {
    await companionRateLimiter.throttle();
    try {
      const response = await api.get('/api/companions');
      if (!response.data) {
        console.error('Empty response when fetching companions');
        return { companions: [] };
      }
      return response.data;
    } catch (error) {
      console.error('Error fetching companions:', error);
      return { companions: [] };
    }
  },

  // Get a specific companion by ID
  getCompanion: async (companionId) => {
    await companionRateLimiter.throttle();
    try {
      const response = await api.get(`/api/companions/${companionId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching companion ${companionId}:`, error);
      return null;
    }
  },

  // Get character by ID
  getCharacter: async (characterId) => {
    try {
      const response = await api.get(`/api/companions/character/${characterId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching character ${characterId}:`, error);
      return null;
    }
  }
};

export default companionsService;