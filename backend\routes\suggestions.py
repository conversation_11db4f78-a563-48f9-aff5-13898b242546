from fastapi import APIRouter, Depends, Request, HTTPException, status
from backend.database.db import db
from backend.utils.decorators import get_current_user, get_optional_user
from backend.ai.book_services import BookServices
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/suggestions", tags=["suggestions"])

# Initialize book services
book_services = BookServices()

def generate_book_suggestions(user_id, count=5, force_refresh=False):
    """Generate book suggestions using the BookServices class"""
    try:
        # Get user's preferred character
        user_pref = db.get_user_preference(user_id)
        if user_pref:
            character_id = user_pref.preferred_companion
            logger.info(f"[Suggestions] User {user_id} preferred companion: {character_id}")
        else:
            character_id = 1
            logger.info(f"[Suggestions] User {user_id} has no preferred companion, defaulting to {character_id}")

        # Get book history
        book_history = db.get_books(user_id)
        logger.info(f"[Suggestions] User {user_id} book history count: {len(book_history) if book_history else 0}")
        
        # Log some book titles for debugging
        if book_history:
            book_titles = [book.get('title', 'Unknown') if isinstance(book, dict) else getattr(book, 'title', 'Unknown') for book in book_history[:3]]
            logger.info(f"[Suggestions] Sample book titles for user {user_id}: {book_titles}")

        # Generate suggestions
        suggestions = book_services.generate_book_suggestions(user_id, character_id, book_history, count)
        logger.info(f"[Suggestions] Generated {len(suggestions) if suggestions else 0} suggestions for user {user_id}")

        # Save suggestions to DB
        if suggestions:
            # Add character information to suggestions before saving
            for suggestion in suggestions:
                suggestion['character'] = character_id
            
            # If force_refresh is True, we want to replace existing suggestions
            # Otherwise, we just save them normally
            if force_refresh:
                # First delete existing suggestions
                db.delete_book_suggestions(user_id)
                logger.info(f"[Suggestions] Deleted existing suggestions for user {user_id} for force refresh")

            db.save_book_suggestions(user_id, suggestions)

        return suggestions
    except Exception as e:
        logger.error(f"[Suggestions] Error in generate_book_suggestions for user {user_id}: {e}")
        return None

# Example suggestions for non-authenticated users
EXAMPLE_SUGGESTIONS = [
    {
        "id": "example-1",
        "title": "Dune",
        "author": "Frank Herbert",
        "description": "Set on the desert planet Arrakis, Dune tells the story of Paul Atreides, whose family accepts stewardship of the planet that produces the 'spice', a substance essential for interstellar travel."
    },
    {
        "id": "example-2",
        "title": "The Alchemist",
        "author": "Paulo Coelho",
        "description": "A philosophical novel about a young Andalusian shepherd named Santiago and his journey to find a hidden treasure at the Egyptian pyramids."
    },
    {
        "id": "example-3",
        "title": "The Silent Patient",
        "author": "Alex Michaelides",
        "description": "A psychological thriller about Alicia Berenson, a famous painter who has been silent since being accused of murdering her husband."
    },
    {
        "id": "example-4",
        "title": "Sapiens: A Brief History of Humankind",
        "author": "Yuval Noah Harari",
        "description": "A survey of the history of humankind from the evolution of archaic human species in the Stone Age up to the twenty-first century."
    },
    {
        "id": "example-5",
        "title": "The Night Circus",
        "author": "Erin Morgenstern",
        "description": "A fantasy novel about a mysterious circus that arrives without warning and is only open at night."
    }
]

@router.get('/', status_code=status.HTTP_200_OK)
async def get_book_suggestions(force_refresh: bool = False, user=Depends(get_optional_user)):
    # For non-authenticated users, return example suggestions
    if not user:
        return EXAMPLE_SUGGESTIONS
        
    try:
        user_id = user.id

        if not force_refresh:
            saved_suggestions = db.get_book_suggestions(user_id)
            if saved_suggestions:
                return saved_suggestions

        suggestions = generate_book_suggestions(user_id, force_refresh=force_refresh)
        if suggestions:
            return suggestions
        else:
            # Check if user actually has books - if they do, it's a different issue
            user_books = db.get_books(user_id)
            if user_books and len(user_books) > 0:
                logger.warning(f"User {user_id} has {len(user_books)} books but no suggestions were generated")
                return {"suggestions": [], "message": "Unable to generate suggestions at the moment. Please try again later."}
            else:
                return {"suggestions": [], "message": "You need to add more books to your library for suggestions to work."}
    except Exception as e:
        logger.error(f"Error generating suggestions: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred")

# Additional example suggestions for replacement when non-authenticated users "add" books
ADDITIONAL_EXAMPLE_SUGGESTIONS = [
    {
        "id": "extra-1",
        "title": "The Road",
        "author": "Cormac McCarthy",
        "description": "A post-apocalyptic novel following a father and his young son's journey across a desolate landscape after an extinction event."
    },
    {
        "id": "extra-2",
        "title": "Educated",
        "author": "Tara Westover",
        "description": "A memoir about a woman who grows up in a survivalist family in Idaho and eventually earns a PhD from Cambridge University."
    },
    {
        "id": "extra-3",
        "title": "The Power of Habit",
        "author": "Charles Duhigg",
        "description": "Explores the science behind habit creation and reformation, explaining how habits work and how they can be changed."
    },
    {
        "id": "extra-4",
        "title": "Where the Crawdads Sing",
        "author": "Delia Owens",
        "description": "A novel about a young woman who grows up isolated in the marshes of North Carolina and later becomes the prime suspect in a murder case."
    }
]

@router.get('/single', status_code=status.HTTP_200_OK)
async def get_single_suggestion(request: Request, user=Depends(get_optional_user)):
    """Get a single book suggestion to replace one that was added to the library"""
    # For non-authenticated users, return random example suggestion
    if not user:
        import random
        return random.choice(ADDITIONAL_EXAMPLE_SUGGESTIONS)
        
    try:
        user_id = user.id
        logger.info(f"Single suggestion requested for user {user_id}")

        suggestions = generate_book_suggestions(user_id, count=1, force_refresh=True)
        if suggestions and len(suggestions) > 0:
            return suggestions[0]
        else:
            raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to generate suggestion")
    except Exception as e:
        logger.error(f"Error in get_single_suggestion: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred")

@router.post('/', status_code=status.HTTP_200_OK)
async def request_book_suggestions(request: Request, user=Depends(get_optional_user)):
    # For non-authenticated users, shuffle example suggestions
    if not user:
        import random
        shuffled_suggestions = EXAMPLE_SUGGESTIONS.copy()
        random.shuffle(shuffled_suggestions)
        return shuffled_suggestions
        
    try:
        user_id = user.id

        suggestions = generate_book_suggestions(user_id, force_refresh=True)
        if suggestions:
            return suggestions
        else:
            # Check if user actually has books - if they do, it's a different issue
            user_books = db.get_books(user_id)
            if user_books and len(user_books) > 0:
                logger.warning(f"User {user_id} has {len(user_books)} books but no suggestions were generated")
                return {"suggestions": [], "message": "Unable to generate suggestions at the moment. Please try again later."}
            else:
                return {"suggestions": [], "message": "You need to add more books to your library for suggestions to work."}
    except Exception as e:
        logger.error(f"Error in request_book_suggestions: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred")
