from backend.app import app
from uvicorn import run as uvicorn_run
import logging
import os
import subprocess

def run_migrations():
    """Run database migrations"""
    try:
        subprocess.run(["alembic", "upgrade", "head"], check=True)
        logging.info("Database migrations completed successfully")
    except subprocess.CalledProcessError as e:
        logging.error(f"Migration failed: {e}")
        raise

if __name__ == '__main__':
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    logging.getLogger('werkzeug').setLevel(logging.INFO)
    
    # Run migrations first
    run_migrations()
    
    # Get port from environment variable (<PERSON><PERSON> sets this)
    port = int(os.environ.get('PORT', 8000))
    
    # Use APP_ENV instead of FLASK_ENV for environment
    reload = os.environ.get('APP_ENV', 'development') == 'development'
    
    # Run the FastAPI app with Uvicorn
    uvicorn_run("backend.app:app", host="0.0.0.0", port=port, reload=reload)