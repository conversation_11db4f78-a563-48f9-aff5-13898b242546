from functools import wraps
import logging
from backend.database.supabase_client import supabase
from backend.database.db import db, User
from sqlalchemy import func
from fastapi import Request, HTTPException, status, Depends

logger = logging.getLogger(__name__)

# Legacy Flask decorators removed (supabase_auth_required, admin_required)
# Use FastAPI dependencies instead.

async def get_current_user(request: Request):
    """FastAPI dependency to get and authenticate the current user via Supabase JWT."""
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Authorization header missing or invalid")
    token = auth_header.split('Bearer ')[1]
    try:
        supabase_user = supabase.get_user_from_token(token)
        if not supabase_user:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid or expired token")
    except Exception as e:
        logger.error(f"Auth dependency: error validating token: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Authentication failed")
    try:
        session = request.state.db
        local_user = session.query(User).filter(
            (User.supabase_id == supabase_user.id) |
            (func.lower(User.email) == supabase_user.email.lower())
        ).first()
        if not local_user:
            username = (supabase_user.user_metadata or {}).get('username', supabase_user.email.split('@')[0]).lower()
            user_id = db.add_user(username, supabase_user.email.lower(), session=session)
            local_user = session.query(User).filter_by(id=user_id).first()
            local_user.supabase_id = supabase_user.id
        elif not local_user.supabase_id:
            local_user.supabase_id = supabase_user.id
        session.commit()
    except Exception as db_error:
        session.rollback()
        logger.error(f"Auth dependency DB error: {db_error}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error during user mapping")
    return local_user

async def get_optional_user(request: Request):
    """FastAPI dependency to get the current user if authenticated, or None if not."""
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return None
    
    token = auth_header.split('Bearer ')[1]
    try:
        supabase_user = supabase.get_user_from_token(token)
        if not supabase_user:
            return None
    except Exception as e:
        logger.debug(f"Optional auth: error validating token: {e}")
        return None
    
    try:
        session = request.state.db
        local_user = session.query(User).filter(
            (User.supabase_id == supabase_user.id) |
            (func.lower(User.email) == supabase_user.email.lower())
        ).first()
        if not local_user:
            username = (supabase_user.user_metadata or {}).get('username', supabase_user.email.split('@')[0]).lower()
            user_id = db.add_user(username, supabase_user.email.lower(), session=session)
            local_user = session.query(User).filter_by(id=user_id).first()
            local_user.supabase_id = supabase_user.id
        elif not local_user.supabase_id:
            local_user.supabase_id = supabase_user.id
        session.commit()
        return local_user
    except Exception as db_error:
        session.rollback()
        logger.debug(f"Optional auth DB error: {db_error}")
        return None