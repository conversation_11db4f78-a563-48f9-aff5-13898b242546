"""Character classes for the BookWorm AI chat system."""
from typing import Dict, Optional, List, Any
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG)

class Character:
    """Base class for all BookWorm companion characters."""
    
    def __init__(self, id: int, name: str, prompt: str, gender: str = None, 
                 title: str = None, personality: str = None, 
                 interests: List[str] = None, tone: str = None,
                 voice: str = None, description: str = None,
                 warning: str = None):
        """Initialize a character with its attributes.
        
        Args:
            id: Unique identifier for the character
            name: Character's name
            prompt: The full system prompt for the character
            gender: Character's gender
            title: Character's title/role
            personality: Brief description of personality
            interests: List of interests
            tone: Character's tone of voice
            voice: Voice model to use for TTS
            description: Short description of the character
            warning: Optional warning about character content
        """
        self.id = id
        self.name = name
        self.prompt = prompt
        self.gender = gender
        self.title = title
        self.personality = personality
        self.interests = interests or []
        self.tone = tone
        self.voice = voice
        self.description = description
        self.warning = warning
    
    def get_prompt(self) -> str:
        """Get the character's prompt."""
        return self.prompt
    
    def get_info(self) -> Dict[str, Any]:
        """Get character information as a dictionary."""
        return {
            'id': self.id,
            'name': self.name,
            'gender': self.gender,
            'title': self.title,
            'personality': self.personality,
            'interests': self.interests,
            'tone': self.tone,
            'voice': self.voice,
            'description': self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Character':
        """Create a Character instance from a dictionary."""
        return cls(
            id=data.get('id'),
            name=data.get('name'),
            prompt=data.get('prompt', ''),
            gender=data.get('gender'),
            title=data.get('title'),
            personality=data.get('personality'),
            interests=data.get('interests', []),
            tone=data.get('tone'),
            voice=data.get('voice'),
            description=data.get('description'),
            warning=data.get('warning')
        )


# Import character prompts
from backend.ai.character_prompts import (
    LILY_PROMPT, MAX_PROMPT, SOPHIA_PROMPT, ETHAN_PROMPT,
    MAYA_PROMPT, LEO_PROMPT, AVA_PROMPT, VIKTOR_PROMPT
)

# Create character instances
CHAR_LILY = Character(
    id=0,  # Assuming Lily doesn't have an ID in AVAILABLE_COMPANIONS
    name="Lily",
    prompt=LILY_PROMPT,
    gender="Female",
    title="The Friendly Bibliophile",
    personality="Friendly, nurturing, and empathetic",
    interests=["Classic literature", "Cozy mysteries", "Heartfelt novels"],
    tone="Warm, inviting, and empathetic",
    voice="nova",
    description="A warm and friendly bibliophile who helps others find the perfect book."
)

CHAR_MAX = Character(
    id=2,
    name="Max",
    prompt=MAX_PROMPT,
    gender="Male",
    title="The Chill Philosopher",
    personality="Relaxed, reflective, and open-minded",
    interests=["Philosophy", "Science fiction", "Contemplative non-fiction"],
    tone="Relaxed, reflective, and open-minded",
    voice="echo",
    description="A laid-back philosopher who loves diving deep into life's big questions through literature."
)

CHAR_SOPHIA = Character(
    id=0,  # Assuming Sophia doesn't have an ID in AVAILABLE_COMPANIONS
    name="Sophia",
    prompt=SOPHIA_PROMPT,
    gender="Female",
    title="The Passionate Storyteller",
    personality="Energetic, passionate, and engaging",
    interests=["Adventure tales", "Fantasy epics", "Thrilling mysteries"],
    tone="Upbeat, lively, and engaging",
    voice="nova",
    description="An energetic storyteller who brings books to life through lively discussions."
)

CHAR_ETHAN = Character(
    id=0,  # Assuming Ethan doesn't have an ID in AVAILABLE_COMPANIONS
    name="Ethan",
    prompt=ETHAN_PROMPT,
    gender="Male",
    title="The Analytical Thinker",
    personality="Thoughtful, analytical, and precise",
    interests=["Psychological thrillers", "Detective novels", "Complex narratives"],
    tone="Thoughtful, analytical, and precise",
    voice="echo",
    description="A logical thinker who enjoys dissecting complex narratives and themes."
)

CHAR_MAYA = Character(
    id=3,
    name="Maya",
    prompt=MAYA_PROMPT,
    gender="Female",
    title="The Artistic Dreamer",
    personality="Expressive, whimsical, and soothing",
    interests=["Poetry", "Magical realism", "Art-inspired literature"],
    tone="Expressive, whimsical, and soothing",
    voice="nova",
    description="A creative soul who sees the magic in poetry and artistic expression."
)

CHAR_LEO = Character(
    id=0,  # Assuming Leo doesn't have an ID in AVAILABLE_COMPANIONS
    name="Leo",
    prompt=LEO_PROMPT,
    gender="Male",
    title="The Historical Guide",
    personality="Approachable, conversational, and engaging",
    interests=["Historical fiction", "Biographies", "History books"],
    tone="Knowledgeable, conversational, and engaging",
    voice="echo",
    description="A friendly historian who connects past events to present-day contexts."
)

CHAR_AVA = Character(
    id=1,
    name="Ava",
    prompt=AVA_PROMPT,
    gender="Female",
    title="The Cozy Companion",
    personality="Gentle, nurturing, and reassuring",
    interests=["Heartfelt dramas", "Family sagas", "Inspirational stories"],
    tone="Gentle, nurturing, and reassuring",
    voice="nova",
    description="A warm and nurturing companion who loves discussing heartfelt stories and family sagas."
)

CHAR_VIKTOR = Character(
    id=4,
    name="Viktor",
    prompt=VIKTOR_PROMPT,
    gender="Male",
    title="The Cynical Critic",
    personality="Caustic, unapologetically elitist, and intellectually combative",
    interests=["Complex literature", "Savage criticism", "Destroying poorly-formed arguments"],
    tone="Sardonic, confrontational, and condescending",
    voice="onyx",
    description="A sharp-tongued critic who challenges readers to think deeper and defend their interpretations.",
    warning="Warning: Viktor uses explicit language and provides brutally honest feedback. His critiques can be harsh and unfiltered."
)

# Dictionary mapping character IDs to character instances
CHARACTERS_BY_ID = {
    1: CHAR_AVA,
    2: CHAR_MAX,
    3: CHAR_MAYA,
    4: CHAR_VIKTOR,
}

# Dictionary mapping character names (lowercase) to character instances
CHARACTERS_BY_NAME = {
    'lily': CHAR_LILY,
    'max': CHAR_MAX,
    'sophia': CHAR_SOPHIA,
    'ethan': CHAR_ETHAN,
    'maya': CHAR_MAYA,
    'leo': CHAR_LEO,
    'ava': CHAR_AVA,
    'viktor': CHAR_VIKTOR,
}

def get_character(identifier) -> Optional[Character]:
    """Get a character by ID or name.
    
    Args:
        identifier: Either an integer ID or string name
        
    Returns:
        Character instance or None if not found
    """
    logging.debug(f"get_character called with identifier: {identifier}, type: {type(identifier)}")
    if isinstance(identifier, int) or (isinstance(identifier, str) and identifier.isdigit()):
        # Try to get by ID
        char_id = int(identifier)
        logging.debug(f"Looking up character by ID: {char_id}, available IDs: {list(CHARACTERS_BY_ID.keys())}")
        return CHARACTERS_BY_ID.get(char_id)
    elif isinstance(identifier, str):
        # Try to get by name (case-insensitive)
        logging.debug(f"Looking up character by name: {identifier.lower()}, available names: {list(CHARACTERS_BY_NAME.keys())}")
        return CHARACTERS_BY_NAME.get(identifier.lower())
    return None

def get_character_by_id(character_id):
    """Get character info by ID.
    
    Args:
        character_id: The character ID
        
    Returns:
        Character info dictionary or None if not found
    """
    character = get_character(character_id)
    if character:
        return character.get_info()
    return None

def get_companion_choices():
    """Get all available companion choices.
    
    Returns:
        List of character info dictionaries
    """
    return [char.get_info() for char in CHARACTERS_BY_ID.values()]
