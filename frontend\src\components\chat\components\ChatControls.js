import React from 'react';
import { Box } from '@mui/material';
import ChatInput from './ChatInput';

const ChatControls = React.memo(({
  onSendMessage = () => {
    console.warn('onSendMessage prop not provided to ChatControls');
  },
  disabled
}) => {
  return (
    <Box
      sx={{
        borderTop: '1px solid',
        borderColor: 'divider',
        p: 2,
        display: 'flex',
        flexDirection: 'column',
        gap: 1
      }}
    >
      <ChatInput
        onSend={onSendMessage}
        disabled={disabled}
      />
    </Box>
  );
});

ChatControls.defaultProps = {
  onSendMessage: () => {
    console.warn('onSendMessage prop not provided to ChatControls');
  }
};

export default ChatControls;
