from fastapi import APIRouter, Depends, Request, HTTPException, status
from backend.utils.decorators import get_current_user
from backend.database.db import db
from sqlalchemy.exc import SQLAlchemyError
import logging
from . import logger, chat_service
from .helpers import format_chat_messages

router = APIRouter()

@router.post('/book-switch/{book_id}', status_code=status.HTTP_200_OK)
async def switch_book(book_id: int, request: Request, user=Depends(get_current_user)):
    """Handle book switching with all required data in a single request"""
    try:
        session, should_close = db._get_session_for_operation()

        try:
            # Get book data
            book = db.get_book(book_id, session=session)
            if not book:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Book not found")

            # Get user preferences
            user_pref = db.get_user_preference(user.id, session=session)
            companion = user_pref.preferred_companion if user_pref else 'DEFAULT'

            # Get chat history
            total_count, chat_history = db.get_chat_history_paginated(
                user_id=user.id,
                book_id=book_id,
                offset=0,
                limit=50,
                session=session
            )

            # Update current book
            db.set_current_book(user.id, book_id, session=session)

            # Format response
            messages = format_chat_messages(chat_history)

            session.commit()

            return {
                'book': book,
                'companion': companion,
                'chat_history': messages
            }

        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"Database error in switch_book: {str(e)}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Database error: {str(e)}")
        except Exception as e:
            session.rollback()
            logger.error(f"Unexpected error in switch_book: {str(e)}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"An unexpected error occurred: {str(e)}")
        finally:
            if should_close:
                session.close()

    except Exception as e:
        logger.error(f"Error in switch_book endpoint: {str(e)}")
        logger.exception("Exception details:")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.delete('/clear', status_code=status.HTTP_200_OK)
async def clear_chat(request: Request, user=Depends(get_current_user)):
    """Clear chat history"""
    book_id = request.query_params.get('book_id')
    chat_id = request.query_params.get('chat_id')

    try:
        # Clear chat history from database
        db.clear_chat_history(user.id, book_id, chat_id)

        # Clear chat history from AI service if needed
        if chat_id:
            chat_service.clear_chat_history(user.id, book_id, chat_id)

        return {'status': 'success'}
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
