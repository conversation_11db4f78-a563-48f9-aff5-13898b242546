import { useState, useRef } from 'react';

/**
 * Custom hook for managing AI suggestions
 */
const useSuggestions = () => {
  const [refreshingSuggestions, setRefreshingSuggestions] = useState(false);
  const refreshSuggestionsRef = useRef(null);

  // Handle refresh suggestions
  const handleRefreshSuggestions = async () => {
    if (refreshSuggestionsRef.current) {
      setRefreshingSuggestions(true);
      try {
        await refreshSuggestionsRef.current();
      } catch (error) {
        console.error("Error during suggestion refresh:", error);
      } finally {
        setRefreshingSuggestions(false);
      }
    }
  };

  // Set refresh handler
  const setRefreshHandler = (handler) => {
    refreshSuggestionsRef.current = handler;
  };

  return {
    refreshingSuggestions,
    handleRefreshSuggestions,
    setRefreshHandler
  };
};

export default useSuggestions; 