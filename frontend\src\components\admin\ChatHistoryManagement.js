import React, { useState, useEffect } from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Alert,
  CircularProgress,
  Typography,
  Collapse,
  Chip
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import adminService from '../../services/adminService';

function ChatHistoryRow({ history, onDelete }) {
  const [open, setOpen] = useState(false);
  const [characterNames, setCharacterNames] = useState({});

  useEffect(() => {
    // Fetch character names for all messages that have a numeric character ID
    const fetchCharacterNames = async () => {
      const uniqueCharacterIds = new Set();
      
      // Collect all unique character IDs from messages
      history.messages.forEach(message => {
        if (!message.isUser && !isNaN(message.character) && message.character) {
          uniqueCharacterIds.add(message.character);
        }
      });
      
      // Fetch character names for each unique ID
      const namesMap = {};
      await Promise.all(
        Array.from(uniqueCharacterIds).map(async id => {
          try {
            const name = await adminService.getCharacterNameById(id);
            namesMap[id] = name;
          } catch (error) {
            console.error(`Failed to fetch character name for ID ${id}:`, error);
            namesMap[id] = `Character ${id}`;
          }
        })
      );
      
      setCharacterNames(namesMap);
    };
    
    if (open) {
      fetchCharacterNames();
    }
  }, [open, history.messages]);

  // Helper function to get character name
  const getCharacterName = (message) => {
    if (message.isUser) {
      return history.username;
    }
    
    // If it's a character message with a numeric ID and we have the name
    if (!isNaN(message.character) && message.character && characterNames[message.character]) {
      return characterNames[message.character];
    }
    
    // Fallback to companionName from history
    return history.companionName;
  };

  return (
    <>
      <TableRow>
        <TableCell>
          <IconButton
            size="small"
            onClick={() => setOpen(!open)}
          >
            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
        </TableCell>
        <TableCell>{history.bookTitle}</TableCell>
        <TableCell>{history.username}</TableCell>
        <TableCell>{history.companionName}</TableCell>
        <TableCell>{new Date(history.lastMessageAt).toLocaleString()}</TableCell>
        <TableCell>{history.messageCount}</TableCell>
        <TableCell>
          <IconButton
            color="error"
            onClick={() => onDelete(history.id)}
          >
            <DeleteIcon />
          </IconButton>
        </TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={7}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 1 }}>
              <Typography variant="h6" gutterBottom component="div">
                Chat Messages
              </Typography>
              <Box sx={{ maxHeight: '300px', overflowY: 'auto' }}>
                {history.messages
                  .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
                  .map((message, index) => (
                    <Box
                      key={index}
                      sx={{
                        p: 1,
                        mb: 1,
                        bgcolor: message.isUser ? 'grey.100' : 'primary.light',
                        borderRadius: 1
                      }}
                    >
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                        <Chip
                          label={getCharacterName(message)}
                          size="small"
                          color={message.isUser ? 'default' : 'primary'}
                        />
                        <Typography variant="caption" color="text.secondary">
                          {new Date(message.timestamp).toLocaleString()}
                        </Typography>
                      </Box>
                      <Typography variant="body2">
                        {message.content}
                      </Typography>
                    </Box>
                  ))}
              </Box>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </>
  );
}

function ChatHistoryManagement() {
  const [chatHistories, setChatHistories] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchChatHistories = async () => {
    try {
      const data = await adminService.getChatHistories();
      setChatHistories(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch chat histories');
      console.error('Error fetching chat histories:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchChatHistories();
  }, []);

  const handleDelete = async (chatId) => {
    if (!window.confirm('Are you sure you want to delete this chat history?')) {
      return;
    }
    setIsLoading(true);
    try {
      await adminService.deleteChatHistory(chatId);
      await fetchChatHistories();
    } catch (err) {
      setError('Failed to delete chat history');
      console.error('Error deleting chat history:', err);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 2 }}>
        <h2>Chat History</h2>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell />
              <TableCell>Book</TableCell>
              <TableCell>User</TableCell>
              <TableCell>Companion</TableCell>
              <TableCell>Last Message</TableCell>
              <TableCell>Messages</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {chatHistories.map((history) => (
              <ChatHistoryRow
                key={history.id}
                history={history}
                onDelete={handleDelete}
              />
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}

export default ChatHistoryManagement; 