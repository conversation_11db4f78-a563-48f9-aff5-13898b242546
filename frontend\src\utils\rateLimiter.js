// Rate limit configurations for different services (in milliseconds)
export const rateLimitConfig = {
    // Authentication related
    auth: {
        login: 1000,
        general: 1000
    },
    // Book operations
    books: {
        fetch: 1000,
        modify: 1000
    },
    // Chat operations
    chat: {
        messages: 500,  // Reduced from 1000ms
        history: 1000   // Reduced from 5000ms to 1000ms
    },
    // Companion operations
    companions: {
        fetch: 1000
    },
    // Preferences
    preferences: {
        fetch: 1000,
        modify: 1000
    },
    // Suggestions
    suggestions: {
        fetch: 1000
    },
    // Default fallback
    default: 1000
};

class RateLimiter {
    constructor(minInterval = rateLimitConfig.default) {
        this.minInterval = minInterval;
        this.lastCallTime = 0;
    }

    async throttle() {
        const now = Date.now();
        const timeSinceLastCall = now - this.lastCallTime;
        
        if (timeSinceLastCall < this.minInterval) {
            const waitTime = this.minInterval - timeSinceLastCall;
            // Add random jitter between 0-1000ms to prevent thundering herd
            const jitter = Math.random() * 1000;
            await new Promise(resolve => setTimeout(resolve, waitTime + jitter));
        }
        
        this.lastCallTime = Date.now();
    }

    // Utility method to update the rate limit
    setRateLimit(newInterval) {
        if (typeof newInterval !== 'number' || newInterval < 0) {
            throw new Error('Rate limit must be a positive number');
        }
        this.minInterval = newInterval;
    }
}

export default RateLimiter;
