import React, { useState, useCallback, useEffect } from 'react';
import {
  Box,
  TextField,
  IconButton
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';

const ChatInput = React.memo(({
  onSend,
  onSubmit,
  disabled,
  textareaHeight,
  handleResizeStart,
  inputRef
}) => {
  const [message, setMessage] = useState('');

  const handleMessageSend = onSubmit || onSend;

  const handleSubmit = useCallback((e) => {
    e.preventDefault();
    if (!message.trim() || disabled) return;
    
    if (handleMessageSend) {
      handleMessageSend(message);
    }
    setMessage('');
    
    // Use setTimeout to ensure focus happens after state updates
    setTimeout(() => {
      if (inputRef?.current) {
        inputRef.current.focus();
      }
    }, 0);
  }, [message, disabled, handleMessageSend, inputRef]);

  const handleKeyPress = useCallback((e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  }, [handleSubmit]);

  // Ensure focus is maintained when component updates
  useEffect(() => {
    if (inputRef?.current) {
      inputRef.current.focus();
    }
  }, [inputRef, message]);

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      sx={{
        display: 'flex',
        gap: 1,
        position: 'relative'
      }}
    >
      <TextField
        fullWidth
        multiline
        maxRows={4}
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyDown={handleKeyPress}
        disabled={disabled}
        placeholder="Type your message..."
        inputRef={inputRef}
        sx={{
          '& .MuiOutlinedInput-root': {
            borderRadius: 2
          },
          '& .MuiInputBase-input': {
            height: textareaHeight ? `${textareaHeight}px` : 'auto',
            overflow: 'auto',
            resize: 'none'
          }
        }}
      />
      {handleResizeStart && (
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: '5px',
            cursor: 'ns-resize',
            zIndex: 1
          }}
          onMouseDown={handleResizeStart}
        />
      )}
      <IconButton
        type="submit"
        disabled={!message.trim() || disabled}
        color="primary"
        sx={{
          alignSelf: 'flex-end'
        }}
      >
        <SendIcon />
      </IconButton>
    </Box>
  );
});

export default ChatInput;
