import api from './axiosConfig';
import { API_BASE_URL } from '../config';

const BASE_URL = `${API_BASE_URL}/api/admin`;

const adminService = {
  // User Management
  getUsers: async (page = 1, limit = 10) => {
    try {
      const response = await api.get(`${BASE_URL}/users?page=${page}&limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching users:', error);
      return { users: [], total: 0 };
    }
  },

  updateUser: async (userId, userData) => {
    try {
      const response = await api.put(`${BASE_URL}/users/${userId}`, userData);
      return response.data;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  },

  deleteUser: async (userId) => {
    try {
      const response = await api.delete(`${BASE_URL}/users/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  },

  clearUserChatHistory: async (userId) => {
    const response = await api.delete(`${BASE_URL}/users/${userId}/clear-chat-history`);
    return response.data;
  },

  // Book Management
  getAllBooks: async (page = 1, limit = 10) => {
    try {
      const response = await api.get(`${BASE_URL}/books?page=${page}&limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching all books:', error);
      return { books: [], total: 0 };
    }
  },

  addBook: async (bookData) => {
    const response = await api.post(`${BASE_URL}/books`, bookData);
    return response.data;
  },

  updateBook: async (bookId, bookData) => {
    const response = await api.put(`${BASE_URL}/books/${bookId}`, bookData);
    return response.data;
  },

  deleteBook: async (bookId) => {
    const response = await api.delete(`${BASE_URL}/books/${bookId}`);
    return response.data;
  },

  // Chat History Management
  getChatHistories: async () => {
    const response = await api.get(`${BASE_URL}/chat-histories`);
    return response.data;
  },

  deleteChatHistory: async (chatId) => {
    const response = await api.delete(`${BASE_URL}/chat-histories/${chatId}`);
    return response.data;
  },

  // Character Management
  getCharacterNameById: async (characterId) => {
    const response = await api.get(`${API_BASE_URL}/api/companions/character/${characterId}`);
    return response.data.name;
  },

  // Analytics
  getAnalytics: async (timeframe = 'week') => {
    try {
      const response = await api.get(`${BASE_URL}/analytics?timeframe=${timeframe}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching analytics:', error);
      return {
        userCount: 0,
        bookCount: 0,
        messageCount: 0,
        activeUsers: [],
        userGrowth: []
      };
    }
  },

  // System Settings
  getSystemSettings: async () => {
    try {
      const response = await api.get(`${BASE_URL}/settings`);
      return response.data;
    } catch (error) {
      console.error('Error fetching system settings:', error);
      return {};
    }
  },

  updateSystemSettings: async (settings) => {
    try {
      const response = await api.put(`${BASE_URL}/settings`, settings);
      return response.data;
    } catch (error) {
      console.error('Error updating system settings:', error);
      throw error;
    }
  },

  // Admin Authentication
  checkAdminAccess: async () => {
    try {
      const response = await api.get(`${BASE_URL}/check-access`);
      return response.data.isAdmin;
    } catch (error) {
      console.error('Error checking admin access:', error);
      return false;
    }
  },



  // Contact Management
  getContacts: async () => {
    try {
      const response = await api.get(`${BASE_URL}/contacts`);
      return response.data;
    } catch (error) {
      console.error('Error fetching contacts:', error);
      return [];
    }
  },

  markContactAsRead: async (contactId) => {
    try {
      const response = await api.put(`${BASE_URL}/contacts/${contactId}/mark-read`);
      return response.data;
    } catch (error) {
      console.error('Error marking contact as read:', error);
      throw error;
    }
  },

  deleteContact: async (contactId) => {
    try {
      const response = await api.delete(`${BASE_URL}/contacts/${contactId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting contact:', error);
      throw error;
    }
  }
};

export default adminService;