import { useCallback } from 'react';
import companionsService from '../../../services/companionsService';

const useCompanionSelection = (onCharacterChange, companions) => {
  const handleCompanionChange = useCallback(async (companionId) => {
    if (!companionId) return;

    try {
      // Update companion preference in backend
      await companionsService.setPreferredCompanion(companionId);
      
      // Find the full companion object
      const selectedCompanion = companions.find(c => c.id.toString() === companionId.toString());
      if (!selectedCompanion) {
        console.error('Selected companion not found:', companionId);
        return;
      }
      
      // Call the parent component's onChange handler with full companion object
      onCharacterChange(selectedCompanion);
    } catch (error) {
      console.error('Error handling companion change:', error);
    }
  }, [onCharacterChange, companions]);

  return {
    handleCompanionChange
  };
};

export default useCompanionSelection;
