"""add_chat_performance_indexes

Revision ID: 03e05b33878e
Revises: 526dc9c82f0f
Create Date: 2025-05-26 10:17:47.586636

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '03e05b33878e'
down_revision: Union[str, None] = '526dc9c82f0f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add indexes for better chat history query performance
    op.create_index('idx_chat_history_user_book', 'chat_history', ['user_id', 'book_id'])
    op.create_index('idx_chat_history_user_chat', 'chat_history', ['user_id', 'chat_id'])
    op.create_index('idx_chat_history_user_character', 'chat_history', ['user_id', 'character'])
    op.create_index('idx_chat_history_timestamp', 'chat_history', ['timestamp'])
    op.create_index('idx_chat_history_composite', 'chat_history', ['user_id', 'book_id', 'character', 'timestamp'])
    op.create_index('idx_chat_history_user_timestamp', 'chat_history', ['user_id', 'timestamp'])


def downgrade() -> None:
    # Remove the indexes
    op.drop_index('idx_chat_history_user_timestamp', table_name='chat_history')
    op.drop_index('idx_chat_history_composite', table_name='chat_history')
    op.drop_index('idx_chat_history_timestamp', table_name='chat_history')
    op.drop_index('idx_chat_history_user_character', table_name='chat_history')
    op.drop_index('idx_chat_history_user_chat', table_name='chat_history')
    op.drop_index('idx_chat_history_user_book', table_name='chat_history')
