import api from './axiosConfig';
import RateLimiter, { rateLimitConfig } from '../utils/rateLimiter';
import { supabase } from './supabaseService';

const generalAuthRateLimiter = new RateLimiter(rateLimitConfig.auth.general);

const authService = {
  // Get current user from API
  getCurrentUser: async () => {
    await generalAuthRateLimiter.throttle();
    try {
      // First check if we have a valid Supabase session
      const { data } = await supabase.auth.getSession();
      if (!data.session) return null;
      
      // Then fetch the user profile from our API
      const response = await api.get('/api/auth/me');
      return response.data;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  },

  // Login with email and password
  login: async (email, password) => {
    await generalAuthRateLimiter.throttle();
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) throw error;
      return data.user;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  // Register with email and password
  register: async (email, password, username) => {
    await generalAuthRateLimiter.throttle();
    try {
      // First register the user with Supabase
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: { username }
        }
      });
      
      if (error) throw error;

      // If email confirmation is required (no active session), skip backend and inform caller
      if (!data.session) {
        return { needsConfirmation: true };
      }

      // Then create the user record in our backend
      const token = await authService.getToken();
      const response = await api.post('/api/auth/register', {
        username // Only send username; backend gets email/id from token
      }, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      // Successful registration and backend creation
      return { needsConfirmation: false, ...response.data };
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  },

  // Logout
  logout: async () => {
    try {
      // Sign out from Supabase
      await supabase.auth.signOut();
    } catch (error) {
      console.error('Logout error:', error);
    }
  },

  // Get Supabase auth token
  getToken: async () => {
    try {
      const { data } = await supabase.auth.getSession();
      return data.session?.access_token || null;
    } catch (error) {
      console.error('Error getting token:', error);
      return null;
    }
  }
};

export default authService;