import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  IconButton
} from '@mui/material';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import ChatMessage from './components/ChatMessage';
import ChatInput from './components/ChatInput';
import ContextMenu from './components/ContextMenu';
import useChatMessages from './hooks/useChatMessages';
import { debounce } from './utils/chatUtils';
import companionsService from '../../services/companionsService';

function ChatInterface({ 
  selectedBook, 
  selectedCharacter, 
  userId, 
  onCharacterChange, 
  books, 
  onBookChange,
  // Add these props to accept them from ChatSection
  messages: externalMessages,
  isLoading: externalIsLoading,
  error: externalError,
  onSend: externalOnSend,
  isLoadingMore: externalIsLoadingMore,
  hasMore: externalHasMore,
  loadMoreMessages: externalLoadMoreMessages,
  clearChat: externalClearChat,
  currentContext: externalCurrentContext
}) {
  const [input, setInput] = useState('');
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [textareaHeight, setTextareaHeight] = useState(100);
  const [companions, setCompanions] = useState([]);
  const [isLoadingCompanions, setIsLoadingCompanions] = useState(false);
  const [defaultCompanion, setDefaultCompanion] = useState(null);
  const [hasInitializedCompanion, setHasInitializedCompanion] = useState(false);
  const [isLoadingBooks, setIsLoadingBooks] = useState(false);
  const [error, setError] = useState(null);
  const inputRef = useRef(null);
  const messagesEndRef = useRef(null);
  const chatContainerRef = useRef(null);
  const resizingRef = useRef(false);
  const startHeightRef = useRef(0);
  const startYRef = useRef(0);

  // Flag to determine if we should use external props or the hook
  const useExternalProps = Boolean(externalMessages);

  // Always call the hook to follow React rules
  const {
    messages: hookMessages,
    isLoading: hookIsLoading,
    error: hookError,
    onSend: hookOnSend,
    isLoadingMore: hookIsLoadingMore,
    hasMore: hookHasMore,
    loadMoreMessages: hookLoadMoreMessages,
    clearChat: hookClearChat,
    currentContext: hookCurrentContext,
    setSkipEffects
  } = useChatMessages(selectedBook, selectedCharacter, userId);

  // Tell the hook to skip its effects if we're using external props
  useEffect(() => {
    if (setSkipEffects && useExternalProps) {
      setSkipEffects(true);
    }
  }, [setSkipEffects, useExternalProps]);

  // Use external props if provided, otherwise use hook values
  const messages = useExternalProps ? externalMessages : hookMessages;
  const isLoading = useExternalProps ? externalIsLoading : hookIsLoading;
  const chatError = useExternalProps ? externalError : hookError;
  const onSend = useExternalProps ? externalOnSend : hookOnSend;
  const isLoadingMore = useExternalProps ? externalIsLoadingMore : hookIsLoadingMore;
  const hasMore = useExternalProps ? externalHasMore : hookHasMore;
  const loadMoreMessages = useExternalProps ? externalLoadMoreMessages : hookLoadMoreMessages;
  const clearChat = useExternalProps ? externalClearChat : hookClearChat;
  const currentContext = useExternalProps ? externalCurrentContext : hookCurrentContext;

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Focus the input field whenever messages change or after loading completes
  useEffect(() => {
    if (!isLoading && inputRef.current) {
      // Use setTimeout to ensure focus happens after DOM updates
      setTimeout(() => {
        inputRef.current.focus();
      }, 0);
    }
  }, [messages, isLoading]);

  // Cache companions in session storage
  const getCachedCompanions = () => {
    const cached = sessionStorage.getItem('companions');
    if (cached) {
      const { data, timestamp } = JSON.parse(cached);
      if (Date.now() - timestamp < 300000) { // 5 minutes cache
        return data;
      }
    }
    return null;
  };

  const setCachedCompanions = (data) => {
    sessionStorage.setItem('companions', JSON.stringify({
      data,
      timestamp: Date.now()
    }));
  };

  // Effect to fetch companions with proper cleanup
  useEffect(() => {
    let isMounted = true;
    
    const initializeCompanions = async () => {
      try {
        setIsLoadingCompanions(true);
        const data = await companionsService.getCompanions();
        
        if (!isMounted) return;

        // Robust error handling: ensure data is an object with a companions property
        if (!data || typeof data !== 'object' || !data.companions || typeof data.companions !== 'object') {
          setCompanions([]);
          setError('Failed to load companions (invalid data)');
          return;
        }

        const companionsList = Object.entries(data.companions).map(([id, companion]) => ({
          id: id.toString(), // Ensure ID is string
          ...companion,
          avatar: `/avatars/${companion.name}.png`
        }));
        
        setCompanions(companionsList);
        
        // Only set default companion if no character is selected and we haven't initialized yet
        if (!selectedCharacter && !hasInitializedCompanion && data.preferred_companion) {
          const preferred = companionsList.find(
            c => c.id === data.preferred_companion.toString()
          );
          if (preferred) {
            onCharacterChange(preferred);
            setHasInitializedCompanion(true);
          }
        }
      } catch (error) {
        console.error('Error fetching companions:', error);
        if (isMounted) {
          setError('Failed to load companions');
        }
      } finally {
        if (isMounted) {
          setIsLoadingCompanions(false);
        }
      }
    };

    initializeCompanions();

    return () => {
      isMounted = false;
    };
  }, [selectedCharacter, hasInitializedCompanion, onCharacterChange]);

  useEffect(() => {
    const focusTimeout = setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 100);

    return () => clearTimeout(focusTimeout);
  }, [selectedCharacter, selectedBook]);

  const handleScroll = (e) => {
    const container = chatContainerRef.current;
    if (container && container.scrollTop === 0 && hasMore && !isLoadingMore) {
      debouncedLoadMoreMessages();
    }
  };

  const debouncedLoadMoreMessages = useCallback(
    debounce(() => {
      loadMoreMessages();
    }, 300),
    [loadMoreMessages]
  );

  const handleSubmit = useCallback(async (messageText) => {
    try {
      console.log('Submitting message:', messageText);
      console.log('handleSubmit - selectedBook:', selectedBook);
      const context = {
        book: selectedBook,
        character: selectedCharacter,
        chatId: currentContext?.chatId
      };
      await onSend(messageText, context);
      setInput('');
      
      // Ensure focus returns to input after message is sent
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 0);
    } catch (err) {
      console.error('Error submitting message:', err);
      setError(err.message);
    }
  }, [onSend, selectedBook, selectedCharacter, currentContext]);

  const handleClearChat = () => {
    setOpenConfirmDialog(true);
  };

  const handleConfirmClear = async () => {
    await clearChat();
    setOpenConfirmDialog(false);
    
    // Focus input after clearing chat
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 0);
  };

  const handleCancelClear = () => {
    setOpenConfirmDialog(false);
    
    // Focus input after canceling dialog
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 0);
  };

  // Add resize handler
  const handleResizeStart = (e) => {
    if (e.button !== 0) return; // Only handle left mouse button
    resizingRef.current = true;
    startHeightRef.current = textareaHeight;
    startYRef.current = e.clientY;

    const handleMouseMove = (e) => {
      if (!resizingRef.current) return;
      const delta = startYRef.current - e.clientY;
      const newHeight = Math.max(100, Math.min(300, startHeightRef.current + delta));
      setTextareaHeight(newHeight);
    };

    const handleMouseUp = () => {
      resizingRef.current = false;
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      
      // Focus input after resizing is complete
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 0);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Format messages for ChatMessage component
  const formatMessagesForDisplay = (messages) => {
    console.log('Formatting messages for display:', messages);
    return messages.map(msg => {
      // Ensure we have a timestamp, using current time as fallback
      const timestamp = msg.timestamp || new Date().toISOString();
      
      return {
        id: msg.id || `${timestamp}-${Math.random()}`,
        type: msg.type || (msg.is_user ? 'user' : 'ai'),
        is_user: msg.is_user === true,
        content: msg.content || msg.text || msg.message,
        timestamp: timestamp
      };
    });
  };

  // Display error if there is one
  if (error || chatError) {
    console.error('Chat error:', error || chatError);
  }

  const renderContent = () => (
    <Box sx={{ 
      flexGrow: 1, 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: 'white',
      borderRadius: 2,
      overflow: 'hidden' 
    }}>
      {/* Chat header */}
      <Box sx={{ 
        p: 2, 
        borderBottom: '1px solid', 
        borderColor: 'divider',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
        </Box>
      </Box>

      {/* Messages area */}
      <Box
        ref={chatContainerRef}
        onScroll={handleScroll}
        sx={{
          overflowY: 'auto',
          flexGrow: 1,
          p: 2,
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {isLoadingMore && (
          <Box sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Loading more messages...
            </Typography>
          </Box>
        )}
        
        {messages.length === 0 && !isLoading ? (
          <Box 
            sx={{ 
              display: 'flex', 
              justifyContent: 'center', 
              alignItems: 'center',
              flexGrow: 1
            }}
          >
            <Box
              sx={{
                width: 250,
                height: 250,
                borderRadius: '50%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                boxShadow: '0 0 10px rgba(0,0,0,0.1)',
                animation: 'pulse 2s infinite ease-in-out',
                '@keyframes pulse': {
                  '0%': { boxShadow: '0 0 0 0 rgba(0,0,0,0.2)' },
                  '70%': { boxShadow: '0 0 0 10px rgba(0,0,0,0)' },
                  '100%': { boxShadow: '0 0 0 0 rgba(0,0,0,0)' }
                },
                backgroundImage: `url(${selectedCharacter?.avatar || `/avatars/${selectedCharacter?.name || 'default'}.png`})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center'
              }}
            />
          </Box>
        ) : (
          messages.map((message, index) => (
            <ChatMessage
              key={message.id || index}
              message={{
                ...message,
                character: null
              }}
              selectedCharacter={null}
            />
          ))
        )}
        
        {isLoading && (
          <ChatMessage
            message={{
              type: 'loading',
              content: 'Thinking...'
            }}
          />
        )}
        
        <div ref={messagesEndRef} />
      </Box>

      {/* Input area */}
      <Box sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}>
        <ChatInput
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onSubmit={handleSubmit}
          isLoading={isLoading}
          inputRef={inputRef}
          onClearChat={handleClearChat}
          textareaHeight={textareaHeight}
          setTextareaHeight={setTextareaHeight}
          handleMouseDown={handleResizeStart}
          showResizeHandle={true}
        />
      </Box>
    </Box>
  );

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', position: 'relative' }}>
      <ContextMenu 
        selectedBook={selectedBook}
        selectedCharacter={selectedCharacter}
        companions={companions}
        isLoadingCompanions={isLoadingCompanions}
        onCompanionChange={onCharacterChange}
        books={books}
        isLoadingBooks={isLoadingBooks}
        onBookChange={onBookChange}
        onClearChat={handleClearChat}
      />
      {renderContent()}
      <Dialog
        open={openConfirmDialog}
        onClose={handleCancelClear}
        aria-labelledby="clear-history-dialog"
      >
        <DialogTitle id="clear-history-dialog">Clear Chat History?</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to clear all chat history? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelClear} color="primary">
            Cancel
          </Button>
          <Button 
            onClick={handleConfirmClear} 
            color="primary" 
            variant="contained"
            sx={{
              backgroundColor: 'primary.main',
              '&:hover': {
                backgroundColor: 'primary.dark',
              }
            }}
          >
            Clear History
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default ChatInterface;
