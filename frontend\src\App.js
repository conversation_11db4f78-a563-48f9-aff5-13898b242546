import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  CssBaseline,
  ThemeProvider,
  Box,
  IconButton,
  Button,
  Snackbar,
  Alert,
} from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import UserSelect from './components/UserSelect';
import theme from './theme';
import { AppProvider, useAppContext } from './context/AppContext';
import AdminSection from './sections/AdminSection';
import CharacterSection from './sections/CharacterSection';
import ChatSection from './sections/ChatSection';
import LibrarySection from './sections/LibrarySection';
import ContactSection from './sections/ContactSection';

/**
 * App content component that uses the context
 */
function AppContent() {
  const [showScrollArrow, setShowScrollArrow] = useState(true);

  const {
    // User management
    currentUser,
    handleUserChange,
    showDefaultCompanionAlert,
    handleDefaultCompanionAlertClose,
    
    // Book management
    books,
    selectedBook,
    selectedBooks,
    handleBookSelect,
    handleDeleteBook,
    handleAddBook,
    
    // Navigation
    activeSection,
    isNavCollapsed,
    contentRef,
    handleSectionChange,
    toggleNavCollapse,
    scrollToContent,
    
    // Character management
    selectedCharacter,
    setSelectedCharacter,
    
    // Suggestions
    refreshingSuggestions,
    handleRefreshSuggestions,
    setRefreshHandler
  } = useAppContext();

  // Handle scroll event to hide arrow when user scrolls down
  useEffect(() => {
    const handleScroll = () => {
      // Calculate scroll percentage (0 to 1)
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollHeight = document.documentElement.scrollHeight;
      const clientHeight = document.documentElement.clientHeight;
      const scrollPercentage = scrollTop / (scrollHeight - clientHeight);

      // Hide arrow when scrolled past 35% of the page, show it when scrolled back up
      if (scrollPercentage > 0.35) {
        setShowScrollArrow(false);
      } else {
        setShowScrollArrow(true);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Modified version of scrollToContent that also hides the arrow
  const scrollToContentAndHideArrow = () => {
    setShowScrollArrow(false);
    if (contentRef.current) {
      const headerOffset = 20;
      const elementPosition = contentRef.current.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  /**
   * Renders the appropriate section based on activeSection
   */
  const renderContent = () => {
    switch (activeSection) {
      case 'admin':
        return <AdminSection />;
      case 'characters':
        return (
          <CharacterSection 
            selectedCharacter={selectedCharacter} 
            setSelectedCharacter={setSelectedCharacter} 
            currentUser={currentUser} 
          />
        );
      case 'chat':
        return (
          <ChatSection 
            selectedBook={selectedBook} 
            selectedCharacter={selectedCharacter} 
            setSelectedCharacter={setSelectedCharacter} 
            currentUser={currentUser} 
            books={books} 
            handleBookSelect={handleBookSelect} 
          />
        );
      case 'contact':
        return <ContactSection />;
      default:
        return (
          <LibrarySection 
            books={books} 
            selectedBooks={selectedBooks} 
            handleBookSelect={handleBookSelect} 
            handleDeleteBook={handleDeleteBook} 
            currentUser={currentUser} 
            handleUserChange={handleUserChange}
            handleAddBook={handleAddBook}
            refreshingSuggestions={refreshingSuggestions}
            handleRefreshSuggestions={handleRefreshSuggestions}
            setRefreshHandler={setRefreshHandler}
          />
        );
    }
  };

  return (
    <>
      <Box sx={{ flexGrow: 1 }}>
        <Box 
          sx={{ 
            position: 'relative',
            width: '100%',
            height: '100vh',
            backgroundImage: `url(${require('./data/image.png')})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
          }}
        >
          <Box 
            sx={{ 
              position: 'fixed',
              top: '20px',
              left: '20px',
              padding: isNavCollapsed ? '8px' : '12px',
              display: 'flex',
              flexDirection: 'column',
              gap: 1.5,
              background: 'rgba(255, 255, 255, 0.9)',
              borderRadius: isNavCollapsed ? '50%' : '15px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              backdropFilter: 'blur(5px)',
              transition: 'all 0.3s ease',
              zIndex: 1000,
              width: isNavCollapsed ? '40px' : 'auto',
              height: isNavCollapsed ? '40px' : 'auto',
              overflow: 'hidden',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 6px 16px rgba(0,0,0,0.15)',
              }
            }}
          >
            <Box sx={{ 
              display: 'flex',
              flexDirection: 'column',
              gap: 1.5,
              position: 'relative'
            }}>
              <IconButton
                onClick={toggleNavCollapse}
                sx={{
                  position: 'absolute',
                  top: -11,
                  left: -4,
                  p: 0,
                  '& .MuiSvgIcon-root': {
                    fontSize: '1rem',
                    color: 'text.secondary'
                  }
                }}
              >
                {isNavCollapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
              </IconButton>
              {!isNavCollapsed && (
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: 'column', 
                  alignItems: 'stretch',
                  gap: 1.5,
                  width: '200px',
                  pt: 0.5
                }}>
                  <Button
                    variant={activeSection === 'library' ? 'contained' : 'outlined'}
                    onClick={() => handleSectionChange('library')}
                    sx={{
                      py: 1.5,
                      backgroundColor: activeSection === 'library' ? 'primary.main' : 'background.paper',
                      color: activeSection === 'library' ? 'white' : 'text.primary',
                      '&:hover': {
                        backgroundColor: activeSection === 'library' ? 'primary.dark' : 'primary.light',
                        color: 'white'
                      }
                    }}
                  >
                    Library
                  </Button>
                  <Button
                    variant={activeSection === 'chat' ? 'contained' : 'outlined'}
                    onClick={() => handleSectionChange('chat')}
                    data-section="chat"
                    sx={{
                      py: 1.5,
                      backgroundColor: activeSection === 'chat' ? 'primary.main' : 'background.paper',
                      color: activeSection === 'chat' ? 'white' : 'text.primary',
                      '&:hover': {
                        backgroundColor: activeSection === 'chat' ? 'primary.dark' : 'primary.light',
                        color: 'white'
                      }
                    }}
                  >
                    Chat
                  </Button>
                  <Button
                    variant={activeSection === 'characters' ? 'contained' : 'outlined'}
                    onClick={() => handleSectionChange('characters')}
                    sx={{
                      py: 1.5,
                      backgroundColor: activeSection === 'characters' ? 'primary.main' : 'background.paper',
                      color: activeSection === 'characters' ? 'white' : 'text.primary',
                      '&:hover': {
                        backgroundColor: activeSection === 'characters' ? 'primary.dark' : 'primary.light',
                        color: 'white'
                      }
                    }}
                  >
                    Reading Companions
                  </Button>
                  <Button
                    variant={activeSection === 'contact' ? 'contained' : 'outlined'}
                    onClick={() => handleSectionChange('contact')}
                    sx={{
                      py: 1.5,
                      backgroundColor: activeSection === 'contact' ? 'primary.main' : 'background.paper',
                      color: activeSection === 'contact' ? 'white' : 'text.primary',
                      '&:hover': {
                        backgroundColor: activeSection === 'contact' ? 'primary.dark' : 'primary.light',
                        color: 'white'
                      }
                    }}
                  >
                    Contact Us
                  </Button>
                  {currentUser?.isAdmin && (
                    <Button
                      variant={activeSection === 'admin' ? 'contained' : 'outlined'}
                      onClick={() => handleSectionChange('admin')}
                      sx={{
                        py: 1.5,
                        backgroundColor: activeSection === 'admin' ? 'primary.main' : 'background.paper',
                        color: activeSection === 'admin' ? 'white' : 'text.primary',
                        '&:hover': {
                          backgroundColor: activeSection === 'admin' ? 'primary.dark' : 'primary.light',
                          color: 'white'
                        }
                      }}
                    >
                      Admin Panel
                    </Button>
                  )}
                </Box>
              )}
            </Box>
          </Box>
          {showScrollArrow && (
            <IconButton
              onClick={scrollToContentAndHideArrow}
              sx={{
                position: 'absolute',
                bottom: '20px',
                left: '50%',
                transform: 'translateX(-50%)',
                backgroundColor: 'rgba(255, 255, 255, 0.7)',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                },
                animation: 'bounce 2s infinite',
                '@keyframes bounce': {
                  '0%, 20%, 50%, 80%, 100%': {
                    transform: 'translateX(-50%) translateY(0)',
                  },
                  '40%': {
                    transform: 'translateX(-50%) translateY(-20px)',
                  },
                  '60%': {
                    transform: 'translateX(-50%) translateY(-10px)',
                  },
                },
              }}
            >
              <KeyboardArrowDownIcon />
            </IconButton>
          )}
        </Box>
        <Box ref={contentRef}>
          <Container maxWidth="lg" sx={{ mt: 4 }}>
            <UserSelect />
            {renderContent()}
          </Container>
        </Box>
      </Box>
      <Snackbar 
        open={showDefaultCompanionAlert} 
        autoHideDuration={6000} 
        onClose={handleDefaultCompanionAlertClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleDefaultCompanionAlertClose} 
          severity="info" 
          sx={{ width: '100%' }}
        >
          Ava has been selected as your default reading companion. You can change this on the Reading Companions page.
        </Alert>
      </Snackbar>
    </>
  );
}

/**
 * Main App component that provides the context and theme
 */
function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AppProvider>
        <AppContent />
      </AppProvider>
    </ThemeProvider>
  );
}

export default App;
