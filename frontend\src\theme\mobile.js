// Mobile-specific theme configuration for BookWorm application
import { createTheme } from '@mui/material';
import palette from './palette';

// Create a mobile-specific theme that extends the base theme
const mobileTheme = createTheme({
  // Use the same palette as the main theme
  palette,
  
  // Typography adjustments for mobile
  typography: {
    fontFamily: '"Verdana", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '1.8rem',
      fontWeight: 600,
    },
    h2: {
      fontSize: '1.5rem',
      fontWeight: 600,
    },
    h3: {
      fontSize: '1.3rem',
      fontWeight: 600,
    },
    h4: {
      fontSize: '1.2rem',
      fontWeight: 600,
    },
    h5: {
      fontSize: '1.1rem',
      fontWeight: 600,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 600,
    },
    body1: {
      fontSize: '0.95rem',
    },
    body2: {
      fontSize: '0.85rem',
    },
    button: {
      textTransform: 'none',
      fontWeight: 500,
    },
  },
  
  // Component overrides for mobile
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
          padding: '10px 16px', // Slightly larger touch target
          minWidth: '44px', // Apple's recommended minimum touch target size
          minHeight: '44px', // Apple's recommended minimum touch target size
        },
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          padding: '12px', // Larger touch target for icon buttons
          '& .MuiSvgIcon-root': {
            fontSize: '1.5rem', // Larger icons for better visibility
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.05)', // Softer shadow
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiInputBase-input': {
            fontSize: '16px', // Prevents iOS zoom on focus
          },
        },
      },
    },
    MuiInputBase: {
      styleOverrides: {
        input: {
          fontSize: '16px', // Prevents iOS zoom on focus
        },
      },
    },
    MuiBottomNavigation: {
      styleOverrides: {
        root: {
          height: '64px', // Taller for easier touch
        },
      },
    },
    MuiBottomNavigationAction: {
      styleOverrides: {
        root: {
          padding: '8px 0',
          minWidth: '80px',
          '& .MuiSvgIcon-root': {
            fontSize: '1.5rem',
          },
        },
      },
    },
    MuiDialog: {
      styleOverrides: {
        paper: {
          margin: '16px',
          width: 'calc(100% - 32px)',
          maxWidth: '600px',
        },
      },
    },
  },
});

export default mobileTheme;
