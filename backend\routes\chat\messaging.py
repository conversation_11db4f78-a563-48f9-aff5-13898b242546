from fastapi import APIRouter, Depends, Request, HTTPException, status
from backend.utils.decorators import get_current_user
from backend.database.db import db
from sqlalchemy.exc import SQLAlchemyError
import logging
import time
from . import logger, chat_service, character
from .helpers import (
    resolve_book_id, 
    get_book_context, 
    get_character_identifier, 
    store_user_message, 
    store_ai_message,
    get_character_info
)

router = APIRouter()

@router.post('/', status_code=status.HTTP_200_OK)
async def chat(request: Request, user=Depends(get_current_user)):
    """Send a chat message"""
    try:
        timings = {}
        t0 = time.monotonic()
        data = await request.json()
        t1 = time.monotonic()
        timings['input_parsing'] = t1 - t0

        if not data or 'message' not in data:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Message is required")

        message = data['message']
        character_identifier = data.get('character') or data.get('characterId')
        logger.info(f"Character identifier: {character_identifier}")

        # Get character identifier and update user preference if needed
        character_identifier = get_character_identifier(user.id, character_identifier)

        # Ensure book_id is treated as an integer or None
        book_id_raw = data.get('book_id') or data.get('bookId')
        book_title = data.get('book_title')
        book_author = data.get('book_author')
        
        # Resolve book ID from raw value or title/author
        book_id = resolve_book_id(user.id, book_id_raw, book_title, book_author)
        
        chat_id_raw = data.get('chat_id') or data.get('chatId')
        chat_id = str(chat_id_raw) if chat_id_raw is not None else None
        stream = data.get('stream', False)

        logger.info(f"Chat request: user={user.id}, message='{message[:30]}...', character={character_identifier}, book_id={book_id}, chat_id={chat_id}")

        # Store user message
        t2 = time.monotonic()
        user_message, chat_id = store_user_message(
            user_id=user.id,
            book_id=book_id,
            message=message,
            character=character_identifier,
            chat_id=chat_id
        )
        t3 = time.monotonic()
        timings['db_add_user_message'] = t3 - t2

        # Get book context if book_id is provided
        t4 = time.monotonic()
        book_context, book_title, book_author = get_book_context(book_id)
        t5 = time.monotonic()
        timings['db_get_book_and_context'] = t5 - t4

        # If frontend provides bookTitle/bookAuthor, use them (override DB if present)
        if 'bookTitle' in data and data['bookTitle']:
            book_title = data['bookTitle']
        if 'bookAuthor' in data and data['bookAuthor']:
            book_author = data['bookAuthor']

        # Generate AI response
        t6 = time.monotonic()
        response_content = chat_service.chat(
            message=message,
            character=character_identifier,
            conversation_id=str(user.id),
            book_id=book_id,
            book_context=book_context,
            chat_id=chat_id,
            stream=False,
            book_title=book_title,
            book_author=book_author
        )
        t7 = time.monotonic()
        timings['ai_response_generation'] = t7 - t6
        logger.info(f"Received response from chat service: {response_content[:100]}...")

        # Ensure response_content is a string
        if not isinstance(response_content, str):
            logger.warning(f"Response content is not a string: {type(response_content)}")
            response_content = str(response_content)

        # Get character info
        character_info = get_character_info(user.id, character_identifier)

        # Store AI response
        t8 = time.monotonic()
        stored_message, chat_id = store_ai_message(
            user_id=user.id,
            book_id=book_id,
            message=response_content,
            character=character_identifier,
            chat_id=chat_id
        )
        t9 = time.monotonic()
        timings['db_add_ai_message'] = t9 - t8

        # Return both the text and the chat ID
        return {
            'text': response_content,
            'chatId': chat_id,
            'characterInfo': character_info,
            'timings': timings
        }

    except Exception as e:
        logger.error(f"Error in chat endpoint: {str(e)}")
        logger.exception("Exception details:")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.post('/send', status_code=status.HTTP_200_OK)
async def send_message(request: Request, user=Depends(get_current_user)):
    try:
        data = await request.json()

        if not data or 'message' not in data:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Message is required")

        message = data['message']
        # Determine character identifier from payload (accepting 'character' or 'characterId')
        character_identifier = data.get('character') or data.get('characterId')
        logger.info(f"Character identifier: {character_identifier}")

        # Get character identifier and update user preference if needed
        character_identifier = get_character_identifier(user.id, character_identifier)

        # Ensure book_id is treated as an integer or None
        # Check for both snake_case and camelCase versions of book_id
        book_id_raw = data.get('book_id') or data.get('bookId')
        book_title = data.get('book_title') or data.get('bookTitle')
        book_author = data.get('book_author') or data.get('bookAuthor')
        
        # Resolve book ID from raw value or title/author
        book_id = resolve_book_id(user.id, book_id_raw, book_title, book_author)
        
        # Check for both snake_case and camelCase versions of chat_id
        chat_id_raw = data.get('chat_id') or data.get('chatId')
        chat_id = str(chat_id_raw) if chat_id_raw is not None else None
        stream = data.get('stream', False)

        logger.info(f"Chat request: user={user.id}, message='{message[:30]}...', character={character_identifier}, book_id={book_id}, chat_id={chat_id}")

        # Store user message
        user_message, chat_id = store_user_message(
            user_id=user.id,
            book_id=book_id,
            message=message,
            character=character_identifier,
            chat_id=chat_id
        )

        # Get book context if book_id is provided
        book_context, book_title, book_author = get_book_context(book_id)

        # If frontend provides bookTitle/bookAuthor, use them (override DB if present)
        if 'bookTitle' in data and data['bookTitle']:
            book_title = data['bookTitle']
        if 'bookAuthor' in data and data['bookAuthor']:
            book_author = data['bookAuthor']

        # Generate AI response using chat service
        if stream:
            def generate():
                full_response = ""
                try:
                    for chunk in chat_service.chat(
                        message=message,
                        character=character_identifier,
                        conversation_id=str(user.id),
                        book_id=book_id,
                        book_context=book_context,
                        chat_id=chat_id,
                        stream=True,
                        book_title=book_title,
                        book_author=book_author
                    ):
                        if chunk:
                            full_response += chunk
                            yield {"text": chunk, "chatId": chat_id}
                    
                    # Store the complete response
                    stored_message, chat_id = store_ai_message(
                        user_id=user.id,
                        book_id=book_id,
                        message=full_response,
                        character=character_identifier,
                        chat_id=chat_id
                    )
                    
                    # Get character info
                    character_info = get_character_info(user.id, character_identifier)
                    
                    # Send final message with complete info
                    yield {"text": "", "chatId": chat_id, "complete": True, "characterInfo": character_info}
                except Exception as e:
                    logger.error(f"Error in streaming response: {str(e)}")
                    logger.exception("Exception details:")
                    yield {"error": str(e), "text": "I'm sorry, I encountered an error."}
            
            return generate()
        else:
            try:
                response_content = chat_service.chat(
                    message=message,
                    character=character_identifier,
                    conversation_id=str(user.id),
                    book_id=book_id,
                    book_context=book_context,
                    chat_id=chat_id,
                    stream=False,
                    book_title=book_title,
                    book_author=book_author
                )
                
                logger.info(f"Received response from chat service: {response_content[:100]}...")
                
                # Ensure response_content is a string
                if not isinstance(response_content, str):
                    logger.warning(f"Response content is not a string: {type(response_content)}")
                    response_content = str(response_content)
                
                # Get character info for the response
                character_info = get_character_info(user.id, character_identifier)
                
                # Store AI response
                stored_message, chat_id = store_ai_message(
                    user_id=user.id,
                    book_id=book_id,
                    message=response_content,
                    character=character_identifier,
                    chat_id=chat_id
                )
                
                # Return both the text and the chat ID
                return {
                    'text': response_content,
                    'chatId': chat_id,
                    'characterInfo': character_info
                }
            except Exception as e:
                logger.error(f"Error generating AI response: {str(e)}")
                logger.exception("Exception details:")
                
                # Return a fallback response
                fallback_response = "I'm sorry, I encountered an error processing your request. Please try again."
                
                try:
                    # Try to store the fallback response
                    stored_message, chat_id = store_ai_message(
                        user_id=user.id,
                        book_id=book_id,
                        message=fallback_response,
                        character=character_identifier,
                        chat_id=chat_id
                    )
                    
                    return {
                        'text': fallback_response,
                        'chatId': chat_id,
                        'error': str(e)
                    }
                except Exception as db_error:
                    logger.error(f"Error storing fallback response: {str(db_error)}")
                    return {
                        'text': fallback_response,
                        'error': str(e)
                    }
    
    except Exception as e:
        logger.error(f"Error in send_message endpoint: {str(e)}")
        logger.exception("Exception details:")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.post('/refresh-context', status_code=status.HTTP_200_OK)
async def refresh_context(request: Request, user=Depends(get_current_user)):
    try:
        data = await request.json()

        if not data or 'book_id' not in data:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Book ID is required")

        book_id = data['book_id']
        character_identifier = data.get('character')

        # Get book context
        book_context, book_title, book_author = get_book_context(book_id)

        # If frontend provides bookTitle/bookAuthor, use them (override DB if present)
        if 'bookTitle' in data and data['bookTitle']:
            book_title = data['bookTitle']
        if 'bookAuthor' in data and data['bookAuthor']:
            book_author = data['bookAuthor']

        # Get AI response
        response_content = chat_service.chat(
            message="",
            character=character_identifier,
            conversation_id=str(user.id),
            book_id=book_id,
            book_context=book_context,
            chat_id=None,
            stream=False,
            book_title=book_title,
            book_author=book_author
        )

        logger.info(f"Received response from chat service: {response_content[:100]}...")

        # Ensure response_content is a string
        if not isinstance(response_content, str):
            logger.warning(f"Response content is not a string: {type(response_content)}")
            response_content = str(response_content)

        # Get character info for the response
        character_info = get_character_info(user.id, character_identifier)

        # Store AI response
        stored_message, chat_id = store_ai_message(
            user_id=user.id,
            book_id=book_id,
            message=response_content,
            character=character_identifier,
            chat_id=None
        )

        # Return both the text and the chat ID
        return {
            'text': response_content,
            'chatId': chat_id,
            'characterInfo': character_info
        }
    except Exception as e:
        logger.error(f"Error in refresh_context endpoint: {str(e)}")
        logger.exception("Exception details:")
        
        # Return error response
        return {
            'error': str(e),
            'text': "I'm sorry, I encountered an error refreshing the context."
        }
