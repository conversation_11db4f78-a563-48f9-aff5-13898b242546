"""Factory for creating AI provider clients."""
import logging
from typing import Optional, Any, Dict
from backend.config import config
from backend.ai.provider.base_provider import BaseProvider
from backend.ai.provider.openai_provider import OpenAIProvider
from backend.ai.provider.anthropic_provider import AnthropicProvider
from backend.ai.provider.openrouter_provider import OpenRouterProvider


class ClientFactory:
    """Factory for creating AI provider clients."""
    
    @staticmethod
    def create_client(provider_name: str, model: str, logger: Optional[logging.Logger] = None) -> BaseProvider:
        """Create and return an appropriate provider client.
        
        Args:
            provider_name: The name of the provider (openai, anthropic, openrouter)
            model: The model name to use
            logger: Logger instance
            
        Returns:
            An initialized provider client
            
        Raises:
            ValueError: If the provider is invalid or required API keys are missing
        """
        logger = logger or logging.getLogger(__name__)
        provider_name = provider_name.lower()
        
        if provider_name == "openrouter":
            openrouter_api_key = config.OPENROUTER_API_KEY
            if not openrouter_api_key:
                raise ValueError("OPENROUTER_API_KEY is not set in config")
            
            openrouter_api_base = config.OPENROUTER_API_BASE
            return OpenRouterProvider(
                api_key=openrouter_api_key,
                model=model,
                base_url=openrouter_api_base,
                logger=logger
            )
        
        elif provider_name == "anthropic":
            anthropic_api_key = config.ANTHROPIC_API_KEY
            if not anthropic_api_key:
                raise ValueError("ANTHROPIC_API_KEY is not set in config")
            
            return AnthropicProvider(
                api_key=anthropic_api_key,
                model=model,
                logger=logger
            )
        
        elif provider_name == "openai":
            openai_api_key = config.OPENAI_API_KEY
            if not openai_api_key:
                raise ValueError("OPENAI_API_KEY is not set in config")
            
            return OpenAIProvider(
                api_key=openai_api_key,
                model=model,
                logger=logger
            )
        
        else:
            raise ValueError(f"Invalid AI provider: {provider_name}. Must be one of: openai, anthropic, openrouter (case-insensitive)")
