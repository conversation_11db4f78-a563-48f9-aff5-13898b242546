import { useState, useRef, useEffect } from 'react';

/**
 * Custom hook for managing navigation and section changes
 */
const useNavigation = () => {
  const [activeSection, setActiveSection] = useState(null);
  const [isNavCollapsed, setIsNavCollapsed] = useState(false);
  const contentRef = useRef(null);

  // Handle section change
  const handleSectionChange = (section) => {
    setActiveSection(section);
  };

  // Toggle navigation collapse state
  const toggleNavCollapse = () => {
    setIsNavCollapsed(prev => !prev);
  };

  // Scroll to content section
  const scrollToContent = () => {
    if (contentRef.current) {
      const headerOffset = 20;
      const elementPosition = contentRef.current.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  // Auto-scroll to content when section changes
  useEffect(() => {
    if (activeSection) {
      // Use a small RAF chain to ensure content is rendered
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          scrollToContent();
        });
      });
    }
  }, [activeSection]);

  return {
    activeSection,
    isNavCollapsed,
    contentRef,
    handleSectionChange,
    toggleNavCollapse,
    scrollToContent
  };
};

export default useNavigation; 