import { API_BASE_URL } from '../config';
import RateLimiter, { rateLimitConfig } from '../utils/rateLimiter';
import api from './axiosConfig';

const messageRateLimiter = new RateLimiter(rateLimitConfig.chat.messages);
const historyRateLimiter = new RateLimiter(rateLimitConfig.chat.history);

// Track ongoing operations to prevent duplicates
const ongoingMessageRequests = new Map();

const chatService = {
  // Send a message to the book character
  sendMessage: async ({ bookId, message, characterId = null, userId = null, chatId = null, bookTitle = null, bookAuthor = null }) => {
    const requestKey = `${bookId}-${Date.now()}`;
    
    if (ongoingMessageRequests.has(bookId)) {
      console.warn('Message request already in progress for book:', bookId);
      return ongoingMessageRequests.get(bookId);
    }
    
    const requestPromise = (async () => {
      await messageRateLimiter.throttle();
      try {
        const payload = {
          message: message,
          bookId: bookId
        };
        
        if (characterId) {
          if (typeof characterId === 'object' && characterId !== null) {
            console.log('Character object detected:', characterId);
            payload.characterId = characterId.id;
          } else {
            payload.characterId = characterId;
          }
          console.log('Sending characterId to backend:', payload.characterId, typeof payload.characterId);
        }
        
        if (userId) {
          payload.userId = userId;
        }
        
        if (chatId) {
          payload.chatId = chatId;
        }
        
        if (bookTitle) {
          payload.bookTitle = bookTitle;
        }
        if (bookAuthor) {
          payload.bookAuthor = bookAuthor;
        }
        
        console.log('API POST /api/chat/send payload:', JSON.stringify(payload));
        const response = await api.post('/api/chat/send', payload, {
          timeout: 60000 // 60 second timeout for chat messages
        });
        
        return response.data;
      } catch (error) {
        console.error('Error sending message:', error);
        throw error;
      } finally {
        ongoingMessageRequests.delete(bookId);
      }
    })();
    
    ongoingMessageRequests.set(bookId, requestPromise);
    return requestPromise;
  },

  // Get chat history for a book or chat session
  getChatHistory: async (params) => {
    await historyRateLimiter.throttle();
    try {
      // Support both simple bookId argument and options object
      let bookId, chatId, character, characterId, before, limit;
      if (typeof params === 'object' && params !== null) {
        ({ bookId, chatId, character, characterId, before, limit } = params);
      } else {
        bookId = params;
      }

      const queryParams = {};
      if (bookId !== undefined && bookId !== null) queryParams.book_id = bookId;
      if (chatId !== undefined && chatId !== null) queryParams.chat_id = chatId;
      // Allow both character or characterId keys
      const charParam = character ?? characterId;
      if (charParam !== undefined && charParam !== null) queryParams.character = charParam;
      if (before !== undefined && before !== null) queryParams.before = before;
      if (limit !== undefined && limit !== null) queryParams.limit = limit;

      const response = await api.get('/api/chat/history', { params: queryParams });

      // Backend may return either an array (when chat_id provided) or an object
      // with `chat_history` / `messages` keys when only book_id is provided.
      const data = response.data;
      if (Array.isArray(data)) return data;
      if (data && Array.isArray(data.chat_history)) return data.chat_history;
      if (data && Array.isArray(data.messages)) return data.messages;
      return [];
    } catch (error) {
      console.error('Error fetching chat history:', error);
      return [];
    }
  },

  // Clear chat history for a book
  clearChatHistory: async ({ bookId, chatId }) => {
    await historyRateLimiter.throttle();
    try {
      const response = await api.delete('/api/chat/clear', {
        params: { book_id: bookId, chat_id: chatId }
      });
      return response.data;
    } catch (error) {
      console.error('Error clearing chat history:', error);
      throw error;
    }
  }
};

export default chatService;
