from fastapi import APIRouter, Depends, Request, HTTPException, status
from backend.database.db import db
from backend.utils.decorators import get_current_user
from sqlalchemy.exc import OperationalError, SQLAlchemyError
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/books", tags=["books"])

@router.get("/", status_code=status.HTTP_200_OK)
async def get_books(request: Request, user=Depends(get_current_user)):
    try:
        books = db.get_books(user.id, session=request.state.db)
        return books
    except OperationalError as e:
        logger.error(f"Database connection error: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Database connection failed")
    except SQLAlchemyError as e:
        logger.error(f"Database error: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Database error occurred")
    except Exception as e:
        logger.error(f"Unexpected error in get_books: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Unexpected error")

@router.delete("/{book_id}", status_code=status.HTTP_200_OK)
async def delete_book(book_id: int, request: Request, user=Depends(get_current_user)):
    try:
        success = db.delete_book(book_id, user.id, session=request.state.db)
        if success:
            return {"message": "Book deleted successfully"}
        book = db.get_book(book_id, session=request.state.db)
        if not book:
            raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Book not found")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to delete book")
    except Exception as e:
        logger.error(f"Error deleting book {book_id}: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Error deleting book")

@router.post("/", status_code=status.HTTP_201_CREATED)
async def add_book(request: Request, user=Depends(get_current_user)):
    data = await request.json()
    
    # Validate required fields
    if not data or 'title' not in data:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail="Title is required")
    
    # Extract all possible fields
    title = data['title']
    author = data.get('author')
    isbn = data.get('isbn')
    description = data.get('description')
    metadata = data.get('metadata', {})
    
    # If description is provided, add it to metadata
    if description:
        if not metadata:
            metadata = {}
        metadata['description'] = description
    
    set_as_current = data.get('set_as_current', True)
    
    try:
        use_ai_suggestions = data.get('use_ai_suggestions', False)

        ai_changes = None

        if use_ai_suggestions:
            from backend.ai.book_services import BookServices
            ai_service = BookServices()

            # Validate title
            valid, corrected_title = ai_service.validate_book_title(title)
            # Generate author suggestion
            generated_author = ai_service.generate_author_name(title)

            # Determine if AI suggests changes
            ai_changes = {}
            if corrected_title and corrected_title != title:
                ai_changes['corrected_title'] = corrected_title
                ai_changes['original_title'] = title
            if generated_author and generated_author != (author or ''):
                ai_changes['generated_author'] = generated_author
                ai_changes['original_author'] = author

            # If AI suggests changes, return them without adding the book yet
            if ai_changes:
                return {
                    "ai_changes": ai_changes,
                    "message": "AI suggestions available. Please confirm changes."
                }

        # If no AI suggestions or AI disabled, add the book directly
        book = db.add_book(
            user_id=user.id,
            title=title,
            author=author,
            isbn=isbn,
            metadata=metadata,
            set_as_current=set_as_current,
            session=request.state.db
        )
        
        # Check if the book already existed in the library
        already_exists = getattr(book, 'already_exists', False)
        
        # Ensure we return all expected fields
        book_data = {
            'id': book.id,
            'title': book.title,
            'author': book.author,
            'isbn': book.isbn,
            'current_page': book.current_page,
            'book_metadata': book.book_metadata,
            'is_current': book.is_current,
            'already_exists': already_exists
        }
        
        return book_data
    except Exception as e:
        logger.error(f"Error adding book: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Error adding book")
