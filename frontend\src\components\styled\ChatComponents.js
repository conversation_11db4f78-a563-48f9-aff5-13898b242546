import { styled } from '@mui/material';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';

export const ResizeHandle = styled('div')({
  width: '100%',
  height: '4px',
  backgroundColor: '#C4A68A',
  cursor: 'ns-resize',
  position: 'absolute',
  top: -2,
  left: 0,
  zIndex: 1,
  '&:hover': {
    backgroundColor: '#AB8B6E',
  },
});

export const StyledTextarea = styled('textarea')({
  width: '100%',
  padding: '12px',
  borderRadius: '4px',
  border: '1px solid #C4A68A',
  fontFamily: 'Verdana, sans-serif',
  fontSize: '16px',
  resize: 'none',
  outline: 'none',
  transition: 'all 0.2s ease-in-out',
  overflowY: 'scroll',
  '&::placeholder': {
    color: '#C4A68A',
  },
  '&:focus': {
    borderColor: '#AB8B6E',
    boxShadow: '0 0 0 2px rgba(196, 166, 138, 0.2)',
  },
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: '#f1f1f1',
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb': {
    background: '#C4A68A',
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb:hover': {
    background: '#AB8B6E',
  }
});

export const StyledMarkdown = styled(ReactMarkdown)({
  fontFamily: 'Verdana, sans-serif',
  fontSize: '16px',
  lineHeight: '1.6',
  '& p': {
    margin: '0.5em 0',
  },
  '& code': {
    backgroundColor: 'rgba(0, 0, 0, 0.04)',
    padding: '0.2em 0.4em',
    borderRadius: '3px',
    fontSize: '85%',
    fontFamily: 'Consolas, Monaco, "Courier New", monospace',
  },
  '& pre': {
    margin: '1em 0',
    padding: 0,
    backgroundColor: 'transparent',
    borderRadius: '4px',
    overflow: 'auto',
  },
  '& pre > div': {
    margin: 0,
    padding: '1em',
    borderRadius: '4px',
  },
  '& blockquote': {
    margin: '1em 0',
    padding: '0 1em',
    color: 'rgba(0, 0, 0, 0.6)',
    borderLeft: '0.25em solid rgba(0, 0, 0, 0.1)',
  },
  '& ul, & ol': {
    marginLeft: '1.5em',
  },
  '& table': {
    borderCollapse: 'collapse',
    width: '100%',
    margin: '1em 0',
  },
  '& th, & td': {
    border: '1px solid rgba(0, 0, 0, 0.1)',
    padding: '0.5em',
  },
  '& img': {
    maxWidth: '100%',
    height: 'auto',
  },
});

// Create a markdown component with syntax highlighting
export const MarkdownWithSyntaxHighlighting = (props) => (
  <StyledMarkdown
    {...props}
    components={{
      code({node, inline, className, children, ...props}) {
        const match = /language-(\w+)/.exec(className || '');
        return !inline && match ? (
          <SyntaxHighlighter
            style={tomorrow}
            language={match[1]}
            PreTag="div"
            {...props}
          >
            {String(children).replace(/\n$/, '')}
          </SyntaxHighlighter>
        ) : (
          <code className={className} {...props}>
            {children}
          </code>
        );
      }
    }}
  />
); 