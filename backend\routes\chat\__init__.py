from fastapi import APIRouter, Depends, Request, HTTPException, status
from backend.database.db import db
from backend.utils.decorators import get_current_user
from backend.ai.chat_service import ChatService
from backend.ai import character
from backend.ai.prompt_utils import format_book_context
from sqlalchemy.exc import SQLAlchemyError
import json
import logging

logger = logging.getLogger(__name__)

# Initialize chat service
chat_service = ChatService()

# Create router with the same prefix and tags as the original
router = APIRouter(prefix="/api/chat", tags=["chat"])

# Import sub-routers
from .history import router as history_router
from .messaging import router as messaging_router
from .maintenance import router as maintenance_router

# Include sub-routers
router.include_router(history_router)
router.include_router(messaging_router)
router.include_router(maintenance_router)
