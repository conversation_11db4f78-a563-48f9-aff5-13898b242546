import React from 'react';
import { render, screen } from '@testing-library/react';
import ChatMessage from '../../components/chat/components/ChatMessage';

describe('ChatMessage Component', () => {
  const mockMessage = {
    content: 'Test message content',
    role: 'user',
    timestamp: new Date().toISOString(),
  };

  test('renders message content correctly', () => {
    render(<ChatMessage message={mockMessage} />);
    expect(screen.getByText(mockMessage.content)).toBeInTheDocument();
  });

  test('displays correct role indicator', () => {
    render(<ChatMessage message={mockMessage} />);
    const messageElement = screen.getByRole('article');
    expect(messageElement).toHaveClass('user-message');
  });
});
