import React, { useMemo } from 'react';
import { 
  BottomNavigation, 
  BottomNavigationAction, 
  Paper,
  Box,
  Tooltip,
  Badge
} from '@mui/material';
import HomeIcon from '@mui/icons-material/Home';
import ChatIcon from '@mui/icons-material/Chat';
import PersonIcon from '@mui/icons-material/Person';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import ContactMailIcon from '@mui/icons-material/ContactMail';
import LoginIcon from '@mui/icons-material/Login';
import useDeviceDetection from '../../hooks/useDeviceDetection';

/**
 * Mobile navigation component optimized for iPhone
 * 
 * @param {Object} props - Component props
 * @param {string} props.activeSection - Currently active section
 * @param {Function} props.handleSectionChange - Function to change the active section
 * @returns {React.ReactElement} Mobile navigation component
 */
const MobileNavigation = ({ activeSection, handleSectionChange, currentUser }) => {
  const { isIPhone, getSafeAreaInsets } = useDeviceDetection();
  const safeAreaInsets = getSafeAreaInsets();
  
  // Determine which sections to show based on user login status
  const sections = useMemo(() => {
    if (currentUser) {
      // For logged in users: library, characters, chat, contact, admin
      return ['library', 'characters', 'chat', 'contact', 'admin'];
    } else {
      // For non-logged in users: library, sign-in, chat, contact
      return ['library', 'sign-in', 'chat', 'contact'];
    }
  }, [currentUser]);
  
  // Map section names to values for BottomNavigation
  const sectionValues = useMemo(() => {
    return sections.reduce((acc, section, index) => {
      acc[section] = index;
      return acc;
    }, {});
  }, [sections]);
  
  // Handle navigation change
  const handleChange = (event, newValue) => {
    const newSection = sections[newValue];
    console.log(`Navigation changed to: ${newSection}`);

    // For non-authenticated users, certain sections should trigger auth prompt
    if (!currentUser && (newSection === 'chat' || newSection === 'sign-in')) {
      // Dispatch event to show auth screen
      window.dispatchEvent(new CustomEvent('show-auth'));
      return;
    }
    
    // If non-authenticated user clicks library again, go to welcome screen
    if (!currentUser && newSection === 'library') {
      // Call the handler which is set up to return to welcome in non-auth mode
      handleSectionChange(newSection);
      return;
    }

    // First update our local UI state
    handleSectionChange(newSection);

    // Then dispatch a global event for other components to react
    window.dispatchEvent(new CustomEvent('section-change', {
      detail: { section: newSection }
    }));
  };

  return (
    <Box sx={{ width: '100%', position: 'fixed', bottom: 0, left: 0, zIndex: 1100 }}>
      <Paper 
        elevation={3} 
        sx={{ 
          borderTopLeftRadius: 16, 
          borderTopRightRadius: 16,
          // Add bottom padding for iPhone safe area
          paddingBottom: isIPhone ? `${safeAreaInsets.bottom}px` : 0
        }}
      >
        <BottomNavigation
          value={sectionValues[activeSection] || 0}
          onChange={handleChange}
          showLabels
          sx={{
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
            '& .MuiBottomNavigationAction-root': {
              color: 'text.secondary',
              '&.Mui-selected': {
                color: 'primary.main',
              },
            },
          }}
        >
          <BottomNavigationAction 
            label="Library" 
            icon={<MenuBookIcon />} 
            sx={{ minWidth: 0 }}
          />
          {currentUser ? (
            <BottomNavigationAction 
              label="Characters" 
              icon={<PersonIcon />} 
              sx={{ minWidth: 0 }}
            />
          ) : (
            <BottomNavigationAction 
              label="Sign In" 
              icon={
                <Badge color="error" variant="dot">
                  <LoginIcon />
                </Badge>
              } 
              sx={{ minWidth: 0 }}
            />
          )}
          <BottomNavigationAction 
            label="Chat" 
            icon={currentUser ? <ChatIcon /> : (
              <Tooltip title="Sign in to chat" placement="top">
                <Badge color="error" variant="dot">
                  <ChatIcon />
                </Badge>
              </Tooltip>
            )} 
            sx={{ minWidth: 0 }}
          />
          <BottomNavigationAction 
            label="Contact" 
            icon={<ContactMailIcon />} 
            sx={{ minWidth: 0 }}
          />
          {currentUser && (
            <BottomNavigationAction 
              label="Admin" 
              icon={<HomeIcon />} 
              sx={{ minWidth: 0 }}
            />
          )}
        </BottomNavigation>
      </Paper>
    </Box>
  );
};

export default MobileNavigation;
