import api from '../../services/axiosConfig';
import chatService from '../../services/chatService';

// Mock dependencies
jest.mock('../../services/axiosConfig');
jest.mock('../../utils/rateLimiter', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      throttle: jest.fn().mockResolvedValue(undefined)
    })),
    rateLimitConfig: {
      chat: {
        message: { requestsPerMinute: 10 },
        history: { requestsPerMinute: 20 }
      }
    }
  };
});

describe('Chat API Response Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('sendMessage response handling', () => {
    const bookId = 'book123';
    const message = 'What is your name?';

    test('handles standard character response correctly', async () => {
      const mockResponse = {
        data: {
          response: 'My name is <PERSON>.',
          characterId: 'char456',
          chatId: 'chat101',
          timestamp: '2023-01-01T12:00:00Z'
        }
      };
      
      api.post.mockResolvedValue(mockResponse);
      
      const result = await chatService.sendMessage(bookId, message);
      
      expect(result).toEqual(mockResponse.data);
      expect(result).toHaveProperty('response');
      expect(result).toHaveProperty('characterId');
      expect(result).toHaveProperty('chatId');
    });

    test('handles response with context information', async () => {
      const mockResponse = {
        data: {
          response: 'Let me tell you about the murder case.',
          characterId: 'char456',
          contextInfo: {
            bookReference: 'Chapter 3',
            confidence: 0.89,
            bookQuotes: ['Elementary, my dear Watson.']
          }
        }
      };
      
      api.post.mockResolvedValue(mockResponse);
      
      const result = await chatService.sendMessage(bookId, message);
      
      expect(result).toEqual(mockResponse.data);
      expect(result).toHaveProperty('contextInfo');
      expect(result.contextInfo).toHaveProperty('bookReference');
      expect(result.contextInfo).toHaveProperty('confidence');
    });

    test('handles error response gracefully', async () => {
      const mockErrorResponse = {
        response: {
          status: 429,
          data: {
            error: 'Too many requests',
            retryAfter: 30
          }
        }
      };
      
      api.post.mockRejectedValue(mockErrorResponse);
      
      await expect(chatService.sendMessage(bookId, message)).rejects.toEqual(mockErrorResponse);
    });

    test('handles timeout response', async () => {
      const timeoutError = new Error('timeout of 60000ms exceeded');
      timeoutError.code = 'ECONNABORTED';
      
      api.post.mockRejectedValue(timeoutError);
      
      await expect(chatService.sendMessage(bookId, message)).rejects.toThrow('timeout of 60000ms exceeded');
    });

    test('handles malformed response', async () => {
      // A response missing expected fields
      const malformedResponse = {
        data: {
          // Missing 'response' field
          chatId: 'chat101'
        }
      };
      
      api.post.mockResolvedValue(malformedResponse);
      
      const result = await chatService.sendMessage(bookId, message);
      
      // Service should still return what it got
      expect(result).toEqual(malformedResponse.data);
      expect(result).not.toHaveProperty('response');
    });
  });

  describe('getChatHistory response handling', () => {
    const bookId = 'book123';

    test('handles empty chat history', async () => {
      const emptyHistory = { data: [] };
      api.get.mockResolvedValue(emptyHistory);
      
      const result = await chatService.getChatHistory(bookId);
      
      expect(result).toEqual([]);
      expect(Array.isArray(result)).toBe(true);
    });

    test('handles populated chat history', async () => {
      const mockHistory = {
        data: [
          { 
            role: 'user', 
            content: 'Hello', 
            timestamp: '2023-01-01T12:00:00Z',
            userId: 'user123'
          },
          { 
            role: 'assistant', 
            content: 'Hello, how can I help you?', 
            timestamp: '2023-01-01T12:00:01Z',
            characterId: 'char456'
          },
          { 
            role: 'user', 
            content: 'Tell me about your book', 
            timestamp: '2023-01-01T12:00:02Z',
            userId: 'user123'
          }
        ]
      };
      
      api.get.mockResolvedValue(mockHistory);
      
      const result = await chatService.getChatHistory(bookId);
      
      expect(result).toEqual(mockHistory.data);
      expect(result.length).toBe(3);
      expect(result[0]).toHaveProperty('role', 'user');
      expect(result[1]).toHaveProperty('role', 'assistant');
      expect(result[0]).toHaveProperty('timestamp');
    });

    test('handles error by returning empty array', async () => {
      api.get.mockRejectedValue(new Error('Network error'));
      
      const result = await chatService.getChatHistory(bookId);
      
      expect(result).toEqual([]);
    });
  });

  describe('clearChatHistory response handling', () => {
    const bookId = 'book123';

    test('handles successful history clear', async () => {
      const successResponse = {
        data: { 
          success: true,
          message: 'Chat history cleared successfully' 
        }
      };
      
      api.delete.mockResolvedValue(successResponse);
      
      const result = await chatService.clearChatHistory(bookId);
      
      expect(result).toEqual(successResponse.data);
      expect(result.success).toBe(true);
    });

    test('handles failed history clear', async () => {
      const errorResponse = {
        response: {
          status: 403,
          data: {
            success: false,
            error: 'Unauthorized to clear chat history'
          }
        }
      };
      
      api.delete.mockRejectedValue(errorResponse);
      
      await expect(chatService.clearChatHistory(bookId)).rejects.toEqual(errorResponse);
    });
  });
});
