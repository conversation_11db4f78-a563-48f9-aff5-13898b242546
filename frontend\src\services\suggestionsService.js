import { API_BASE_URL } from '../config';
import RateLimiter, { rateLimitConfig } from '../utils/rateLimiter';
import api from './axiosConfig';

const suggestionsFetchRateLimiter = new RateLimiter(rateLimitConfig.suggestions.fetch);
const suggestionAddRateLimiter = new RateLimiter(rateLimitConfig.suggestions.add);

const suggestionsService = {
  // Get book suggestions for a user
  getSuggestions: async () => { // Removed userId parameter
    await suggestionsFetchRateLimiter.throttle();
    try {
      const response = await api.get(`/api/suggestions/`); // Changed endpoint
      return response.data;
    } catch (error) {
      console.error('Error fetching suggestions:', error);
      // Re-throw the error so the calling component can handle it
      throw error;
    }
  },

  // Add a suggested book to the user's library
  addSuggestedBook: async (suggestion) => {
    await suggestionAddRateLimiter.throttle();
    try {
      const response = await api.post('/api/books', {
        title: suggestion.title,
        author: suggestion.author,
        genre: suggestion.genre,
        from_suggestion: true
      });
      return response.data;
    } catch (error) {
      console.error('Error adding suggested book:', error);
      throw error;
    }
  },

  // Refresh suggestions for a user
  refreshSuggestions: async (userId) => {
    await suggestionsFetchRateLimiter.throttle();
    try {
      // Use the POST endpoint to force refresh suggestions
      const response = await api.post(`/api/suggestions/`);
      return response.data;
    } catch (error) {
      console.error('Error refreshing suggestions:', error);
      throw error;
    }
  },

  // Validate book title using AI
  verifyBook: async (title) => {
    try {
      const response = await api.post('/api/ai-book/verify', { title });
      return response.data;
    } catch (error) {
      console.error('Error verifying book title:', error);
      throw error;
    }
  },

  // Generate AI suggestions for a book
  generateAISuggestions: async (title) => {
    try {
      const response = await api.post('/api/ai-book/generate', { title });
      return response.data;
    } catch (error) {
      console.error('Error generating AI suggestions:', error);
      throw error;
    }
  },

  // Get a single new suggestion
  getNewSuggestion: async () => {
    await suggestionsFetchRateLimiter.throttle();
    try {
      const response = await api.get('/api/suggestions/single');
      return response.data;
    } catch (error) {
      console.error('Error fetching new suggestion:', error);
      throw error;
    }
  }
};

export default suggestionsService;