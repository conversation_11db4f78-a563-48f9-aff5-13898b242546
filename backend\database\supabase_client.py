import logging
from supabase import create_client, Client

from backend.config import config

logger = logging.getLogger(__name__)

class SupabaseClient:
    _instance = None
    _client = None

    def __new__(cls):
        """Singleton pattern to ensure only one instance of SupabaseClient exists."""
        if cls._instance is None:
            cls._instance = super(SupabaseClient, cls).__new__(cls)
            cls._instance._initialize_client()
        return cls._instance

    def _initialize_client(self):
        """Initialize the Supabase client with credentials from environment variables."""
        try:
            url = config.SUPABASE_URL
            key = config.SUPABASE_API_KEY

            if not url or not key:
                logger.error("Supabase URL or API key not found in configuration")
                raise ValueError("Missing Supabase credentials")

            self._client = create_client(url, key)
            logger.info("Supabase client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {str(e)}")
            raise

    @property
    def client(self) -> Client:
        """Get the Supabase client instance."""
        if self._client is None:
            self._initialize_client()
        return self._client



    def sign_up(self, email, password, username=None):
        """Register a new user."""
        try:
            options = {"data": {}}

            if username:
                options["data"]["username"] = username

            auth_response = self.client.auth.sign_up({
                "email": email,
                "password": password,
                "options": options
            })

            user = auth_response.user
            session = auth_response.session

            if not user:
                logger.warning(f"Failed to create Supabase user for: {email}")
                return None

            return {
                "user": user,
                "session": session
            }
        except Exception as e:
            logger.error(f"Registration error: {str(e)}")
            return None

    def sign_out(self, access_token=None):
        """Sign out a user."""
        try:
            # If token is provided, set it before sign out
            if access_token:
                self.client.auth.set_session(access_token)

            self.client.auth.sign_out()
            return True
        except Exception as e:
            logger.error(f"Sign out error: {str(e)}")
            return False

    def get_user(self, user_id):
        """Get user by ID from Supabase Auth."""
        try:
            return self.client.auth.admin.get_user_by_id(user_id)
        except Exception as e:
            logger.error(f"Error getting user by ID: {str(e)}")
            return None

    def get_user_by_email(self, email):
        """Get user by email from Supabase Auth."""
        try:
            users = self.client.auth.admin.list_users()
            if users and hasattr(users, 'users'):
                for user in users.users:
                    if user.email.lower() == email.lower():
                        return user
            return None
        except Exception as e:
            logger.error(f"Error getting user by email: {str(e)}")
            return None

    def create_user(self, email, password, username=None):
        """Create a new user in Supabase Auth."""
        try:
            user_data = {
                "email": email,
                "password": password,
                "user_metadata": {"username": username} if username else {}
            }

            user = self.client.auth.admin.create_user(user_data)
            return user
        except Exception as e:
            logger.error(f"Error creating user: {str(e)}")
            return None



    def get_user_from_token(self, token: str):
        """Validate token and get user data using Supabase API."""
        try:
            # This uses the service_role key implicitly if the client was initialized with it
            user_response = self.client.auth.get_user(token)
            return user_response.user
        except Exception as e:
            logger.error(f"Error validating token via Supabase API: {str(e)}")
            # Differentiate between invalid token and other API errors if possible
            if "invalid token" in str(e).lower() or "JWTExpired" in str(e):
                return None  # Clearly indicates token is bad
            raise  # Re-raise other errors (like network issues)


# Create a singleton instance to be imported by other modules
supabase = SupabaseClient()