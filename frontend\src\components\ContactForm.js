import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  TextField, 
  CircularProgress,
  Alert,
  Typography,
  Paper,
  Grid,
  Divider,
  Fade,
  Slide,
  InputAdornment
} from '@mui/material';
import { 
  Send as SendIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Subject as SubjectIcon,
  Message as MessageIcon
} from '@mui/icons-material';
import contactService from '../services/contactService';
import { primaryGradientButton, secondaryOutlinedButton } from './styled/buttonStyles';

const ContactForm = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const validateEmail = (email) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Form validation
    if (!name.trim()) {
      setError('Name is required');
      return;
    }
    
    if (!email.trim() || !validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }
    
    if (!subject.trim()) {
      setError('Subject is required');
      return;
    }
    
    if (!message.trim()) {
      setError('Message is required');
      return;
    }

    try {
      setError('');
      setSuccess('');
      setIsLoading(true);
      
      const result = await contactService.submitContactForm({
        name,
        email,
        subject,
        message
      });

      if (result.success) {
        setSuccess(result.message);
        // Reset form
        setName('');
        setEmail('');
        setSubject('');
        setMessage('');
      } else {
        setError('Failed to send your message. Please try again.');
      }
    } catch (error) {
      console.error('Contact form error:', error);
      setError('Failed to send your message: ' + (error.message || 'Please try again.'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Fade in={true} timeout={800}>
      <Paper 
        elevation={3} 
        sx={{
          borderRadius: '12px',
          overflow: 'hidden',
          bgcolor: 'background.paper',
          transition: 'all 0.3s ease',
          '&:hover': {
            boxShadow: '0 8px 24px rgba(74, 56, 40, 0.12)'
          }
        }}
      >
        <Box sx={{
          p: { xs: 2, sm: 3, md: 4 },
          background: 'linear-gradient(135deg, #C4A68A 0%, #E6B8A8 100%)',
        }}>
          <Typography 
            variant="h4" 
            component="h2" 
            align="center"
            sx={{
              color: 'white',
              fontWeight: 700,
              mb: 1,
              textShadow: '0px 2px 4px rgba(0,0,0,0.2)'  
            }}
          >
            Want to say hello?
          </Typography>
          <Typography 
            variant="body1" 
            align="center"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              mb: 0
            }}
          >
            Fill out the form below and we'll get back to you as soon as possible.
          </Typography>
        </Box>

        <Box sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
          {error && (
            <Slide direction="down" in={!!error} mountOnEnter unmountOnExit>
              <Alert severity="error" sx={{ mb: 3, borderRadius: '8px' }}>
                {error}
              </Alert>
            </Slide>
          )}
          
          {success && (
            <Slide direction="down" in={!!success} mountOnEnter unmountOnExit>
              <Alert 
                severity="success" 
                sx={{ 
                  mb: 3,
                  borderRadius: '8px',
                  '& .MuiAlert-icon': {
                    fontSize: '1.5rem'
                  }
                }}
              >
                {success}
              </Alert>
            </Slide>
          )}

          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Your Name"
                  variant="outlined"
                  fullWidth
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  disabled={isLoading}
                  required
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PersonIcon color="primary" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '8px',
                      transition: 'all 0.3s',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'secondary.main'
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderWidth: '1px'
                      }
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Email Address"
                  type="email"
                  variant="outlined"
                  fullWidth
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                  required
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <EmailIcon color="primary" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '8px',
                      transition: 'all 0.3s',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'secondary.main'
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderWidth: '1px'
                      }
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Subject"
                  variant="outlined"
                  fullWidth
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                  disabled={isLoading}
                  required
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SubjectIcon color="primary" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '8px',
                      transition: 'all 0.3s',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'secondary.main'
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderWidth: '1px'
                      }
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Message"
                  variant="outlined"
                  multiline
                  rows={5}
                  fullWidth
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  disabled={isLoading}
                  required
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start" sx={{ alignSelf: 'flex-start', mt: 1.5 }}>
                        <MessageIcon color="primary" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '8px',
                      transition: 'all 0.3s',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'secondary.main'
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderWidth: '1px'
                      }
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <Box sx={{ mt: 2 }}>
                  <Divider sx={{ mb: 3 }} />
                  <Button
                    type="submit"
                    variant="contained"
                    fullWidth
                    disabled={isLoading}
                    sx={{
                      ...primaryGradientButton,
                      py: 1.8,
                      fontSize: '1.1rem'
                    }}
                    startIcon={isLoading ? null : <SendIcon />}
                  >
                    {isLoading ? <CircularProgress size={24} color="inherit" /> : 'Send Message'}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </form>
        </Box>
      </Paper>
    </Fade>
  );
};

export default ContactForm; 