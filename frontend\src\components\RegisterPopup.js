import React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import RegisterForm from './RegisterForm';
import { useAppContext } from '../context/AppContext';

const RegisterPopup = ({ open, setOpen, onSuccess }) => {
  const { currentUser } = useAppContext();

  const handleClose = () => {
    setOpen(false);
  };

  // Automatically close the popup if the user becomes authenticated
  React.useEffect(() => {
    if (currentUser && open) {
      setOpen(false);
    }
  }, [currentUser, open, setOpen]);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      disableRestoreFocus
    >
      <DialogTitle sx={{ textAlign: 'center' }}>Create a BookWorm account</DialogTitle>
      <DialogContent>
        <RegisterForm onSuccess={onSuccess || handleClose} />
      </DialogContent>
    </Dialog>
  );
};

export default RegisterPopup;
