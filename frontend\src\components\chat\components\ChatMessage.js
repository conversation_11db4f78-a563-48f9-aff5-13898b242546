import React from 'react';
import {
  Box,
  Typography,
  CircularProgress
} from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import { formatTimestamp } from '../utils/chatUtils';
import { MarkdownWithSyntaxHighlighting } from '../../styled/ChatComponents';
import remarkGfm from 'remark-gfm';

const ChatMessage = React.memo(({
  message,
  selectedCharacter
}) => {
  console.log('ChatMessage received message:', message); // Debug log
  
  // Handle both new messages and loaded history messages
  // is_user comes from backend, isUser is used in frontend
  const isUser = message.type === 'user' || message.is_user === true;
  const isLoading = message.type === 'loading';
  const isError = message.isError || message.type === 'error';

  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          p: 1
        }}
      >
        <CircularProgress size={20} />
        <Typography variant="body2" color="text.secondary">
          {message.content}
        </Typography>
      </Box>
    );
  }

  // Enhanced debug logging for content extraction
  console.log('Message properties:', {
    content: message.content,
    message: message.message,
    text: message.text,
    type: message.type,
    is_user: message.is_user
  });

  // Handle content from both new messages and loaded history
  // More robust content extraction with additional fallbacks
  let messageContent = '';
  if (typeof message.content === 'string' && message.content.trim()) {
    messageContent = message.content;
  } else if (typeof message.message === 'string' && message.message.trim()) {
    messageContent = message.message;
  } else if (typeof message.text === 'string' && message.text.trim()) {
    messageContent = message.text;
  } else if (message.type === 'ai' || message.type === 'assistant' || message.is_user === false) {
    // Try to parse as JSON if it's an AI response that might be serialized
    try {
      const parsedContent = message.content ? JSON.parse(message.content) : null;
      if (parsedContent && typeof parsedContent.text === 'string') {
        messageContent = parsedContent.text;
      } else if (parsedContent && typeof parsedContent === 'string') {
        messageContent = parsedContent;
      }
    } catch (e) {
      // Ignore JSON parsing errors and continue with fallbacks
    }
  }
  
  // Only show error if we've exhausted all options and still don't have content
  if (!messageContent) {
    console.warn('No valid content found in message:', message);
    messageContent = 'No message content available';
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: isUser ? 'flex-end' : 'flex-start',
        mb: 1
      }}
    >
      <Box
        sx={{
          maxWidth: '70%',
          backgroundColor: isUser 
            ? 'primary.main' 
            : isError 
              ? '#ffebee' // Light red for errors
              : 'grey.100',
          color: isUser 
            ? 'white' 
            : isError 
              ? '#d32f2f' // Red text for errors
              : 'text.primary',
          borderRadius: 2,
          p: 1.5,
          position: 'relative',
          ...(isError && {
            borderLeft: '4px solid #d32f2f'
          })
        }}
      >
        {isError && (
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <ErrorOutlineIcon color="error" fontSize="small" sx={{ mr: 1 }} />
            <Typography variant="body2" color="error" fontWeight="bold">
              Error
            </Typography>
          </Box>
        )}
        {isUser ? (
          <Typography variant="body1">
            {messageContent}
          </Typography>
        ) : (
          <MarkdownWithSyntaxHighlighting remarkPlugins={[remarkGfm]}>
            {messageContent}
          </MarkdownWithSyntaxHighlighting>
        )}
      </Box>
      {message.timestamp && (
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ mt: 0.5 }}
        >
          {formatTimestamp(message.timestamp)}
        </Typography>
      )}
    </Box>
  );
});

export default ChatMessage;
