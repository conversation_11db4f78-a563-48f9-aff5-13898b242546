from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from sentry_sdk import init as sentry_init
from sentry_sdk.integrations.asgi import SentryAsgiMiddleware
import sentry_sdk
from sqlalchemy.exc import SQLAlchemyError, OperationalError
import os
import logging
from backend.config import config
from backend.database.db import init_db, get_db_session
from backend.routes import register_routers

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.CORS_ORIGINS,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
    allow_credentials=True,
)

if config.ENV == 'production' and config.SENTRY_DSN:
    sentry_init(
        dsn=config.SENTRY_DSN,
        integrations=[SentryAsgiMiddleware()],
        traces_sample_rate=1.0,
        environment=config.ENV
    )

@app.on_event("startup")
async def startup_event():
    try:
        init_db()
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise

@app.middleware("http")
async def db_session_middleware(request: Request, call_next):
    try:
        request.state.db = get_db_session()
        response = await call_next(request)
    finally:
        try:
            request.state.db.close()
        except:
            pass
    return response

@app.exception_handler(SQLAlchemyError)
async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError):
    logger.error(f"Database error: {exc}")
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Database error occurred")

@app.exception_handler(OperationalError)
async def operational_exception_handler(request: Request, exc: OperationalError):
    logger.error(f"Operational DB error: {exc}")
    raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail="Database connection error")

from fastapi import Depends
import time
from collections import defaultdict
import threading

class InMemoryRateLimiter:
    def __init__(self, rate_limit_per_minute=60):
        self.rate_limit = rate_limit_per_minute
        self.window = 60  # seconds
        self.requests = defaultdict(list)
        self.lock = threading.Lock()
        
    def is_rate_limited(self, key: str) -> bool:
        current_time = time.time()
        with self.lock:
            self.requests[key] = [t for t in self.requests[key] 
                                if current_time - t < self.window]
            
            if len(self.requests[key]) >= self.rate_limit:
                return True
                
            self.requests[key].append(current_time)
            return False

rate_limit_per_minute = int(config.RATE_LIMIT.split('/')[0]) // 60 if '/' in config.RATE_LIMIT else 100
in_memory_limiter = InMemoryRateLimiter(rate_limit_per_minute)

async def rate_limit(request: Request):
    key = request.client.host
    if in_memory_limiter.is_rate_limited(key):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded"
        )

@app.exception_handler(429)
async def ratelimit_handler(request: Request, exc: HTTPException):
    return {"error": "Rate limit exceeded", "message": str(exc.detail)}

@app.exception_handler(404)
async def not_found_error(request: Request, exc: HTTPException):
    return {"error": "Not found", "message": "The requested resource was not found"}

@app.exception_handler(500)
async def internal_error(request: Request, exc: HTTPException):
    return {"error": "Internal server error", "message": "An unexpected error occurred"}

@app.get("/health", tags=["Health"])
async def health_check():
    return {"status": "healthy", "environment": config.ENV}

@app.get("/", tags=["Root"])
async def root():
    return {"message": "Welcome to BookWorm API", "version": "1.0"}

register_routers(app)
