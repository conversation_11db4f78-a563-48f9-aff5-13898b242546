import React, { useState } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  CircularProgress,
  Link,
  Alert
} from '@mui/material';
import { supabase } from '../services/supabaseService';
import api from '../services/axiosConfig';
import RegisterPopup from './RegisterPopup'; // Import RegisterPopup
import LoginIcon from '@mui/icons-material/Login';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import { primaryGradientButton, secondaryOutlinedButton } from './styled/buttonStyles';

const LoginForm = ({ onSuccess }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isRegisterPopupOpen, setIsRegisterPopupOpen] = useState(false); // State for popup

  const handleLogin = async (e) => {
    e.preventDefault();

    if (!email.trim()) {
      setError('Email is required');
      return;
    }

    if (!password) {
      setError('Password is required');
      return;
    }

    try {
      setError('');
      setIsLoading(true);

      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        throw error;
      }

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Login error:', error);
      setError(error.message || 'Login failed. Please check your credentials.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenRegisterPopup = () => {
    setIsRegisterPopupOpen(true);
  };

  const handleCloseRegisterPopup = () => {
    setIsRegisterPopupOpen(false);
  };



  return (
    <Box sx={{ maxWidth: 400, width: '100%', mx: 'auto' }}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <form onSubmit={handleLogin}>
        <TextField
          label="Email"
          type="email"
          variant="outlined"
          fullWidth
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          sx={{ mb: 2 }}
          disabled={isLoading}
        />
        <TextField
          label="Password"
          type="password"
          variant="outlined"
          fullWidth
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          sx={{ mb: 2 }}
          disabled={isLoading}
        />
        <Button
          type="submit"
          variant="contained"
          fullWidth
          disabled={isLoading}
          sx={primaryGradientButton}
          startIcon={<LoginIcon />}
        >
          {isLoading ? <CircularProgress size={24} /> : 'Log In'}
        </Button>
        <Box sx={{ mt: 3, textAlign: 'center', display: 'flex', justifyContent: 'center' }}>
          <Button
            variant="outlined"
            onClick={handleOpenRegisterPopup}
            sx={secondaryOutlinedButton}
            startIcon={<PersonAddIcon />}
          >
            Register New Account
          </Button>
        </Box>
      </form>

      {/* Add the RegisterPopup component */}
      <RegisterPopup
        open={isRegisterPopupOpen}
        setOpen={setIsRegisterPopupOpen}
      />
    </Box>
  );
};

export default LoginForm;