/**
 * Utility for caching images using IndexedDB
 */

// Database configuration
const DB_NAME = 'bookworm-cache';
const STORE_NAME = 'images';
const DB_VERSION = 1;

// Initialize the database
const initDB = () => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      // Create an object store for the image cache if it doesn't exist
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        db.createObjectStore(STORE_NAME, { keyPath: 'id' });
      }
    };
    
    request.onsuccess = (event) => {
      resolve(event.target.result);
    };
    
    request.onerror = (event) => {
      console.error('IndexedDB error:', event.target.error);
      reject(event.target.error);
    };
  });
};

/**
 * Store an image in the cache
 * @param {string} id - Unique identifier for the image (e.g., book_id)
 * @param {string} url - The URL of the image
 * @param {Blob} blob - The image data as a Blob
 */
export const cacheImage = async (id, url, blob) => {
  try {
    const db = await initDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORE_NAME], 'readwrite');
      const store = transaction.objectStore(STORE_NAME);
      
      const item = {
        id,
        url,
        blob,
        timestamp: Date.now(),
      };
      
      const request = store.put(item);
      
      request.onsuccess = () => resolve(true);
      request.onerror = (event) => {
        console.error('Error caching image:', event.target.error);
        reject(event.target.error);
      };
    });
  } catch (error) {
    console.error('Failed to cache image:', error);
    return false;
  }
};

/**
 * Retrieve an image from the cache
 * @param {string} id - Unique identifier for the image
 * @returns {Promise<Object|null>} The cached image data or null if not found
 */
export const getCachedImage = async (id) => {
  try {
    const db = await initDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORE_NAME], 'readonly');
      const store = transaction.objectStore(STORE_NAME);
      
      const request = store.get(id);
      
      request.onsuccess = (event) => {
        const result = event.target.result;
        if (result) {
          resolve(result);
        } else {
          resolve(null);
        }
      };
      
      request.onerror = (event) => {
        console.error('Error retrieving cached image:', event.target.error);
        reject(event.target.error);
      };
    });
  } catch (error) {
    console.error('Failed to retrieve cached image:', error);
    return null;
  }
};

/**
 * Fetch an image from a URL and cache it
 * @param {string} id - Unique identifier for the image
 * @param {string} url - The URL of the image to fetch and cache
 * @returns {Promise<string>} A local object URL for the image
 */
export const fetchAndCacheImage = async (id, url) => {
  try {
    // First check if the image is already in the cache
    const cachedImage = await getCachedImage(id);
    if (cachedImage) {
      // Create an object URL from the cached blob
      return URL.createObjectURL(cachedImage.blob);
    }
    
    // If not in cache, fetch it
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
    }
    
    const blob = await response.blob();
    
    // Cache the image
    await cacheImage(id, url, blob);
    
    // Return an object URL for immediate use
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error('Error in fetchAndCacheImage:', error);
    // Return the original URL if caching fails
    return url;
  }
};

/**
 * Clear expired images from the cache
 * @param {number} maxAge - Maximum age in milliseconds (default: 7 days)
 */
export const clearExpiredImages = async (maxAge = 7 * 24 * 60 * 60 * 1000) => {
  try {
    const db = await initDB();
    const transaction = db.transaction([STORE_NAME], 'readwrite');
    const store = transaction.objectStore(STORE_NAME);
    
    const now = Date.now();
    const request = store.openCursor();
    
    request.onsuccess = (event) => {
      const cursor = event.target.result;
      if (cursor) {
        if (now - cursor.value.timestamp > maxAge) {
          // Delete expired items
          cursor.delete();
        }
        cursor.continue();
      }
    };
  } catch (error) {
    console.error('Failed to clear expired images:', error);
  }
};

/**
 * Utility function to check if IndexedDB is available
 * @returns {boolean} Whether IndexedDB is available
 */
export const isIndexedDBAvailable = () => {
  return typeof indexedDB !== 'undefined';
};

// Run a cleanup of expired images when this module is loaded
clearExpiredImages().catch(console.error);

export default {
  fetchAndCacheImage,
  getCachedImage,
  cacheImage,
  clearExpiredImages,
  isIndexedDBAvailable
}; 