import React, { useState, useEffect } from 'react';
import <PERSON>actD<PERSON> from 'react-dom/client';
import App from './App';
import MobileApp from './MobileApp';

// Guard debug logs: disable console.log outside development builds
if (process.env.NODE_ENV !== 'development') {
  // Convert noisy console.logs to silent no-ops in production
  console.log = () => {};
}

// Device detection constants
const MOBILE_REGEX = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
const IPHONE_REGEX = /iPhone/i;

// App selector component that chooses between mobile and desktop versions
const AppSelector = () => {
  // Initialize with desktop version, will update after device detection
  const [isMobileDevice, setIsMobileDevice] = useState(false);
  const [isIPhone, setIsIPhone] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Detect device type
    const userAgent = navigator.userAgent;
    const isMobile = MOBILE_REGEX.test(userAgent);
    const isIPhone = IPHONE_REGEX.test(userAgent);
    
    setIsMobileDevice(isMobile);
    setIsIPhone(isIPhone);
    setIsLoading(false);
    
    // Add a meta viewport tag for proper mobile scaling
    if (isMobile) {
      const viewportMeta = document.createElement('meta');
      viewportMeta.name = 'viewport';
      viewportMeta.content = 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover';
      document.head.appendChild(viewportMeta);
      
      // Add iOS specific meta tags
      if (isIPhone) {
        // Add apple-mobile-web-app-capable meta tag
        const appleMobileWebAppCapable = document.createElement('meta');
        appleMobileWebAppCapable.name = 'apple-mobile-web-app-capable';
        appleMobileWebAppCapable.content = 'yes';
        document.head.appendChild(appleMobileWebAppCapable);
        
        // Add apple-mobile-web-app-status-bar-style meta tag
        const statusBarStyle = document.createElement('meta');
        statusBarStyle.name = 'apple-mobile-web-app-status-bar-style';
        statusBarStyle.content = 'black-translucent';
        document.head.appendChild(statusBarStyle);
      }
    }
  }, []);

  if (isLoading) {
    // Show a simple loading state while detecting device
    return <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>Loading...</div>;
  }

  // Render the appropriate app version based on device detection
  return isMobileDevice ? <MobileApp /> : <App />;
};

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <AppSelector />
  </React.StrictMode>
);
