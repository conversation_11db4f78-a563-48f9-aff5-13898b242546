from fastapi import APIRouter, Depends, Request, HTTPException, status
from backend.utils.decorators import get_current_user
from backend.database.db import db
from sqlalchemy.exc import SQLAlchemyError
import logging
from . import logger
from .helpers import format_chat_messages

router = APIRouter()

@router.get('/sessions', status_code=status.HTTP_200_OK)
async def get_chat_sessions(request: Request, user=Depends(get_current_user)):
    """Get chat sessions for a user and optionally filtered by book"""
    book_id = request.query_params.get('book_id')
    character = request.query_params.get('character')

    # Get unique chat sessions from chat history
    chat_history = db.get_chat_history(user.id, book_id=book_id, character=character)

    # Group by chat_id and get latest message
    sessions = {}
    for msg in chat_history:
        if msg.chat_id not in sessions:
            sessions[msg.chat_id] = {
                'chat_id': msg.chat_id,
                'last_message': msg.message,
                'timestamp': msg.timestamp.isoformat(),
                'character': msg.character
            }
    return list(sessions.values())

@router.get('/history', status_code=status.HTTP_200_OK)
async def get_chat_history(request: Request, user=Depends(get_current_user)):
    """Get chat history for a specific chat session"""
    try:
        book_id = request.query_params.get('book_id')
        chat_id = request.query_params.get('chat_id')
        character = request.query_params.get('character')
        limit = request.query_params.get('limit')
        
        # Convert limit to int if provided
        if limit:
            try:
                limit = int(limit)
            except ValueError:
                limit = None

        logger.info(f"Fetching chat history - user_id: {user.id}, book_id: {book_id}, chat_id: {chat_id}, character: {character}, limit: {limit}")

        if not user.id:
            logger.error("User ID is required but not provided")
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="User ID is required")

        chat_history = db.get_chat_history(user.id, book_id=book_id, chat_id=chat_id, character=character, limit=limit)
        logger.info(f"Retrieved {len(chat_history)} chat messages from database")

        messages = format_chat_messages(chat_history)

        if chat_id:
            return messages
        else:
            return {'chat_history': messages, 'count': len(messages)}

    except Exception as e:
        logger.error(f"Error in get_chat_history: {str(e)}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.get('/history/{book_id}', status_code=status.HTTP_200_OK)
async def get_chat_history_by_book_id(book_id: int, request: Request, user=Depends(get_current_user)):
    """Get chat history for a specific book ID (path param)."""
    try:
        if not user.id:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="User ID is required")
        chat_history = db.get_chat_history(user.id, book_id=book_id)
        messages = format_chat_messages(chat_history)
        return {'chat_history': messages, 'count': len(messages)}
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.get('/history-paginated', status_code=status.HTTP_200_OK)
async def get_chat_history_paginated(request: Request, user=Depends(get_current_user)):
    """Get paginated chat history for a specific chat session"""
    try:
        book_id = request.query_params.get('book_id')
        chat_id = request.query_params.get('chat_id')
        offset = int(request.query_params.get('offset', 0))
        limit = int(request.query_params.get('limit', 50))
        
        if book_id and book_id.isdigit():
            book_id = int(book_id)
        else:
            book_id = None
            
        logger.info(f"Fetching paginated chat history - user_id: {user.id}, book_id: {book_id}, chat_id: {chat_id}, offset: {offset}, limit: {limit}")
        
        total_count, chat_history = db.get_chat_history_paginated(
            user_id=user.id,
            book_id=book_id,
            chat_id=chat_id,
            offset=offset,
            limit=limit
        )
        
        messages = format_chat_messages(chat_history)
        
        return {
            'chat_history': messages,
            'total_count': total_count,
            'offset': offset,
            'limit': limit
        }
    except Exception as e:
        logger.error(f"Error in get_chat_history_paginated: {str(e)}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
