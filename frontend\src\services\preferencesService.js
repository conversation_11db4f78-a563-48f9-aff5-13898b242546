import { API_BASE_URL } from '../config';
import RateLimiter, { rateLimitConfig } from '../utils/rateLimiter';
import api from './axiosConfig';

const preferencesRateLimiter = new RateLimiter(rateLimitConfig.preferences.fetch);
const preferenceUpdateRateLimiter = new RateLimiter(rateLimitConfig.preferences.update);

// Track error state to reduce repeated logging
let lastErrorTime = 0;
const ERROR_LOG_INTERVAL = 60000; // 1 minute between error logs

const shouldLogError = () => {
  const now = Date.now();
  if (now - lastErrorTime > ERROR_LOG_INTERVAL) {
    lastErrorTime = now;
    return true;
  }
  return false;
};

const preferencesService = {
  // Get user's companion preference
  getCompanionPreference: async () => {
    await preferencesRateLimiter.throttle();
    try {
      const response = await api.get('/api/preferences/companion');
      return response.data;
    } catch (error) {
      if (error.response?.status === 401) {
        // Return default value for unauthorized users
        return { companion: null, isDefault: true };
      }
      
      if (shouldLogError()) {
        console.error('Error fetching companion preference:', error);
      }
      return { companion: null, isDefault: true };
    }
  },

  // Set preferred companion
  setCompanionPreference: async (companionId) => {
    await preferenceUpdateRateLimiter.throttle();
    try {
      const response = await api.post('/api/preferences/companion', { companion: companionId });
      return response.data;
    } catch (error) {
      if (shouldLogError()) {
        console.error('Error setting companion preference:', error);
      }
      throw error;
    }
  }
};

export default preferencesService;