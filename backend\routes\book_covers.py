from fastapi import APIRouter, Depends, Request, HTTPException, status
import requests
import logging
from backend.utils.decorators import get_optional_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/book-covers", tags=["book-covers"])

@router.get("/{book_identifier}", status_code=status.HTTP_200_OK)
async def get_book_cover(book_identifier: str, identifier_type: str = "isbn", size: str = "M", user=Depends(get_optional_user)):
    """
    Fetch a book cover image URL from Open Library API or other sources.
    
    Args:
        book_identifier: ISBN, title, or other identifier
        identifier_type: Type of identifier (isbn, title, etc.)
        size: Size of cover image (S, M, L)
    
    Returns:
        An object with cover_url
    """
    try:
        # Default response with a fallback cover
        response_data = {
            "cover_url": None,
            "success": False,
            "message": "No cover found"
        }
        
        # Open Library API for covers
        if identifier_type.lower() == "isbn":
            # Try direct ISBN lookup
            cover_url = f"https://covers.openlibrary.org/b/isbn/{book_identifier}-{size}.jpg"
            response_data["cover_url"] = cover_url
            response_data["success"] = True
            response_data["message"] = "Cover found via ISBN"
            return response_data
            
        elif identifier_type.lower() == "title" or identifier_type.lower() == "search":
            # Search Open Library by title
            search_url = f"https://openlibrary.org/search.json?title={book_identifier}&limit=1"
            search_response = requests.get(search_url)
            
            if search_response.status_code == 200:
                data = search_response.json()
                if data.get("numFound", 0) > 0 and len(data.get("docs", [])) > 0:
                    doc = data["docs"][0]
                    
                    # Try to get ISBN
                    isbn = None
                    if "isbn" in doc and len(doc["isbn"]) > 0:
                        isbn = doc["isbn"][0]
                    
                    # Try to get cover ID
                    cover_id = None
                    if "cover_i" in doc:
                        cover_id = doc["cover_i"]
                    
                    # Determine which URL to use
                    if isbn:
                        cover_url = f"https://covers.openlibrary.org/b/isbn/{isbn}-{size}.jpg"
                        response_data["cover_url"] = cover_url
                        response_data["success"] = True
                        response_data["message"] = "Cover found via title search"
                    elif cover_id:
                        cover_url = f"https://covers.openlibrary.org/b/id/{cover_id}-{size}.jpg"
                        response_data["cover_url"] = cover_url
                        response_data["success"] = True
                        response_data["message"] = "Cover found via title search"
                    
        return response_data
    
    except Exception as e:
        logger.error(f"Error fetching book cover: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching book cover"
        )
