import { useCallback } from 'react';
import bookService from '../../../services/bookService';
import chatService from '../../../services/chatService';
import { API_BASE_URL } from '../../../config';

const useBookSelection = (onBookChange) => {
  const handleBookChange = useCallback(async (bookId, books) => {
    if (!bookId) return;
    
    const selectedBook = books.find(book => book.id === bookId);
    if (!selectedBook) return;

    try {
      // Use the optimized book-switch endpoint
      const response = await bookService.switchBook(selectedBook.id);
      
      // Update state with the response data
      onBookChange({
        book: response.book,
        chatHistory: response.chat_history,
        companionPreference: response.companion
      });
    } catch (error) {
      console.error('Error switching book:', error);
    }
  }, [onBookChange]);

  return {
    handleBookChange
  };
};

export default useBookSelection;
