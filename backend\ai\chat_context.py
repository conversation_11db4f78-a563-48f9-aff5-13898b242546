"""Manages persistent chat context including book and character information."""
from typing import Dict, Any, Optional

class ChatContext:
    """Maintains the state and context of a chat conversation."""
    
    def __init__(self, conversation_id: str):
        """Initialize chat context.
        
        Args:
            conversation_id: The unique identifier for the conversation
        """
        self.conversation_id = conversation_id
        self.book_id: Optional[str] = None
        self.book_context: str = ""
        self.character_id: Optional[str] = None
        self.chat_id: Optional[str] = None
        self._metadata: Dict[str, Any] = {}

    def update_book_context(self, book_id: str, book_context: str) -> None:
        """Update the book-related context.
        
        Args:
            book_id: The book's identifier
            book_context: The book's contextual information
        """
        self.book_id = book_id
        self.book_context = book_context

    def update_character(self, character_id: str) -> None:
        """Update the character information.
        
        Args:
            character_id: The character's identifier
        """
        self.character_id = character_id

    def update_chat_id(self, chat_id: str) -> None:
        """Update the chat session identifier.
        
        Args:
            chat_id: The chat session identifier
        """
        self.chat_id = chat_id

    def set_metadata(self, key: str, value: Any) -> None:
        """Set additional metadata for the context.
        
        Args:
            key: Metadata key
            value: Metadata value
        """
        self._metadata[key] = value

    def get_metadata(self, key: str) -> Any:
        """Get metadata value by key.
        
        Args:
            key: Metadata key
            
        Returns:
            The metadata value if it exists, None otherwise
        """
        return self._metadata.get(key)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the context to a dictionary.
        
        Returns:
            Dictionary representation of the context
        """
        return {
            'conversation_id': self.conversation_id,
            'book_id': self.book_id,
            'book_context': self.book_context,
            'character_id': self.character_id,
            'chat_id': self.chat_id,
            'metadata': self._metadata
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatContext':
        """Create a ChatContext instance from a dictionary.
        
        Args:
            data: Dictionary containing context data
            
        Returns:
            New ChatContext instance
        """
        context = cls(data['conversation_id'])
        if 'book_id' in data and 'book_context' in data:
            context.update_book_context(data['book_id'], data['book_context'])
        if 'character_id' in data:
            context.update_character(data['character_id'])
        if 'chat_id' in data:
            context.update_chat_id(data['chat_id'])
        if 'metadata' in data:
            context._metadata = data['metadata']
        return context
