from fastapi import APIRouter, Depends, Request, HTTPException, status
from pydantic import BaseModel, EmailStr
from backend.utils.decorators import get_current_user
from backend.database.db import Contact
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/contact", tags=["contact"])

class ContactFormData(BaseModel):
    name: str
    email: EmailStr
    subject: str
    message: str

@router.post('', status_code=status.HTTP_201_CREATED)
async def submit_contact_form(contact_data: ContactFormData, request: Request):
    """
    Submit a contact form message
    """
    try:
        session = request.state.db
        new_contact = Contact(
            name=contact_data.name,
            email=contact_data.email,
            subject=contact_data.subject,
            message=contact_data.message,
            is_read=False
        )
        session.add(new_contact)
        session.commit()
        return {
            "success": True,
            "message": "Your message has been sent successfully! We will get back to you soon."
        }
    except Exception as e:
        session.rollback()
        logger.error(f"Error submitting contact form: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to submit contact form"
        )
