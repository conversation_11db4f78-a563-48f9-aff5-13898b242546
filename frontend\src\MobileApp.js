import React, { useState, useEffect } from 'react';
import './mobile.css';
import {
  CssBaseline,
  Box,
  Button,
  Typography
} from '@mui/material';
import { AppProvider, useAppContext } from './context/AppContext';
import ResponsiveThemeProvider from './theme/ResponsiveThemeProvider';
import useDeviceDetection from './hooks/useDeviceDetection';
import MobileNavigation from './components/mobile/MobileNavigation';
import MobileAuth from './components/mobile/MobileAuth';
import MobileLibrary from './components/mobile/MobileLibrary';
import MobileChatInterface from './components/mobile/MobileChatInterface';
import AdminSection from './sections/AdminSection';
import CharacterSection from './sections/CharacterSection';
import ContactSection from './sections/ContactSection';
import chatService from './services/chatService';

/**
 * Mobile app content component that uses the context
 */
function MobileAppContent() {
  // State to track if auth screen should be shown
  const [showAuth, setShowAuth] = useState(false);
  // Chat-related states
  const [chatMessages, setChatMessages] = useState([]);
  const [isChatLoading, setIsChatLoading] = useState(false);
  const [chatError, setChatError] = useState(null);

  const {
    // User management
    currentUser,
    handleUserChange,
    handleLogout,

    // Book management
    books,
    selectedBook,
    selectedBooks,
    handleBookSelect,
    handleDeleteBook,
    handleAddBook,

    // Navigation
    activeSection,
    handleSectionChange,

    // Character management
    selectedCharacter,
    setSelectedCharacter,

    // Suggestions
    refreshingSuggestions,
    handleRefreshSuggestions,
    setRefreshHandler,

    // Companion management
    companions
  } = useAppContext();

  const { isIPhone, getSafeAreaInsets } = useDeviceDetection();
  const safeAreaInsets = getSafeAreaInsets();

  // Format messages from backend to frontend format
  const formatMessagesFromBackend = (backendMessages) => {
    return backendMessages.map(msg => ({
      id: msg.id,
      content: msg.message || msg.content || msg.text || '',
      text: msg.message || msg.content || msg.text || '',
      is_user: msg.is_user === true,
      type: msg.is_user ? 'user' : 'ai',
      timestamp: msg.timestamp || new Date().toISOString()
    }));
  };

  // Load chat history when activeSection changes to 'chat' or when book/character changes
  useEffect(() => {
    const loadChatHistory = async () => {
      if (!selectedBook?.id || !currentUser?.id || activeSection !== 'chat') return;

      try {
        setIsChatLoading(true);
        const history = await chatService.getChatHistory({
          bookId: selectedBook.id,
          characterId: selectedCharacter?.id
        });

        if (history && history.length > 0) {
          const formattedMessages = formatMessagesFromBackend(history);
          setChatMessages(formattedMessages);
        } else {
          setChatMessages([]);
        }
      } catch (err) {
        console.error('Error loading chat history:', err);
        setChatError(err.message);
      } finally {
        setIsChatLoading(false);
      }
    };

    loadChatHistory();
  }, [selectedBook?.id, selectedCharacter?.id, currentUser?.id, activeSection]);

  // Handle sending a chat message
  const handleSendChatMessage = async (message, context) => {
    if (!message || !currentUser?.id) return;

    // Add user message to the UI immediately
    const userMessage = {
      id: Date.now(),
      content: message,
      text: message,
      is_user: true,
      type: 'user',
      timestamp: new Date().toISOString()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setIsChatLoading(true);

    try {
      // Send message to the API
      const response = await chatService.sendMessage({
        message,
        userId: currentUser.id,
        bookId: context?.book?.id,
        characterId: context?.character?.id,
        chatId: context?.chatId,
        bookTitle: context?.book?.title,
        bookAuthor: context?.book?.author
      });

      // Add AI response to the UI
      if (response) {
        const aiMessage = {
          id: response.id || Date.now() + 1,
          content: response.text,
          text: response.text,
          is_user: false,
          type: 'ai',
          timestamp: response.timestamp || new Date().toISOString()
        };

        setChatMessages(prev => [...prev, aiMessage]);
      }
    } catch (err) {
      console.error('Error sending message:', err);
      setChatError(err.message);
    } finally {
      setIsChatLoading(false);
    }
  };

  // Handle clearing chat history
  const handleClearChat = async () => {
    try {
      if (selectedBook?.id) {
        await chatService.clearChatHistory({
          bookId: selectedBook.id
        });
      }
      setChatMessages([]);
    } catch (err) {
      console.error('Error clearing chat:', err);
      setChatError(err.message);
    }
  };

  /**
   * Renders the appropriate section based on activeSection
   */
  const renderContent = () => {
    switch (activeSection) {
      case 'admin':
        return <AdminSection />;
      case 'characters':
        return (
          <CharacterSection 
            selectedCharacter={selectedCharacter} 
            setSelectedCharacter={setSelectedCharacter} 
            currentUser={currentUser} 
          />
        );
      case 'chat':
        return (
          <MobileChatInterface
            selectedBook={selectedBook}
            selectedCharacter={selectedCharacter}
            onCharacterChange={setSelectedCharacter}
            userId={currentUser?.id}
            books={books}
            characters={companions}
            onBookChange={handleBookSelect}
            messages={chatMessages}
            isLoading={isChatLoading}
            onSend={handleSendChatMessage}
            clearChat={handleClearChat}
            handleLogout={handleLogout}
          />
        );
      case 'contact':
        return <ContactSection />;
      default:
        return (
          <MobileLibrary 
            books={books} 
            selectedBooks={selectedBooks} 
            handleBookSelect={handleBookSelect} 
            handleDeleteBook={handleDeleteBook} 
            currentUser={currentUser} 
            handleUserChange={handleUserChange}
            handleAddBook={handleAddBook}
            refreshingSuggestions={refreshingSuggestions}
          />
        );
    }
  };

  // Handle login attempt
  const handleLogin = async (userData) => {
    try {
      // userData comes directly from the authService.login method
      // which returns the user object from Supabase
      if (userData && userData.id) {
        await handleUserChange(userData.id);
        setShowAuth(false);
      } else {
        throw new Error('Invalid user data');
      }
    } catch (error) {
      console.error('Error in handleLogin:', error);
      throw new Error('Login failed: ' + (error.message || 'Unknown error'));
    }
  };

  // Listen for section change events
  useEffect(() => {
    const sectionChangeHandler = (event) => {
      if (event.detail && event.detail.section) {
        const newSection = event.detail.section;
        console.log(`App received section change event: ${newSection}`);

        // Update our app context
        handleSectionChange(newSection);

        // If changing to chat section, make sure we have messages displayed
        if (newSection === 'chat' && selectedBook) {
          // Trigger a chat history load
          const loadChatHistory = async () => {
            try {
              setIsChatLoading(true);
              const history = await chatService.getChatHistory({
                bookId: selectedBook.id,
                characterId: selectedCharacter?.id
              });

              if (history && history.length > 0) {
                const formattedMessages = formatMessagesFromBackend(history);
                setChatMessages(formattedMessages);
              } else {
                setChatMessages([]);
              }
            } catch (err) {
              console.error('Error loading chat history:', err);
              setChatError(err.message);
            } finally {
              setIsChatLoading(false);
            }
          };

          loadChatHistory();
        }
      }
    };

    // Listen for show-auth events
    const showAuthHandler = () => {
      setShowAuth(true);
    };

    window.addEventListener('section-change', sectionChangeHandler);
    window.addEventListener('show-auth', showAuthHandler);

    return () => {
      window.removeEventListener('section-change', sectionChangeHandler);
      window.removeEventListener('show-auth', showAuthHandler);
    };
  }, [handleSectionChange, selectedBook, selectedCharacter]);

  // Handle registration attempt
  const handleRegister = async (response) => {
    try {
      // response comes from authService.register which returns an object
      // with registration information
      
      // If email confirmation is required, show a message
      if (response.needsConfirmation) {
        alert('Please check your email to confirm your registration');
        setShowAuth(false);
        return;
      }
      
      // Otherwise, we should have user data in the response
      if (response.user && response.user.id) {
        await handleUserChange(response.user.id);
        setShowAuth(false);
      } else {
        throw new Error('Invalid registration response');
      }
    } catch (error) {
      console.error('Error in handleRegister:', error);
      throw new Error('Registration failed: ' + (error.message || 'Unknown error'));
    }
  };

  // Calculate bottom padding to account for navigation bar
  const getBottomPadding = () => {
    const baseNavHeight = 64; // Base navigation height
    const safeAreaBottom = isIPhone ? safeAreaInsets.bottom : 0;
    return baseNavHeight + safeAreaBottom;
  };

  // Track whether to show welcome screen
  const [showWelcome, setShowWelcome] = useState(true);

  // Render auth screen, welcome screen, or main content
  if (!currentUser && !showAuth) {
    // If we're not showing the welcome screen, show the library
    if (!showWelcome) {
      return (
        <Box 
          sx={{ 
            display: 'flex',
            flexDirection: 'column',
            minHeight: '100vh',
            bgcolor: 'background.default',
            // Add safe area insets for iPhone
            pt: isIPhone ? `${safeAreaInsets.top}px` : 0,
            // Content area should not extend under the navigation bar
            pb: `${getBottomPadding()}px`,
          }}
          className="has-safe-area-bottom"
        >
          <MobileLibrary 
            books={[]} 
            selectedBooks={[]} 
            handleBookSelect={() => {}}
            handleDeleteBook={() => {}}
            currentUser={null} 
            handleUserChange={handleUserChange}
            handleAddBook={() => {}}
            refreshingSuggestions={false}
          />
          <MobileNavigation 
            activeSection="library" 
            handleSectionChange={() => setShowWelcome(true)}
            currentUser={null}
          />
        </Box>
      );
    }
    
    // Otherwise show welcome screen
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          p: 3,
          textAlign: 'center',
          bgcolor: 'background.default',
          // Add safe area insets for iPhone
          pt: isIPhone ? `${safeAreaInsets.top}px` : 0,
          pb: isIPhone ? `${safeAreaInsets.bottom}px` : 0,
        }}
      >
        <Typography variant="h4" gutterBottom>
          Welcome to BookWorm
        </Typography>
        <Typography variant="body1" paragraph>
          Your AI-powered reading companion
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Explore our example library or sign up for a personalized experience
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={() => setShowAuth(true)}
            sx={{ minHeight: 48, minWidth: 150 }}
          >
            Sign In / Register
          </Button>
          <Button
            variant="outlined"
            color="primary"
            size="large"
            onClick={() => {
              setShowWelcome(false);
              // Also update the app context, but we primarily rely on local state
              handleSectionChange('library');
            }}
            sx={{ minHeight: 48, minWidth: 150 }}
          >
            Explore Example Library
          </Button>
        </Box>
      </Box>
    );
  }

  if (!currentUser && showAuth) {
    return (
      <MobileAuth
        onLogin={handleLogin}
        onRegister={handleRegister}
        onClose={() => setShowAuth(false)}
      />
    );
  }

  return (
    <Box 
      sx={{ 
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        bgcolor: 'background.default',
        // Add safe area insets for iPhone
        pt: isIPhone ? `${safeAreaInsets.top}px` : 0,
        // Content area should not extend under the navigation bar
        pb: `${getBottomPadding()}px`,
      }}
      className="has-safe-area-bottom"
    >
      {renderContent()}
      <MobileNavigation 
        activeSection={activeSection} 
        handleSectionChange={handleSectionChange}
        currentUser={currentUser}
      />
    </Box>
  );
}

/**
 * Main Mobile App component that provides the context and theme
 */
function MobileApp() {
  return (
    <ResponsiveThemeProvider>
      <CssBaseline />
      <AppProvider>
        <MobileAppContent />
      </AppProvider>
    </ResponsiveThemeProvider>
  );
}

export default MobileApp;
