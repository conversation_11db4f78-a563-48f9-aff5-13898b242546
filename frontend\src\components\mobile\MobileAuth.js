import React, { useState } from 'react';
import authService from '../../services/authService';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Tabs,
  Tab,
  IconButton,
  InputAdornment,
  Snackbar,
  Alert,
  CircularProgress
} from '@mui/material';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import useDeviceDetection from '../../hooks/useDeviceDetection';

/**
 * Mobile authentication component for login and registration
 * 
 * @param {Object} props - Component props
 * @param {Function} props.onLogin - Function to handle login
 * @param {Function} props.onRegister - Function to handle registration
 * @param {Function} props.onClose - Function to close the auth screen
 * @returns {React.ReactElement} Mobile authentication component
 */
const MobileAuth = ({ onLogin, onRegister, onClose }) => {
  const [activeTab, setActiveTab] = useState(1);
  const [loginData, setLoginData] = useState({ username: '', password: '' });
  const [registerData, setRegisterData] = useState({ username: '', email: '', password: '', confirmPassword: '' });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const { isIPhone, getSafeAreaInsets } = useDeviceDetection();
  const safeAreaInsets = getSafeAreaInsets();

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    setError(null);
  };

  // Handle login form change
  const handleLoginChange = (e) => {
    const { name, value } = e.target;
    setLoginData(prev => ({ ...prev, [name]: value }));
  };

  // Handle register form change
  const handleRegisterChange = (e) => {
    const { name, value } = e.target;
    setRegisterData(prev => ({ ...prev, [name]: value }));
  };

  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Toggle confirm password visibility
  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  // Handle login submission
  const handleLoginSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    
    if (!loginData.username.trim() || !loginData.password.trim()) {
      setError('Please enter both username and password');
      return;
    }
    
    try {
      setLoading(true);
      const user = await authService.login(
        loginData.username.trim(),
        loginData.password.trim()
      );
      
      if (user) {
        await onLogin(user);
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (err) {
      console.error('Login error:', err);
      setError(err.message || 'Login failed. Please check your credentials.');
    } finally {
      setLoading(false);
    }
  };

  // Handle registration submission
  const handleRegisterSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    
    if (!registerData.username.trim()) {
      setError('Please enter a username');
      return;
    }
    if (!registerData.email.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(registerData.email)) {
      setError('Please enter a valid email address');
      return;
    }
    if (!registerData.password.trim()) {
      setError('Please enter a password');
      return;
    }
    if (registerData.password !== registerData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }
    
    try {
      setLoading(true);
      
      const response = await authService.register(
        registerData.email.trim(),
        registerData.password.trim(),
        registerData.username.trim()
      );
      
      if (response) {
        await onRegister(response);
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (err) {
      console.error('Registration error:', err);
      setError(err.message || 'Registration failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        p: 2,
        bgcolor: 'background.default',
        // Add padding for iPhone safe areas
        pt: isIPhone ? `${safeAreaInsets.top}px` : 0,
        pb: isIPhone ? `${safeAreaInsets.bottom}px` : 0,
      }}
    >
      <Paper
        elevation={3}
        sx={{
          width: '100%',
          maxWidth: 400,
          p: 3,
          borderRadius: 2,
        }}
      >
        <Typography variant="h5" align="center" gutterBottom>
          BookWorm
        </Typography>
        
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{ mb: 3 }}
        >
          <Tab label="Login" />
          <Tab label="Register" />
        </Tabs>
        
        {activeTab === 0 ? (
          // Login Form
          <form onSubmit={handleLoginSubmit}>
            <TextField
              fullWidth
              label="Username"
              name="username"
              value={loginData.username}
              onChange={handleLoginChange}
              margin="normal"
              variant="outlined"
              autoComplete="username"
              inputProps={{ 
                style: { fontSize: '16px' } 
              }}
            />
            <TextField
              fullWidth
              label="Password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              value={loginData.password}
              onChange={handleLoginChange}
              margin="normal"
              variant="outlined"
              autoComplete="current-password"
              inputProps={{ 
                style: { fontSize: '16px' } // Prevents iOS zoom
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={togglePasswordVisibility}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <Button
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              disabled={loading}
              sx={{ mt: 3, mb: 2, height: 48 }}
            >
              {loading ? <CircularProgress size={24} /> : 'Login'}
            </Button>
          </form>
        ) : (
          // Register Form
          <form onSubmit={handleRegisterSubmit}>
            <TextField
              fullWidth
              label="Username"
              name="username"
              value={registerData.username}
              onChange={handleRegisterChange}
              margin="normal"
              variant="outlined"
              autoComplete="username"
              inputProps={{ 
                style: { fontSize: '16px' } 
              }}
            />
            <TextField
              fullWidth
              label="Email"
              name="email"
              type="email"
              value={registerData.email}
              onChange={handleRegisterChange}
              margin="normal"
              variant="outlined"
              autoComplete="email"
              inputProps={{ 
                style: { fontSize: '16px' } // Prevents iOS zoom
              }}
              disabled={loading}
            />
            <TextField
              fullWidth
              label="Password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              value={registerData.password}
              onChange={handleRegisterChange}
              margin="normal"
              variant="outlined"
              autoComplete="new-password"
              inputProps={{ 
                style: { fontSize: '16px' } 
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={togglePasswordVisibility}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              fullWidth
              label="Confirm Password"
              name="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              value={registerData.confirmPassword}
              onChange={handleRegisterChange}
              margin="normal"
              variant="outlined"
              autoComplete="new-password"
              inputProps={{ 
                style: { fontSize: '16px' } // Prevents iOS zoom
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle confirm password visibility"
                      onClick={toggleConfirmPasswordVisibility}
                      edge="end"
                    >
                      {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <Button
              fullWidth
              variant="contained"
              color="primary"
              type="submit"
              disabled={loading}
              sx={{ mt: 3, mb: 2, height: 48 }}
            >
              {loading ? <CircularProgress size={24} /> : 'Register'}
            </Button>
          </form>
        )}
      </Paper>
      
      {/* Error message */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setError(null)} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default MobileAuth;
