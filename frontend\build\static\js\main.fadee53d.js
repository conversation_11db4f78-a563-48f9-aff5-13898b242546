/*! For license information please see main.fadee53d.js.LICENSE.txt */
(()=>{var e={3803:(e,t,n)=>{"use strict";n.d(t,{A:()=>oe});var r=function(){function e(e){var t=this;this._insertTag=function(e){var n;n=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,n),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{n.insertRule(e,n.cssRules.length)}catch(r){}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach((function(e){var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)})),this.tags=[],this.ctr=0},e}(),o=Math.abs,a=String.fromCharCode,i=Object.assign;function l(e){return e.trim()}function s(e,t,n){return e.replace(t,n)}function c(e,t){return e.indexOf(t)}function u(e,t){return 0|e.charCodeAt(t)}function d(e,t,n){return e.slice(t,n)}function p(e){return e.length}function f(e){return e.length}function m(e,t){return t.push(e),e}var h=1,g=1,v=0,y=0,b=0,x="";function w(e,t,n,r,o,a,i){return{value:e,root:t,parent:n,type:r,props:o,children:a,line:h,column:g,length:i,return:""}}function A(e,t){return i(w("",null,null,"",null,null,0),e,{length:-e.length},t)}function k(){return b=y>0?u(x,--y):0,g--,10===b&&(g=1,h--),b}function S(){return b=y<v?u(x,y++):0,g++,10===b&&(g=1,h++),b}function C(){return u(x,y)}function E(){return y}function R(e,t){return d(x,e,t)}function P(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function M(e){return h=g=1,v=p(x=e),y=0,[]}function O(e){return x="",e}function T(e){return l(R(y-1,N(91===e?e+2:40===e?e+1:e)))}function j(e){for(;(b=C())&&b<33;)S();return P(e)>2||P(b)>3?"":" "}function I(e,t){for(;--t&&S()&&!(b<48||b>102||b>57&&b<65||b>70&&b<97););return R(e,E()+(t<6&&32==C()&&32==S()))}function N(e){for(;S();)switch(b){case e:return y;case 34:case 39:34!==e&&39!==e&&N(b);break;case 40:41===e&&N(e);break;case 92:S()}return y}function $(e,t){for(;S()&&e+b!==57&&(e+b!==84||47!==C()););return"/*"+R(t,y-1)+"*"+a(47===e?e:S())}function z(e){for(;!P(C());)S();return R(e,y)}var L="-ms-",_="-moz-",F="-webkit-",D="comm",W="rule",B="decl",V="@keyframes";function H(e,t){for(var n="",r=f(e),o=0;o<r;o++)n+=t(e[o],o,e,t)||"";return n}function U(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case B:return e.return=e.return||e.value;case D:return"";case V:return e.return=e.value+"{"+H(e.children,r)+"}";case W:e.value=e.props.join(",")}return p(n=H(e.children,r))?e.return=e.value+"{"+n+"}":""}function X(e){return O(K("",null,null,null,[""],e=M(e),0,[0],e))}function K(e,t,n,r,o,i,l,d,f){for(var h=0,g=0,v=l,y=0,b=0,x=0,w=1,A=1,R=1,P=0,M="",O=o,N=i,L=r,_=M;A;)switch(x=P,P=S()){case 40:if(108!=x&&58==u(_,v-1)){-1!=c(_+=s(T(P),"&","&\f"),"&\f")&&(R=-1);break}case 34:case 39:case 91:_+=T(P);break;case 9:case 10:case 13:case 32:_+=j(x);break;case 92:_+=I(E()-1,7);continue;case 47:switch(C()){case 42:case 47:m(G($(S(),E()),t,n),f);break;default:_+="/"}break;case 123*w:d[h++]=p(_)*R;case 125*w:case 59:case 0:switch(P){case 0:case 125:A=0;case 59+g:-1==R&&(_=s(_,/\f/g,"")),b>0&&p(_)-v&&m(b>32?Q(_+";",r,n,v-1):Q(s(_," ","")+";",r,n,v-2),f);break;case 59:_+=";";default:if(m(L=q(_,t,n,h,g,o,d,M,O=[],N=[],v),i),123===P)if(0===g)K(_,t,L,L,O,i,v,d,N);else switch(99===y&&110===u(_,3)?100:y){case 100:case 108:case 109:case 115:K(e,L,L,r&&m(q(e,L,L,0,0,o,d,M,o,O=[],v),N),o,N,v,d,r?O:N);break;default:K(_,L,L,L,[""],N,0,d,N)}}h=g=b=0,w=R=1,M=_="",v=l;break;case 58:v=1+p(_),b=x;default:if(w<1)if(123==P)--w;else if(125==P&&0==w++&&125==k())continue;switch(_+=a(P),P*w){case 38:R=g>0?1:(_+="\f",-1);break;case 44:d[h++]=(p(_)-1)*R,R=1;break;case 64:45===C()&&(_+=T(S())),y=C(),g=v=p(M=_+=z(E())),P++;break;case 45:45===x&&2==p(_)&&(w=0)}}return i}function q(e,t,n,r,a,i,c,u,p,m,h){for(var g=a-1,v=0===a?i:[""],y=f(v),b=0,x=0,A=0;b<r;++b)for(var k=0,S=d(e,g+1,g=o(x=c[b])),C=e;k<y;++k)(C=l(x>0?v[k]+" "+S:s(S,/&\f/g,v[k])))&&(p[A++]=C);return w(e,t,n,0===a?W:u,p,m,h)}function G(e,t,n){return w(e,t,n,D,a(b),d(e,2,-2),0)}function Q(e,t,n,r){return w(e,t,n,B,d(e,0,r),d(e,r+1,-1),r)}var Y=function(e,t,n){for(var r=0,o=0;r=o,o=C(),38===r&&12===o&&(t[n]=1),!P(o);)S();return R(e,y)},J=function(e,t){return O(function(e,t){var n=-1,r=44;do{switch(P(r)){case 0:38===r&&12===C()&&(t[n]=1),e[n]+=Y(y-1,t,n);break;case 2:e[n]+=T(r);break;case 4:if(44===r){e[++n]=58===C()?"&\f":"",t[n]=e[n].length;break}default:e[n]+=a(r)}}while(r=S());return e}(M(e),t))},Z=new WeakMap,ee=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,n=e.parent,r=e.column===n.column&&e.line===n.line;"rule"!==n.type;)if(!(n=n.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||Z.get(n))&&!r){Z.set(e,!0);for(var o=[],a=J(t,o),i=n.props,l=0,s=0;l<a.length;l++)for(var c=0;c<i.length;c++,s++)e.props[s]=o[l]?a[l].replace(/&\f/g,i[c]):i[c]+" "+a[l]}}},te=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}};function ne(e,t){switch(function(e,t){return 45^u(e,0)?(((t<<2^u(e,0))<<2^u(e,1))<<2^u(e,2))<<2^u(e,3):0}(e,t)){case 5103:return F+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return F+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return F+e+_+e+L+e+e;case 6828:case 4268:return F+e+L+e+e;case 6165:return F+e+L+"flex-"+e+e;case 5187:return F+e+s(e,/(\w+).+(:[^]+)/,F+"box-$1$2"+L+"flex-$1$2")+e;case 5443:return F+e+L+"flex-item-"+s(e,/flex-|-self/,"")+e;case 4675:return F+e+L+"flex-line-pack"+s(e,/align-content|flex-|-self/,"")+e;case 5548:return F+e+L+s(e,"shrink","negative")+e;case 5292:return F+e+L+s(e,"basis","preferred-size")+e;case 6060:return F+"box-"+s(e,"-grow","")+F+e+L+s(e,"grow","positive")+e;case 4554:return F+s(e,/([^-])(transform)/g,"$1"+F+"$2")+e;case 6187:return s(s(s(e,/(zoom-|grab)/,F+"$1"),/(image-set)/,F+"$1"),e,"")+e;case 5495:case 3959:return s(e,/(image-set\([^]*)/,F+"$1$`$1");case 4968:return s(s(e,/(.+:)(flex-)?(.*)/,F+"box-pack:$3"+L+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+F+e+e;case 4095:case 3583:case 4068:case 2532:return s(e,/(.+)-inline(.+)/,F+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(p(e)-1-t>6)switch(u(e,t+1)){case 109:if(45!==u(e,t+4))break;case 102:return s(e,/(.+:)(.+)-([^]+)/,"$1"+F+"$2-$3$1"+_+(108==u(e,t+3)?"$3":"$2-$3"))+e;case 115:return~c(e,"stretch")?ne(s(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(115!==u(e,t+1))break;case 6444:switch(u(e,p(e)-3-(~c(e,"!important")&&10))){case 107:return s(e,":",":"+F)+e;case 101:return s(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+F+(45===u(e,14)?"inline-":"")+"box$3$1"+F+"$2$3$1"+L+"$2box$3")+e}break;case 5936:switch(u(e,t+11)){case 114:return F+e+L+s(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return F+e+L+s(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return F+e+L+s(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return F+e+L+e+e}return e}var re=[function(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case B:e.return=ne(e.value,e.length);break;case V:return H([A(e,{value:s(e.value,"@","@"+F)})],r);case W:if(e.length)return function(e,t){return e.map(t).join("")}(e.props,(function(t){switch(function(e,t){return(e=t.exec(e))?e[0]:e}(t,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return H([A(e,{props:[s(t,/:(read-\w+)/,":-moz-$1")]})],r);case"::placeholder":return H([A(e,{props:[s(t,/:(plac\w+)/,":"+F+"input-$1")]}),A(e,{props:[s(t,/:(plac\w+)/,":-moz-$1")]}),A(e,{props:[s(t,/:(plac\w+)/,L+"input-$1")]})],r)}return""}))}}],oe=function(e){var t=e.key;if("css"===t){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var o,a,i=e.stylisPlugins||re,l={},s=[];o=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var t=e.getAttribute("data-emotion").split(" "),n=1;n<t.length;n++)l[t[n]]=!0;s.push(e)}));var c,u,d=[U,(u=function(e){c.insert(e)},function(e){e.root||(e=e.return)&&u(e)})],p=function(e){var t=f(e);return function(n,r,o,a){for(var i="",l=0;l<t;l++)i+=e[l](n,r,o,a)||"";return i}}([ee,te].concat(i,d));a=function(e,t,n,r){c=n,H(X(e?e+"{"+t.styles+"}":t.styles),p),r&&(m.inserted[t.name]=!0)};var m={key:t,sheet:new r({key:t,container:o,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:l,registered:{},insert:a};return m.sheet.hydrate(s),m}},918:(e,t,n)=>{"use strict";function r(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}n.d(t,{A:()=>r})},4575:(e,t,n)=>{"use strict";n.d(t,{C:()=>i,T:()=>s,w:()=>l});var r=n(5043),o=n(3803),a=(n(6598),n(9436),r.createContext("undefined"!==typeof HTMLElement?(0,o.A)({key:"css"}):null)),i=a.Provider,l=function(e){return(0,r.forwardRef)((function(t,n){var o=(0,r.useContext)(a);return e(t,o,n)}))},s=r.createContext({})},3290:(e,t,n)=>{"use strict";n.d(t,{AH:()=>c,i7:()=>u,mL:()=>s});var r=n(4575),o=n(5043),a=n(1722),i=n(9436),l=n(6598),s=(n(3803),n(219),(0,r.w)((function(e,t){var n=e.styles,s=(0,l.J)([n],void 0,o.useContext(r.T)),c=o.useRef();return(0,i.i)((function(){var e=t.key+"-global",n=new t.sheet.constructor({key:e,nonce:t.sheet.nonce,container:t.sheet.container,speedy:t.sheet.isSpeedy}),r=!1,o=document.querySelector('style[data-emotion="'+e+" "+s.name+'"]');return t.sheet.tags.length&&(n.before=t.sheet.tags[0]),null!==o&&(r=!0,o.setAttribute("data-emotion",e),n.hydrate([o])),c.current=[n,r],function(){n.flush()}}),[t]),(0,i.i)((function(){var e=c.current,n=e[0];if(e[1])e[1]=!1;else{if(void 0!==s.next&&(0,a.sk)(t,s.next,!0),n.tags.length){var r=n.tags[n.tags.length-1].nextElementSibling;n.before=r,n.flush()}t.insert("",s,n,!1)}}),[t,s.name]),null})));function c(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,l.J)(t)}var u=function(){var e=c.apply(void 0,arguments),t="animation-"+e.name;return{name:t,styles:"@keyframes "+t+"{"+e.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}},6598:(e,t,n)=>{"use strict";n.d(t,{J:()=>g});var r={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},o=n(918),a=!1,i=/[A-Z]|^ms/g,l=/_EMO_([^_]+?)_([^]*?)_EMO_/g,s=function(e){return 45===e.charCodeAt(1)},c=function(e){return null!=e&&"boolean"!==typeof e},u=(0,o.A)((function(e){return s(e)?e:e.replace(i,"-$&").toLowerCase()})),d=function(e,t){switch(e){case"animation":case"animationName":if("string"===typeof t)return t.replace(l,(function(e,t,n){return m={name:t,styles:n,next:m},t}))}return 1===r[e]||s(e)||"number"!==typeof t||0===t?t:t+"px"},p="Component selectors can only be used in conjunction with @emotion/babel-plugin, the swc Emotion plugin, or another Emotion-aware compiler transform.";function f(e,t,n){if(null==n)return"";var r=n;if(void 0!==r.__emotion_styles)return r;switch(typeof n){case"boolean":return"";case"object":var o=n;if(1===o.anim)return m={name:o.name,styles:o.styles,next:m},o.name;var i=n;if(void 0!==i.styles){var l=i.next;if(void 0!==l)for(;void 0!==l;)m={name:l.name,styles:l.styles,next:m},l=l.next;return i.styles+";"}return function(e,t,n){var r="";if(Array.isArray(n))for(var o=0;o<n.length;o++)r+=f(e,t,n[o])+";";else for(var i in n){var l=n[i];if("object"!==typeof l){var s=l;null!=t&&void 0!==t[s]?r+=i+"{"+t[s]+"}":c(s)&&(r+=u(i)+":"+d(i,s)+";")}else{if("NO_COMPONENT_SELECTOR"===i&&a)throw new Error(p);if(!Array.isArray(l)||"string"!==typeof l[0]||null!=t&&void 0!==t[l[0]]){var m=f(e,t,l);switch(i){case"animation":case"animationName":r+=u(i)+":"+m+";";break;default:r+=i+"{"+m+"}"}}else for(var h=0;h<l.length;h++)c(l[h])&&(r+=u(i)+":"+d(i,l[h])+";")}}return r}(e,t,n);case"function":if(void 0!==e){var s=m,h=n(e);return m=s,f(e,t,h)}}var g=n;if(null==t)return g;var v=t[g];return void 0!==v?v:g}var m,h=/label:\s*([^\s;{]+)\s*(;|$)/g;function g(e,t,n){if(1===e.length&&"object"===typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var r=!0,o="";m=void 0;var a=e[0];null==a||void 0===a.raw?(r=!1,o+=f(n,t,a)):o+=a[0];for(var i=1;i<e.length;i++){if(o+=f(n,t,e[i]),r)o+=a[i]}h.lastIndex=0;for(var l,s="";null!==(l=h.exec(o));)s+="-"+l[1];var c=function(e){for(var t,n=0,r=0,o=e.length;o>=4;++r,o-=4)t=1540483477*(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))+(59797*(t>>>16)<<16),n=1540483477*(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^1540483477*(65535&n)+(59797*(n>>>16)<<16);switch(o){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n=1540483477*(65535&(n^=255&e.charCodeAt(r)))+(59797*(n>>>16)<<16)}return(((n=1540483477*(65535&(n^=n>>>13))+(59797*(n>>>16)<<16))^n>>>15)>>>0).toString(36)}(o)+s;return{name:c,styles:o,next:m}}},9436:(e,t,n)=>{"use strict";var r;n.d(t,{i:()=>l,s:()=>i});var o=n(5043),a=!!(r||(r=n.t(o,2))).useInsertionEffect&&(r||(r=n.t(o,2))).useInsertionEffect,i=a||function(e){return e()},l=a||o.useLayoutEffect},1722:(e,t,n)=>{"use strict";n.d(t,{Rk:()=>r,SF:()=>o,sk:()=>a});function r(e,t,n){var r="";return n.split(" ").forEach((function(n){void 0!==e[n]?t.push(e[n]+";"):n&&(r+=n+" ")})),r}var o=function(e,t,n){var r=e.key+"-"+t.name;!1===n&&void 0===e.registered[r]&&(e.registered[r]=t.styles)},a=function(e,t,n){o(e,t,n);var r=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var a=t;do{e.insert(t===a?"."+r:"",a,e.sheet,!0),a=a.next}while(void 0!==a)}}},2505:(e,t,n)=>{"use strict";var r=n(4994);t.A=void 0;var o=r(n(39)),a=n(579);t.A=(0,o.default)((0,a.jsx)("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"}),"Add")},7201:(e,t,n)=>{"use strict";var r=n(4994);t.A=void 0;var o=r(n(39)),a=n(579);t.A=(0,o.default)((0,a.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m5 11h-4v4h-2v-4H7v-2h4V7h2v4h4z"}),"AddCircle")},5382:(e,t,n)=>{"use strict";var r=n(4994);t.A=void 0;var o=r(n(39)),a=n(579);t.A=(0,o.default)((0,a.jsx)("path",{d:"M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"}),"Check")},5896:(e,t,n)=>{"use strict";var r=n(4994);t.A=void 0;var o=r(n(39)),a=n(579);t.A=(0,o.default)((0,a.jsx)("path",{d:"M15.41 7.41 14 6l-6 6 6 6 1.41-1.41L10.83 12z"}),"ChevronLeft")},1707:(e,t,n)=>{"use strict";var r=n(4994);t.A=void 0;var o=r(n(39)),a=n(579);t.A=(0,o.default)((0,a.jsx)("path",{d:"M10 6 8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"}),"ChevronRight")},3471:(e,t,n)=>{"use strict";var r=n(4994);t.A=void 0;var o=r(n(39)),a=n(579);t.A=(0,o.default)((0,a.jsx)("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z"}),"Delete")},5540:(e,t,n)=>{"use strict";var r=n(4994);t.A=void 0;var o=r(n(39)),a=n(579);t.A=(0,o.default)((0,a.jsx)("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.9959.9959 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"}),"Edit")},1958:(e,t,n)=>{"use strict";var r=n(4994);t.A=void 0;var o=r(n(39)),a=n(579);t.A=(0,o.default)((0,a.jsx)("path",{d:"M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9m-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8z"}),"History")},4536:(e,t,n)=>{"use strict";var r=n(4994);t.A=void 0;var o=r(n(39)),a=n(579);t.A=(0,o.default)((0,a.jsx)("path",{d:"M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"}),"KeyboardArrowDown")},9810:(e,t,n)=>{"use strict";var r=n(4994);t.A=void 0;var o=r(n(39)),a=n(579);t.A=(0,o.default)([(0,a.jsx)("path",{d:"M21 5c-1.11-.35-2.33-.5-3.5-.5-1.95 0-4.05.4-5.5 1.5-1.45-1.1-3.55-1.5-5.5-1.5S2.45 4.9 1 6v14.65c0 .25.25.5.5.5.1 0 .15-.05.25-.05C3.1 20.45 5.05 20 6.5 20c1.95 0 4.05.4 5.5 1.5 1.35-.85 3.8-1.5 5.5-1.5 1.65 0 3.35.3 4.75 1.05.1.05.15.05.25.05.25 0 .5-.25.5-.5V6c-.6-.45-1.25-.75-2-1m0 13.5c-1.1-.35-2.3-.5-3.5-.5-1.7 0-4.15.65-5.5 1.5V8c1.35-.85 3.8-1.5 5.5-1.5 1.2 0 2.4.15 3.5.5z"},"0"),(0,a.jsx)("path",{d:"M17.5 10.5c.88 0 1.73.09 2.5.26V9.24c-.79-.15-1.64-.24-2.5-.24-1.7 0-3.24.29-4.5.83v1.66c1.13-.64 2.7-.99 4.5-.99M13 12.49v1.66c1.13-.64 2.7-.99 4.5-.99.88 0 1.73.09 2.5.26V11.9c-.79-.15-1.64-.24-2.5-.24-1.7 0-3.24.3-4.5.83m4.5 1.84c-1.7 0-3.24.29-4.5.83v1.66c1.13-.64 2.7-.99 4.5-.99.88 0 1.73.09 2.5.26v-1.52c-.79-.16-1.64-.24-2.5-.24"},"1")],"MenuBook")},8625:(e,t,n)=>{"use strict";var r=n(4994);t.A=void 0;var o=r(n(39)),a=n(579);t.A=(0,o.default)((0,a.jsx)("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"}),"Refresh")},5678:(e,t,n)=>{"use strict";var r=n(4994);t.A=void 0;var o=r(n(39)),a=n(579);t.A=(0,o.default)((0,a.jsx)("path",{d:"M2.01 21 23 12 2.01 3 2 10l15 2-15 2z"}),"Send")},39:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r.createSvgIcon}});var r=n(4421)},8206:(e,t,n)=>{"use strict";n.d(t,{b:()=>o});n(5043);var r=n(3654);n(579);function o(e){return(0,r.b)(e)}},8279:(e,t,n)=>{"use strict";n.d(t,{A:()=>F});var r=n(8168),o=n(8587),a=n(7868),i=n(9172),l=n(7758),s=n(8812),c=n(8280);var u=n(7266);const d={black:"#000",white:"#fff"},p={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},f={50:"#f3e5f5",100:"#e1bee7",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",600:"#8e24aa",700:"#7b1fa2",800:"#6a1b9a",900:"#4a148c",A100:"#ea80fc",A200:"#e040fb",A400:"#d500f9",A700:"#aa00ff"},m={50:"#ffebee",100:"#ffcdd2",200:"#ef9a9a",300:"#e57373",400:"#ef5350",500:"#f44336",600:"#e53935",700:"#d32f2f",800:"#c62828",900:"#b71c1c",A100:"#ff8a80",A200:"#ff5252",A400:"#ff1744",A700:"#d50000"},h={50:"#fff3e0",100:"#ffe0b2",200:"#ffcc80",300:"#ffb74d",400:"#ffa726",500:"#ff9800",600:"#fb8c00",700:"#f57c00",800:"#ef6c00",900:"#e65100",A100:"#ffd180",A200:"#ffab40",A400:"#ff9100",A700:"#ff6d00"},g={50:"#e3f2fd",100:"#bbdefb",200:"#90caf9",300:"#64b5f6",400:"#42a5f5",500:"#2196f3",600:"#1e88e5",700:"#1976d2",800:"#1565c0",900:"#0d47a1",A100:"#82b1ff",A200:"#448aff",A400:"#2979ff",A700:"#2962ff"},v={50:"#e1f5fe",100:"#b3e5fc",200:"#81d4fa",300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",600:"#039be5",700:"#0288d1",800:"#0277bd",900:"#01579b",A100:"#80d8ff",A200:"#40c4ff",A400:"#00b0ff",A700:"#0091ea"},y={50:"#e8f5e9",100:"#c8e6c9",200:"#a5d6a7",300:"#81c784",400:"#66bb6a",500:"#4caf50",600:"#43a047",700:"#388e3c",800:"#2e7d32",900:"#1b5e20",A100:"#b9f6ca",A200:"#69f0ae",A400:"#00e676",A700:"#00c853"},b=["mode","contrastThreshold","tonalOffset"],x={text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:d.white,default:d.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}},w={text:{primary:d.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:d.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}};function A(e,t,n,r){const o=r.light||r,a=r.dark||1.5*r;e[t]||(e.hasOwnProperty(n)?e[t]=e[n]:"light"===t?e.light=(0,u.a)(e.main,o):"dark"===t&&(e.dark=(0,u.e$)(e.main,a)))}function k(e){const{mode:t="light",contrastThreshold:n=3,tonalOffset:l=.2}=e,s=(0,o.A)(e,b),c=e.primary||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:g[200],light:g[50],dark:g[400]}:{main:g[700],light:g[400],dark:g[800]}}(t),k=e.secondary||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:f[200],light:f[50],dark:f[400]}:{main:f[500],light:f[300],dark:f[700]}}(t),S=e.error||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:m[500],light:m[300],dark:m[700]}:{main:m[700],light:m[400],dark:m[800]}}(t),C=e.info||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:v[400],light:v[300],dark:v[700]}:{main:v[700],light:v[500],dark:v[900]}}(t),E=e.success||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:y[400],light:y[300],dark:y[700]}:{main:y[800],light:y[500],dark:y[900]}}(t),R=e.warning||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:h[400],light:h[300],dark:h[700]}:{main:"#ed6c02",light:h[500],dark:h[900]}}(t);function P(e){return(0,u.eM)(e,w.text.primary)>=n?w.text.primary:x.text.primary}const M=e=>{let{color:t,name:n,mainShade:o=500,lightShade:i=300,darkShade:s=700}=e;if(t=(0,r.A)({},t),!t.main&&t[o]&&(t.main=t[o]),!t.hasOwnProperty("main"))throw new Error((0,a.A)(11,n?` (${n})`:"",o));if("string"!==typeof t.main)throw new Error((0,a.A)(12,n?` (${n})`:"",JSON.stringify(t.main)));return A(t,"light",i,l),A(t,"dark",s,l),t.contrastText||(t.contrastText=P(t.main)),t},O={dark:w,light:x};return(0,i.A)((0,r.A)({common:(0,r.A)({},d),mode:t,primary:M({color:c,name:"primary"}),secondary:M({color:k,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:M({color:S,name:"error"}),warning:M({color:R,name:"warning"}),info:M({color:C,name:"info"}),success:M({color:E,name:"success"}),grey:p,contrastThreshold:n,getContrastText:P,augmentColor:M,tonalOffset:l},O[t]),s)}const S=["fontFamily","fontSize","fontWeightLight","fontWeightRegular","fontWeightMedium","fontWeightBold","htmlFontSize","allVariants","pxToRem"];const C={textTransform:"uppercase"},E='"Roboto", "Helvetica", "Arial", sans-serif';function R(e,t){const n="function"===typeof t?t(e):t,{fontFamily:a=E,fontSize:l=14,fontWeightLight:s=300,fontWeightRegular:c=400,fontWeightMedium:u=500,fontWeightBold:d=700,htmlFontSize:p=16,allVariants:f,pxToRem:m}=n,h=(0,o.A)(n,S);const g=l/14,v=m||(e=>e/p*g+"rem"),y=(e,t,n,o,i)=>{return(0,r.A)({fontFamily:a,fontWeight:e,fontSize:v(t),lineHeight:n},a===E?{letterSpacing:(l=o/t,Math.round(1e5*l)/1e5)+"em"}:{},i,f);var l},b={h1:y(s,96,1.167,-1.5),h2:y(s,60,1.2,-.5),h3:y(c,48,1.167,0),h4:y(c,34,1.235,.25),h5:y(c,24,1.334,0),h6:y(u,20,1.6,.15),subtitle1:y(c,16,1.75,.15),subtitle2:y(u,14,1.57,.1),body1:y(c,16,1.5,.15),body2:y(c,14,1.43,.15),button:y(u,14,1.75,.4,C),caption:y(c,12,1.66,.4),overline:y(c,12,2.66,1,C),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return(0,i.A)((0,r.A)({htmlFontSize:p,pxToRem:v,fontFamily:a,fontSize:l,fontWeightLight:s,fontWeightRegular:c,fontWeightMedium:u,fontWeightBold:d},b),h,{clone:!1})}function P(){return[`${arguments.length<=0?void 0:arguments[0]}px ${arguments.length<=1?void 0:arguments[1]}px ${arguments.length<=2?void 0:arguments[2]}px ${arguments.length<=3?void 0:arguments[3]}px rgba(0,0,0,0.2)`,`${arguments.length<=4?void 0:arguments[4]}px ${arguments.length<=5?void 0:arguments[5]}px ${arguments.length<=6?void 0:arguments[6]}px ${arguments.length<=7?void 0:arguments[7]}px rgba(0,0,0,0.14)`,`${arguments.length<=8?void 0:arguments[8]}px ${arguments.length<=9?void 0:arguments[9]}px ${arguments.length<=10?void 0:arguments[10]}px ${arguments.length<=11?void 0:arguments[11]}px rgba(0,0,0,0.12)`].join(",")}const M=["none",P(0,2,1,-1,0,1,1,0,0,1,3,0),P(0,3,1,-2,0,2,2,0,0,1,5,0),P(0,3,3,-2,0,3,4,0,0,1,8,0),P(0,2,4,-1,0,4,5,0,0,1,10,0),P(0,3,5,-1,0,5,8,0,0,1,14,0),P(0,3,5,-1,0,6,10,0,0,1,18,0),P(0,4,5,-2,0,7,10,1,0,2,16,1),P(0,5,5,-3,0,8,10,1,0,3,14,2),P(0,5,6,-3,0,9,12,1,0,3,16,2),P(0,6,6,-3,0,10,14,1,0,4,18,3),P(0,6,7,-4,0,11,15,1,0,4,20,3),P(0,7,8,-4,0,12,17,2,0,5,22,4),P(0,7,8,-4,0,13,19,2,0,5,24,4),P(0,7,9,-4,0,14,21,2,0,5,26,4),P(0,8,9,-5,0,15,22,2,0,6,28,5),P(0,8,10,-5,0,16,24,2,0,6,30,5),P(0,8,11,-5,0,17,26,2,0,6,32,5),P(0,9,11,-5,0,18,28,2,0,7,34,6),P(0,9,12,-6,0,19,29,2,0,7,36,6),P(0,10,13,-6,0,20,31,3,0,8,38,7),P(0,10,13,-6,0,21,33,3,0,8,40,7),P(0,10,14,-6,0,22,35,3,0,8,42,7),P(0,11,14,-7,0,23,36,3,0,9,44,8),P(0,11,15,-7,0,24,38,3,0,9,46,8)],O=["duration","easing","delay"],T={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},j={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function I(e){return`${Math.round(e)}ms`}function N(e){if(!e)return 0;const t=e/36;return Math.round(10*(4+15*t**.25+t/5))}function $(e){const t=(0,r.A)({},T,e.easing),n=(0,r.A)({},j,e.duration);return(0,r.A)({getAutoHeightDuration:N,create:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["all"],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{duration:a=n.standard,easing:i=t.easeInOut,delay:l=0}=r;(0,o.A)(r,O);return(Array.isArray(e)?e:[e]).map((e=>`${e} ${"string"===typeof a?a:I(a)} ${i} ${"string"===typeof l?l:I(l)}`)).join(",")}},e,{easing:t,duration:n})}const z={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500},L=["breakpoints","mixins","spacing","palette","transitions","typography","shape"];function _(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{mixins:t={},palette:n={},transitions:u={},typography:d={}}=e,p=(0,o.A)(e,L);if(e.vars)throw new Error((0,a.A)(18));const f=k(n),m=(0,c.A)(e);let h=(0,i.A)(m,{mixins:(g=m.breakpoints,v=t,(0,r.A)({toolbar:{minHeight:56,[g.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[g.up("sm")]:{minHeight:64}}},v)),palette:f,shadows:M.slice(),typography:R(f,d),transitions:$(u),zIndex:(0,r.A)({},z)});var g,v;h=(0,i.A)(h,p);for(var y=arguments.length,b=new Array(y>1?y-1:0),x=1;x<y;x++)b[x-1]=arguments[x];return h=b.reduce(((e,t)=>(0,i.A)(e,t)),h),h.unstable_sxConfig=(0,r.A)({},l.A,null==p?void 0:p.unstable_sxConfig),h.unstable_sx=function(e){return(0,s.A)({sx:e,theme:this})},h}const F=_},5170:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r=(0,n(8279).A)()},3375:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r="$$material"},1475:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(7123);const o=e=>(0,r.A)(e)&&"classes"!==e},7123:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r=function(e){return"ownerState"!==e&&"theme"!==e&&"sx"!==e&&"as"!==e}},4535:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>l});var r=n(8052),o=n(5170),a=n(3375),i=n(1475);const l=(0,r.Ay)({themeId:a.A,defaultTheme:o.A,rootShouldForwardProp:i.A})},6803:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r=n(7598).A},9662:(e,t,n)=>{"use strict";n.d(t,{A:()=>b});var r=n(8168),o=n(5043),a=n(8587),i=n(8387),l=n(8610),s=n(6803),c=n(8206),u=n(4535),d=n(2532),p=n(2372);function f(e){return(0,p.Ay)("MuiSvgIcon",e)}(0,d.A)("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);var m=n(579);const h=["children","className","color","component","fontSize","htmlColor","inheritViewBox","titleAccess","viewBox"],g=(0,u.Ay)("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,"inherit"!==n.color&&t[`color${(0,s.A)(n.color)}`],t[`fontSize${(0,s.A)(n.fontSize)}`]]}})((e=>{let{theme:t,ownerState:n}=e;var r,o,a,i,l,s,c,u,d,p,f,m,h;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",fill:n.hasSvgAsChild?void 0:"currentColor",flexShrink:0,transition:null==(r=t.transitions)||null==(o=r.create)?void 0:o.call(r,"fill",{duration:null==(a=t.transitions)||null==(a=a.duration)?void 0:a.shorter}),fontSize:{inherit:"inherit",small:(null==(i=t.typography)||null==(l=i.pxToRem)?void 0:l.call(i,20))||"1.25rem",medium:(null==(s=t.typography)||null==(c=s.pxToRem)?void 0:c.call(s,24))||"1.5rem",large:(null==(u=t.typography)||null==(d=u.pxToRem)?void 0:d.call(u,35))||"2.1875rem"}[n.fontSize],color:null!=(p=null==(f=(t.vars||t).palette)||null==(f=f[n.color])?void 0:f.main)?p:{action:null==(m=(t.vars||t).palette)||null==(m=m.action)?void 0:m.active,disabled:null==(h=(t.vars||t).palette)||null==(h=h.action)?void 0:h.disabled,inherit:void 0}[n.color]}})),v=o.forwardRef((function(e,t){const n=(0,c.b)({props:e,name:"MuiSvgIcon"}),{children:u,className:d,color:p="inherit",component:v="svg",fontSize:y="medium",htmlColor:b,inheritViewBox:x=!1,titleAccess:w,viewBox:A="0 0 24 24"}=n,k=(0,a.A)(n,h),S=o.isValidElement(u)&&"svg"===u.type,C=(0,r.A)({},n,{color:p,component:v,fontSize:y,instanceFontSize:e.fontSize,inheritViewBox:x,viewBox:A,hasSvgAsChild:S}),E={};x||(E.viewBox=A);const R=(e=>{const{color:t,fontSize:n,classes:r}=e,o={root:["root","inherit"!==t&&`color${(0,s.A)(t)}`,`fontSize${(0,s.A)(n)}`]};return(0,l.A)(o,f,r)})(C);return(0,m.jsxs)(g,(0,r.A)({as:v,className:(0,i.A)(R.root,d),focusable:"false",color:b,"aria-hidden":!w||void 0,role:w?"img":void 0,ref:t},E,k,S&&u.props,{ownerState:C,children:[S?u.props.children:u,w?(0,m.jsx)("title",{children:w}):null]}))}));v.muiName="SvgIcon";const y=v;function b(e,t){function n(n,o){return(0,m.jsx)(y,(0,r.A)({"data-testid":`${t}Icon`,ref:o},n,{children:e}))}return n.muiName=y.muiName,o.memo(o.forwardRef(n))}},950:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r=n(3468).A},4421:(e,t,n)=>{"use strict";n.r(t),n.d(t,{capitalize:()=>o.A,createChainedFunction:()=>a,createSvgIcon:()=>i.A,debounce:()=>l.A,deprecatedPropType:()=>s,isMuiElement:()=>c.A,ownerDocument:()=>u.A,ownerWindow:()=>d.A,requirePropFactory:()=>p,setRef:()=>f,unstable_ClassNameGenerator:()=>w,unstable_useEnhancedEffect:()=>m.A,unstable_useId:()=>h.A,unsupportedProp:()=>g,useControlled:()=>v.A,useEventCallback:()=>y.A,useForkRef:()=>b.A,useIsFocusVisible:()=>x.A});var r=n(9386),o=n(6803);const a=n(2456).A;var i=n(9662),l=n(950);const s=function(e,t){return()=>null};var c=n(7328),u=n(2427),d=n(6078);n(8168);const p=function(e,t){return()=>null};const f=n(6564).A;var m=n(5013),h=n(5879);const g=function(e,t,n,r,o){return null};var v=n(5420),y=n(3319),b=n(5849),x=n(3574);const w={configure:e=>{r.A.configure(e)}}},7328:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(5043);const o=function(e,t){var n,o;return r.isValidElement(e)&&-1!==t.indexOf(null!=(n=e.type.muiName)?n:null==(o=e.type)||null==(o=o._payload)||null==(o=o.value)?void 0:o.muiName)}},2427:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r=n(1668).A},6078:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r=n(3940).A},5420:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(5043);const o=function(e){let{controlled:t,default:n,name:o,state:a="value"}=e;const{current:i}=r.useRef(void 0!==t),[l,s]=r.useState(n);return[i?t:l,r.useCallback((e=>{i||s(e)}),[])]}},5013:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r=n(4440).A},3319:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r=n(1782).A},5849:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r=n(3462).A},5879:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r=n(5844).A},3574:(e,t,n)=>{"use strict";n.d(t,{A:()=>f});var r=n(5043),o=n(9303);let a=!0,i=!1;const l=new o.E,s={text:!0,search:!0,url:!0,tel:!0,email:!0,password:!0,number:!0,date:!0,month:!0,week:!0,time:!0,datetime:!0,"datetime-local":!0};function c(e){e.metaKey||e.altKey||e.ctrlKey||(a=!0)}function u(){a=!1}function d(){"hidden"===this.visibilityState&&i&&(a=!0)}function p(e){const{target:t}=e;try{return t.matches(":focus-visible")}catch(n){}return a||function(e){const{type:t,tagName:n}=e;return!("INPUT"!==n||!s[t]||e.readOnly)||"TEXTAREA"===n&&!e.readOnly||!!e.isContentEditable}(t)}const f=function(){const e=r.useCallback((e=>{var t;null!=e&&((t=e.ownerDocument).addEventListener("keydown",c,!0),t.addEventListener("mousedown",u,!0),t.addEventListener("pointerdown",u,!0),t.addEventListener("touchstart",u,!0),t.addEventListener("visibilitychange",d,!0))}),[]),t=r.useRef(!1);return{isFocusVisibleRef:t,onFocus:function(e){return!!p(e)&&(t.current=!0,!0)},onBlur:function(){return!!t.current&&(i=!0,l.start(100,(()=>{i=!1})),t.current=!1,!0)},ref:e}}},869:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});n(5043);var r=n(3290),o=n(579);function a(e){const{styles:t,defaultTheme:n={}}=e,a="function"===typeof t?e=>{return t(void 0===(r=e)||null===r||0===Object.keys(r).length?n:e);var r}:t;return(0,o.jsx)(r.mL,{styles:a})}},3174:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalStyles:()=>k.A,StyledEngineProvider:()=>A,ThemeContext:()=>s.T,css:()=>y.AH,default:()=>S,internal_processStyles:()=>C,keyframes:()=>y.i7});var r=n(8168),o=n(5043),a=n(918),i=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,l=(0,a.A)((function(e){return i.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91})),s=n(4575),c=n(1722),u=n(6598),d=n(9436),p=l,f=function(e){return"theme"!==e},m=function(e){return"string"===typeof e&&e.charCodeAt(0)>96?p:f},h=function(e,t,n){var r;if(t){var o=t.shouldForwardProp;r=e.__emotion_forwardProp&&o?function(t){return e.__emotion_forwardProp(t)&&o(t)}:o}return"function"!==typeof r&&n&&(r=e.__emotion_forwardProp),r},g=function(e){var t=e.cache,n=e.serialized,r=e.isStringTag;return(0,c.SF)(t,n,r),(0,d.s)((function(){return(0,c.sk)(t,n,r)})),null},v=function e(t,n){var a,i,l=t.__emotion_real===t,d=l&&t.__emotion_base||t;void 0!==n&&(a=n.label,i=n.target);var p=h(t,n,l),f=p||m(d),v=!f("as");return function(){var y=arguments,b=l&&void 0!==t.__emotion_styles?t.__emotion_styles.slice(0):[];if(void 0!==a&&b.push("label:"+a+";"),null==y[0]||void 0===y[0].raw)b.push.apply(b,y);else{b.push(y[0][0]);for(var x=y.length,w=1;w<x;w++)b.push(y[w],y[0][w])}var A=(0,s.w)((function(e,t,n){var r=v&&e.as||d,a="",l=[],h=e;if(null==e.theme){for(var y in h={},e)h[y]=e[y];h.theme=o.useContext(s.T)}"string"===typeof e.className?a=(0,c.Rk)(t.registered,l,e.className):null!=e.className&&(a=e.className+" ");var x=(0,u.J)(b.concat(l),t.registered,h);a+=t.key+"-"+x.name,void 0!==i&&(a+=" "+i);var w=v&&void 0===p?m(r):f,A={};for(var k in e)v&&"as"===k||w(k)&&(A[k]=e[k]);return A.className=a,n&&(A.ref=n),o.createElement(o.Fragment,null,o.createElement(g,{cache:t,serialized:x,isStringTag:"string"===typeof r}),o.createElement(r,A))}));return A.displayName=void 0!==a?a:"Styled("+("string"===typeof d?d:d.displayName||d.name||"Component")+")",A.defaultProps=t.defaultProps,A.__emotion_real=A,A.__emotion_base=d,A.__emotion_styles=b,A.__emotion_forwardProp=p,Object.defineProperty(A,"toString",{value:function(){return"."+i}}),A.withComponent=function(t,o){return e(t,(0,r.A)({},n,o,{shouldForwardProp:h(A,o,!0)})).apply(void 0,b)},A}}.bind();["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"].forEach((function(e){v[e]=v(e)}));var y=n(3290),b=n(3803),x=n(579);let w;function A(e){const{injectFirst:t,children:n}=e;return t&&w?(0,x.jsx)(s.C,{value:w,children:n}):n}"object"===typeof document&&(w=(0,b.A)({key:"css",prepend:!0}));var k=n(869);function S(e,t){return v(e,t)}const C=(e,t)=>{Array.isArray(e.__emotion_styles)&&(e.__emotion_styles=t(e.__emotion_styles))}},7266:(e,t,n)=>{"use strict";var r=n(4994);t.X4=f,t.e$=m,t.tL=g,t.eM=function(e,t){const n=p(e),r=p(t);return(Math.max(n,r)+.05)/(Math.min(n,r)+.05)},t.a=h;var o=r(n(457)),a=r(n(9214));function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return(0,a.default)(e,t,n)}function l(e){e=e.slice(1);const t=new RegExp(`.{1,${e.length>=6?2:1}}`,"g");let n=e.match(t);return n&&1===n[0].length&&(n=n.map((e=>e+e))),n?`rgb${4===n.length?"a":""}(${n.map(((e,t)=>t<3?parseInt(e,16):Math.round(parseInt(e,16)/255*1e3)/1e3)).join(", ")})`:""}function s(e){if(e.type)return e;if("#"===e.charAt(0))return s(l(e));const t=e.indexOf("("),n=e.substring(0,t);if(-1===["rgb","rgba","hsl","hsla","color"].indexOf(n))throw new Error((0,o.default)(9,e));let r,a=e.substring(t+1,e.length-1);if("color"===n){if(a=a.split(" "),r=a.shift(),4===a.length&&"/"===a[3].charAt(0)&&(a[3]=a[3].slice(1)),-1===["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].indexOf(r))throw new Error((0,o.default)(10,r))}else a=a.split(",");return a=a.map((e=>parseFloat(e))),{type:n,values:a,colorSpace:r}}const c=e=>{const t=s(e);return t.values.slice(0,3).map(((e,n)=>-1!==t.type.indexOf("hsl")&&0!==n?`${e}%`:e)).join(" ")};function u(e){const{type:t,colorSpace:n}=e;let{values:r}=e;return-1!==t.indexOf("rgb")?r=r.map(((e,t)=>t<3?parseInt(e,10):e)):-1!==t.indexOf("hsl")&&(r[1]=`${r[1]}%`,r[2]=`${r[2]}%`),r=-1!==t.indexOf("color")?`${n} ${r.join(" ")}`:`${r.join(", ")}`,`${t}(${r})`}function d(e){e=s(e);const{values:t}=e,n=t[0],r=t[1]/100,o=t[2]/100,a=r*Math.min(o,1-o),i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(e+n/30)%12;return o-a*Math.max(Math.min(t-3,9-t,1),-1)};let l="rgb";const c=[Math.round(255*i(0)),Math.round(255*i(8)),Math.round(255*i(4))];return"hsla"===e.type&&(l+="a",c.push(t[3])),u({type:l,values:c})}function p(e){let t="hsl"===(e=s(e)).type||"hsla"===e.type?s(d(e)).values:e.values;return t=t.map((t=>("color"!==e.type&&(t/=255),t<=.03928?t/12.92:((t+.055)/1.055)**2.4))),Number((.2126*t[0]+.7152*t[1]+.0722*t[2]).toFixed(3))}function f(e,t){return e=s(e),t=i(t),"rgb"!==e.type&&"hsl"!==e.type||(e.type+="a"),"color"===e.type?e.values[3]=`/${t}`:e.values[3]=t,u(e)}function m(e,t){if(e=s(e),t=i(t),-1!==e.type.indexOf("hsl"))e.values[2]*=1-t;else if(-1!==e.type.indexOf("rgb")||-1!==e.type.indexOf("color"))for(let n=0;n<3;n+=1)e.values[n]*=1-t;return u(e)}function h(e,t){if(e=s(e),t=i(t),-1!==e.type.indexOf("hsl"))e.values[2]+=(100-e.values[2])*t;else if(-1!==e.type.indexOf("rgb"))for(let n=0;n<3;n+=1)e.values[n]+=(255-e.values[n])*t;else if(-1!==e.type.indexOf("color"))for(let n=0;n<3;n+=1)e.values[n]+=(1-e.values[n])*t;return u(e)}function g(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.15;return p(e)>.5?m(e,t):h(e,t)}},8052:(e,t,n)=>{"use strict";var r=n(4994);t.Ay=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{themeId:t,defaultTheme:n=h,rootShouldForwardProp:r=m,slotShouldForwardProp:s=m}=e,u=e=>(0,c.default)((0,o.default)({},e,{theme:v((0,o.default)({},e,{defaultTheme:n,themeId:t}))}));return u.__mui_systemSx=!0,function(e){let c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(0,i.internal_processStyles)(e,(e=>e.filter((e=>!(null!=e&&e.__mui_systemSx)))));const{name:d,slot:f,skipVariantsResolver:h,skipSx:x,overridesResolver:w=y(g(f))}=c,A=(0,a.default)(c,p),k=void 0!==h?h:f&&"Root"!==f&&"root"!==f||!1,S=x||!1;let C=m;"Root"===f||"root"===f?C=r:f?C=s:function(e){return"string"===typeof e&&e.charCodeAt(0)>96}(e)&&(C=void 0);const E=(0,i.default)(e,(0,o.default)({shouldForwardProp:C,label:undefined},A)),R=e=>"function"===typeof e&&e.__emotion_real!==e||(0,l.isPlainObject)(e)?r=>b(e,(0,o.default)({},r,{theme:v({theme:r.theme,defaultTheme:n,themeId:t})})):e,P=function(r){let a=R(r);for(var i=arguments.length,l=new Array(i>1?i-1:0),s=1;s<i;s++)l[s-1]=arguments[s];const c=l?l.map(R):[];d&&w&&c.push((e=>{const r=v((0,o.default)({},e,{defaultTheme:n,themeId:t}));if(!r.components||!r.components[d]||!r.components[d].styleOverrides)return null;const a=r.components[d].styleOverrides,i={};return Object.entries(a).forEach((t=>{let[n,a]=t;i[n]=b(a,(0,o.default)({},e,{theme:r}))})),w(e,i)})),d&&!k&&c.push((e=>{var r;const a=v((0,o.default)({},e,{defaultTheme:n,themeId:t}));return b({variants:null==a||null==(r=a.components)||null==(r=r[d])?void 0:r.variants},(0,o.default)({},e,{theme:a}))})),S||c.push(u);const p=c.length-l.length;if(Array.isArray(r)&&p>0){const e=new Array(p).fill("");a=[...r,...e],a.raw=[...r.raw,...e]}const f=E(a,...c);return e.muiName&&(f.muiName=e.muiName),f};return E.withConfig&&(P.withConfig=E.withConfig),P}};var o=r(n(4634)),a=r(n(4893)),i=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=f(t);if(n&&n.has(e))return n.get(e);var r={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(r,a,i):r[a]=e[a]}return r.default=e,n&&n.set(e,r),r}(n(3174)),l=n(9482),s=(r(n(7918)),r(n(3382)),r(n(4989))),c=r(n(3234));const u=["ownerState"],d=["variants"],p=["name","slot","skipVariantsResolver","skipSx","overridesResolver"];function f(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(f=function(e){return e?n:t})(e)}function m(e){return"ownerState"!==e&&"theme"!==e&&"sx"!==e&&"as"!==e}const h=(0,s.default)(),g=e=>e?e.charAt(0).toLowerCase()+e.slice(1):e;function v(e){let{defaultTheme:t,theme:n,themeId:r}=e;return o=n,0===Object.keys(o).length?t:n[r]||n;var o}function y(e){return e?(t,n)=>n[e]:null}function b(e,t){let{ownerState:n}=t,r=(0,a.default)(t,u);const i="function"===typeof e?e((0,o.default)({ownerState:n},r)):e;if(Array.isArray(i))return i.flatMap((e=>b(e,(0,o.default)({ownerState:n},r))));if(i&&"object"===typeof i&&Array.isArray(i.variants)){const{variants:e=[]}=i;let t=(0,a.default)(i,d);return e.forEach((e=>{let a=!0;"function"===typeof e.props?a=e.props((0,o.default)({ownerState:n},r,n)):Object.keys(e.props).forEach((t=>{(null==n?void 0:n[t])!==e.props[t]&&r[t]!==e.props[t]&&(a=!1)})),a&&(Array.isArray(t)||(t=[t]),t.push("function"===typeof e.style?e.style((0,o.default)({ownerState:n},r,n)):e.style))})),t}return i}},3654:(e,t,n)=>{"use strict";n.d(t,{A:()=>s,b:()=>l});var r=n(5043),o=n(3030),a=n(579);const i=r.createContext(void 0);function l(e){let{props:t,name:n}=e;return function(e){const{theme:t,name:n,props:r}=e;if(!t||!t.components||!t.components[n])return r;const a=t.components[n];return a.defaultProps?(0,o.A)(a.defaultProps,r):a.styleOverrides||a.variants?r:(0,o.A)(a,r)}({props:t,name:n,theme:{components:r.useContext(i)}})}const s=function(e){let{value:t,children:n}=e;return(0,a.jsx)(i.Provider,{value:t,children:n})}},9751:(e,t,n)=>{"use strict";n.d(t,{EU:()=>l,NI:()=>i,iZ:()=>c,kW:()=>u,vf:()=>s,zu:()=>o});var r=n(9172);const o={xs:0,sm:600,md:900,lg:1200,xl:1536},a={keys:["xs","sm","md","lg","xl"],up:e=>`@media (min-width:${o[e]}px)`};function i(e,t,n){const r=e.theme||{};if(Array.isArray(t)){const e=r.breakpoints||a;return t.reduce(((r,o,a)=>(r[e.up(e.keys[a])]=n(t[a]),r)),{})}if("object"===typeof t){const e=r.breakpoints||a;return Object.keys(t).reduce(((r,a)=>{if(-1!==Object.keys(e.values||o).indexOf(a)){r[e.up(a)]=n(t[a],a)}else{const e=a;r[e]=t[e]}return r}),{})}return n(t)}function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};var t;return(null==(t=e.keys)?void 0:t.reduce(((t,n)=>(t[e.up(n)]={},t)),{}))||{}}function s(e,t){return e.reduce(((e,t)=>{const n=e[t];return(!n||0===Object.keys(n).length)&&delete e[t],e}),t)}function c(e){const t=l(e);for(var n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];const i=[t,...o].reduce(((e,t)=>(0,r.A)(e,t)),{});return s(Object.keys(t),i)}function u(e){let{values:t,breakpoints:n,base:r}=e;const o=r||function(e,t){if("object"!==typeof e)return{};const n={},r=Object.keys(t);return Array.isArray(e)?r.forEach(((t,r)=>{r<e.length&&(n[t]=!0)})):r.forEach((t=>{null!=e[t]&&(n[t]=!0)})),n}(t,n),a=Object.keys(o);if(0===a.length)return t;let i;return a.reduce(((e,n,r)=>(Array.isArray(t)?(e[n]=null!=t[r]?t[r]:t[i],i=r):"object"===typeof t?(e[n]=null!=t[n]?t[n]:t[i],i=n):e[n]=t,e)),{})}},9703:(e,t,n)=>{"use strict";function r(e,t){const n=this;if(n.vars&&"function"===typeof n.getColorSchemeSelector){const r=n.getColorSchemeSelector(e).replace(/(\[[^\]]+\])/,"*:where($1)");return{[r]:t}}return n.palette.mode===e?t:{}}n.d(t,{A:()=>r})},4853:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(8587),o=n(8168);const a=["values","unit","step"],i=e=>{const t=Object.keys(e).map((t=>({key:t,val:e[t]})))||[];return t.sort(((e,t)=>e.val-t.val)),t.reduce(((e,t)=>(0,o.A)({},e,{[t.key]:t.val})),{})};function l(e){const{values:t={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:n="px",step:l=5}=e,s=(0,r.A)(e,a),c=i(t),u=Object.keys(c);function d(e){return`@media (min-width:${"number"===typeof t[e]?t[e]:e}${n})`}function p(e){return`@media (max-width:${("number"===typeof t[e]?t[e]:e)-l/100}${n})`}function f(e,r){const o=u.indexOf(r);return`@media (min-width:${"number"===typeof t[e]?t[e]:e}${n}) and (max-width:${(-1!==o&&"number"===typeof t[u[o]]?t[u[o]]:r)-l/100}${n})`}return(0,o.A)({keys:u,values:c,up:d,down:p,between:f,only:function(e){return u.indexOf(e)+1<u.length?f(e,u[u.indexOf(e)+1]):d(e)},not:function(e){const t=u.indexOf(e);return 0===t?d(u[1]):t===u.length-1?p(u[t]):f(e,u[u.indexOf(e)+1]).replace("@media","@media not all and")},unit:n},s)}},8280:(e,t,n)=>{"use strict";n.d(t,{A:()=>f});var r=n(8168),o=n(8587),a=n(9172),i=n(4853);const l={borderRadius:4};var s=n(8604);var c=n(8812),u=n(7758),d=n(9703);const p=["breakpoints","palette","spacing","shape"];const f=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{breakpoints:t={},palette:n={},spacing:f,shape:m={}}=e,h=(0,o.A)(e,p),g=(0,i.A)(t),v=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:8;if(e.mui)return e;const t=(0,s.LX)({spacing:e}),n=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return(0===n.length?[1]:n).map((e=>{const n=t(e);return"number"===typeof n?`${n}px`:n})).join(" ")};return n.mui=!0,n}(f);let y=(0,a.A)({breakpoints:g,direction:"ltr",components:{},palette:(0,r.A)({mode:"light"},n),spacing:v,shape:(0,r.A)({},l,m)},h);y.applyStyles=d.A;for(var b=arguments.length,x=new Array(b>1?b-1:0),w=1;w<b;w++)x[w-1]=arguments[w];return y=x.reduce(((e,t)=>(0,a.A)(e,t)),y),y.unstable_sxConfig=(0,r.A)({},u.A,null==h?void 0:h.unstable_sxConfig),y.unstable_sx=function(e){return(0,c.A)({sx:e,theme:this})},y}},4989:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r.A,private_createBreakpoints:()=>o.A,unstable_applyStyles:()=>a.A});var r=n(8280),o=n(4853),a=n(9703)},3815:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(9172);const o=function(e,t){return t?(0,r.A)(e,t,{clone:!1}):e}},8604:(e,t,n)=>{"use strict";n.d(t,{LX:()=>m,MA:()=>f,_W:()=>h,Lc:()=>y,Ms:()=>b});var r=n(9751),o=n(7162),a=n(3815);const i={m:"margin",p:"padding"},l={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},s={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},c=function(e){const t={};return n=>(void 0===t[n]&&(t[n]=e(n)),t[n])}((e=>{if(e.length>2){if(!s[e])return[e];e=s[e]}const[t,n]=e.split(""),r=i[t],o=l[n]||"";return Array.isArray(o)?o.map((e=>r+e)):[r+o]})),u=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],d=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"],p=[...u,...d];function f(e,t,n,r){var a;const i=null!=(a=(0,o.Yn)(e,t,!1))?a:n;return"number"===typeof i?e=>"string"===typeof e?e:i*e:Array.isArray(i)?e=>"string"===typeof e?e:i[e]:"function"===typeof i?i:()=>{}}function m(e){return f(e,"spacing",8)}function h(e,t){if("string"===typeof t||null==t)return t;const n=e(Math.abs(t));return t>=0?n:"number"===typeof n?-n:`-${n}`}function g(e,t,n,o){if(-1===t.indexOf(n))return null;const a=function(e,t){return n=>e.reduce(((e,r)=>(e[r]=h(t,n),e)),{})}(c(n),o),i=e[n];return(0,r.NI)(e,i,a)}function v(e,t){const n=m(e.theme);return Object.keys(e).map((r=>g(e,t,r,n))).reduce(a.A,{})}function y(e){return v(e,u)}function b(e){return v(e,d)}function x(e){return v(e,p)}y.propTypes={},y.filterProps=u,b.propTypes={},b.filterProps=d,x.propTypes={},x.filterProps=p},7162:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>l,BO:()=>i,Yn:()=>a});var r=n(7598),o=n(9751);function a(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||"string"!==typeof t)return null;if(e&&e.vars&&n){const n=`vars.${t}`.split(".").reduce(((e,t)=>e&&e[t]?e[t]:null),e);if(null!=n)return n}return t.split(".").reduce(((e,t)=>e&&null!=e[t]?e[t]:null),e)}function i(e,t,n){let r,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:n;return r="function"===typeof e?e(n):Array.isArray(e)?e[n]||o:a(e,n)||o,t&&(r=t(r,o,e)),r}const l=function(e){const{prop:t,cssProperty:n=e.prop,themeKey:l,transform:s}=e,c=e=>{if(null==e[t])return null;const c=e[t],u=a(e.theme,l)||{};return(0,o.NI)(e,c,(e=>{let o=i(u,s,e);return e===o&&"string"===typeof e&&(o=i(u,s,`${t}${"default"===e?"":(0,r.A)(e)}`,e)),!1===n?o:{[n]:o}}))};return c.propTypes={},c.filterProps=[t],c}},7758:(e,t,n)=>{"use strict";n.d(t,{A:()=>N});var r=n(8604),o=n(7162),a=n(3815);const i=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const r=t.reduce(((e,t)=>(t.filterProps.forEach((n=>{e[n]=t})),e)),{}),o=e=>Object.keys(e).reduce(((t,n)=>r[n]?(0,a.A)(t,r[n](e)):t),{});return o.propTypes={},o.filterProps=t.reduce(((e,t)=>e.concat(t.filterProps)),[]),o};var l=n(9751);function s(e){return"number"!==typeof e?e:`${e}px solid`}function c(e,t){return(0,o.Ay)({prop:e,themeKey:"borders",transform:t})}const u=c("border",s),d=c("borderTop",s),p=c("borderRight",s),f=c("borderBottom",s),m=c("borderLeft",s),h=c("borderColor"),g=c("borderTopColor"),v=c("borderRightColor"),y=c("borderBottomColor"),b=c("borderLeftColor"),x=c("outline",s),w=c("outlineColor"),A=e=>{if(void 0!==e.borderRadius&&null!==e.borderRadius){const t=(0,r.MA)(e.theme,"shape.borderRadius",4,"borderRadius"),n=e=>({borderRadius:(0,r._W)(t,e)});return(0,l.NI)(e,e.borderRadius,n)}return null};A.propTypes={},A.filterProps=["borderRadius"];i(u,d,p,f,m,h,g,v,y,b,A,x,w);const k=e=>{if(void 0!==e.gap&&null!==e.gap){const t=(0,r.MA)(e.theme,"spacing",8,"gap"),n=e=>({gap:(0,r._W)(t,e)});return(0,l.NI)(e,e.gap,n)}return null};k.propTypes={},k.filterProps=["gap"];const S=e=>{if(void 0!==e.columnGap&&null!==e.columnGap){const t=(0,r.MA)(e.theme,"spacing",8,"columnGap"),n=e=>({columnGap:(0,r._W)(t,e)});return(0,l.NI)(e,e.columnGap,n)}return null};S.propTypes={},S.filterProps=["columnGap"];const C=e=>{if(void 0!==e.rowGap&&null!==e.rowGap){const t=(0,r.MA)(e.theme,"spacing",8,"rowGap"),n=e=>({rowGap:(0,r._W)(t,e)});return(0,l.NI)(e,e.rowGap,n)}return null};C.propTypes={},C.filterProps=["rowGap"];i(k,S,C,(0,o.Ay)({prop:"gridColumn"}),(0,o.Ay)({prop:"gridRow"}),(0,o.Ay)({prop:"gridAutoFlow"}),(0,o.Ay)({prop:"gridAutoColumns"}),(0,o.Ay)({prop:"gridAutoRows"}),(0,o.Ay)({prop:"gridTemplateColumns"}),(0,o.Ay)({prop:"gridTemplateRows"}),(0,o.Ay)({prop:"gridTemplateAreas"}),(0,o.Ay)({prop:"gridArea"}));function E(e,t){return"grey"===t?t:e}i((0,o.Ay)({prop:"color",themeKey:"palette",transform:E}),(0,o.Ay)({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:E}),(0,o.Ay)({prop:"backgroundColor",themeKey:"palette",transform:E}));function R(e){return e<=1&&0!==e?100*e+"%":e}const P=(0,o.Ay)({prop:"width",transform:R}),M=e=>{if(void 0!==e.maxWidth&&null!==e.maxWidth){const t=t=>{var n,r;const o=(null==(n=e.theme)||null==(n=n.breakpoints)||null==(n=n.values)?void 0:n[t])||l.zu[t];return o?"px"!==(null==(r=e.theme)||null==(r=r.breakpoints)?void 0:r.unit)?{maxWidth:`${o}${e.theme.breakpoints.unit}`}:{maxWidth:o}:{maxWidth:R(t)}};return(0,l.NI)(e,e.maxWidth,t)}return null};M.filterProps=["maxWidth"];const O=(0,o.Ay)({prop:"minWidth",transform:R}),T=(0,o.Ay)({prop:"height",transform:R}),j=(0,o.Ay)({prop:"maxHeight",transform:R}),I=(0,o.Ay)({prop:"minHeight",transform:R}),N=((0,o.Ay)({prop:"size",cssProperty:"width",transform:R}),(0,o.Ay)({prop:"size",cssProperty:"height",transform:R}),i(P,M,O,T,j,I,(0,o.Ay)({prop:"boxSizing"})),{border:{themeKey:"borders",transform:s},borderTop:{themeKey:"borders",transform:s},borderRight:{themeKey:"borders",transform:s},borderBottom:{themeKey:"borders",transform:s},borderLeft:{themeKey:"borders",transform:s},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:s},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:A},color:{themeKey:"palette",transform:E},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:E},backgroundColor:{themeKey:"palette",transform:E},p:{style:r.Ms},pt:{style:r.Ms},pr:{style:r.Ms},pb:{style:r.Ms},pl:{style:r.Ms},px:{style:r.Ms},py:{style:r.Ms},padding:{style:r.Ms},paddingTop:{style:r.Ms},paddingRight:{style:r.Ms},paddingBottom:{style:r.Ms},paddingLeft:{style:r.Ms},paddingX:{style:r.Ms},paddingY:{style:r.Ms},paddingInline:{style:r.Ms},paddingInlineStart:{style:r.Ms},paddingInlineEnd:{style:r.Ms},paddingBlock:{style:r.Ms},paddingBlockStart:{style:r.Ms},paddingBlockEnd:{style:r.Ms},m:{style:r.Lc},mt:{style:r.Lc},mr:{style:r.Lc},mb:{style:r.Lc},ml:{style:r.Lc},mx:{style:r.Lc},my:{style:r.Lc},margin:{style:r.Lc},marginTop:{style:r.Lc},marginRight:{style:r.Lc},marginBottom:{style:r.Lc},marginLeft:{style:r.Lc},marginX:{style:r.Lc},marginY:{style:r.Lc},marginInline:{style:r.Lc},marginInlineStart:{style:r.Lc},marginInlineEnd:{style:r.Lc},marginBlock:{style:r.Lc},marginBlockStart:{style:r.Lc},marginBlockEnd:{style:r.Lc},displayPrint:{cssProperty:!1,transform:e=>({"@media print":{display:e}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:k},rowGap:{style:C},columnGap:{style:S},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:R},maxWidth:{style:M},minWidth:{transform:R},height:{transform:R},maxHeight:{transform:R},minHeight:{transform:R},boxSizing:{},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}})},8698:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var r=n(8168),o=n(8587),a=n(9172),i=n(7758);const l=["sx"],s=e=>{var t,n;const r={systemProps:{},otherProps:{}},o=null!=(t=null==e||null==(n=e.theme)?void 0:n.unstable_sxConfig)?t:i.A;return Object.keys(e).forEach((t=>{o[t]?r.systemProps[t]=e[t]:r.otherProps[t]=e[t]})),r};function c(e){const{sx:t}=e,n=(0,o.A)(e,l),{systemProps:i,otherProps:c}=s(n);let u;return u=Array.isArray(t)?[i,...t]:"function"===typeof t?function(){const e=t(...arguments);return(0,a.Q)(e)?(0,r.A)({},i,e):i}:(0,r.A)({},i,t),(0,r.A)({},c,{sx:u})}},3234:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r.A,extendSxProp:()=>o.A,unstable_createStyleFunctionSx:()=>r.k,unstable_defaultSxConfig:()=>a.A});var r=n(8812),o=n(8698),a=n(7758)},8812:(e,t,n)=>{"use strict";n.d(t,{A:()=>u,k:()=>s});var r=n(7598),o=n(3815),a=n(7162),i=n(9751),l=n(7758);function s(){function e(e,t,n,o){const l={[e]:t,theme:n},s=o[e];if(!s)return{[e]:t};const{cssProperty:c=e,themeKey:u,transform:d,style:p}=s;if(null==t)return null;if("typography"===u&&"inherit"===t)return{[e]:t};const f=(0,a.Yn)(n,u)||{};if(p)return p(l);return(0,i.NI)(l,t,(t=>{let n=(0,a.BO)(f,d,t);return t===n&&"string"===typeof t&&(n=(0,a.BO)(f,d,`${e}${"default"===t?"":(0,r.A)(t)}`,t)),!1===c?n:{[c]:n}}))}return function t(n){var r;const{sx:a,theme:s={}}=n||{};if(!a)return null;const c=null!=(r=s.unstable_sxConfig)?r:l.A;function u(n){let r=n;if("function"===typeof n)r=n(s);else if("object"!==typeof n)return n;if(!r)return null;const a=(0,i.EU)(s.breakpoints),l=Object.keys(a);let u=a;return Object.keys(r).forEach((n=>{const a=(l=r[n],d=s,"function"===typeof l?l(d):l);var l,d;if(null!==a&&void 0!==a)if("object"===typeof a)if(c[n])u=(0,o.A)(u,e(n,a,s,c));else{const e=(0,i.NI)({theme:s},a,(e=>({[n]:e})));!function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const r=t.reduce(((e,t)=>e.concat(Object.keys(t))),[]),o=new Set(r);return t.every((e=>o.size===Object.keys(e).length))}(e,a)?u=(0,o.A)(u,e):u[n]=t({sx:a,theme:s})}else u=(0,o.A)(u,e(n,a,s,c))})),(0,i.vf)(l,u)}return Array.isArray(a)?a.map(u):u(a)}}const c=s();c.filterProps=["sx"];const u=c},2374:(e,t,n)=>{"use strict";t.A=void 0;var r=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=a(t);if(n&&n.has(e))return n.get(e);var r={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=o?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(r,i,l):r[i]=e[i]}return r.default=e,n&&n.set(e,r),r}(n(5043)),o=n(3174);function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(a=function(e){return e?n:t})(e)}t.A=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;const t=r.useContext(o.ThemeContext);return t&&(n=t,0!==Object.keys(n).length)?t:e;var n}},9386:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});const r=e=>e,o=(()=>{let e=r;return{configure(t){e=t},generate:t=>e(t),reset(){e=r}}})()},7598:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(7868);function o(e){if("string"!==typeof e)throw new Error((0,r.A)(7));return e.charAt(0).toUpperCase()+e.slice(1)}},7918:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r.A});var r=n(7598)},9214:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});const r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MIN_SAFE_INTEGER,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.MAX_SAFE_INTEGER;return Math.max(t,Math.min(e,n))}},8610:(e,t,n)=>{"use strict";function r(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;const r={};return Object.keys(e).forEach((o=>{r[o]=e[o].reduce(((e,r)=>{if(r){const o=t(r);""!==o&&e.push(o),n&&n[r]&&e.push(n[r])}return e}),[]).join(" ")})),r}n.d(t,{A:()=>r})},2456:(e,t,n)=>{"use strict";function r(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce(((e,t)=>null==t?e:function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];e.apply(this,r),t.apply(this,r)}),(()=>{}))}n.d(t,{A:()=>r})},3468:(e,t,n)=>{"use strict";function r(e){let t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:166;function r(){for(var r=arguments.length,o=new Array(r),a=0;a<r;a++)o[a]=arguments[a];clearTimeout(t),t=setTimeout((()=>{e.apply(this,o)}),n)}return r.clear=()=>{clearTimeout(t)},r}n.d(t,{A:()=>r})},9172:(e,t,n)=>{"use strict";n.d(t,{A:()=>i,Q:()=>o});var r=n(8168);function o(e){if("object"!==typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function a(e){if(!o(e))return e;const t={};return Object.keys(e).forEach((n=>{t[n]=a(e[n])})),t}function i(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{clone:!0};const l=n.clone?(0,r.A)({},e):e;return o(e)&&o(t)&&Object.keys(t).forEach((r=>{o(t[r])&&Object.prototype.hasOwnProperty.call(e,r)&&o(e[r])?l[r]=i(e[r],t[r],n):n.clone?l[r]=o(t[r])?a(t[r]):t[r]:l[r]=t[r]})),l}},9482:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r.A,isPlainObject:()=>r.Q});var r=n(9172)},7868:(e,t,n)=>{"use strict";function r(e){let t="https://mui.com/production-error/?code="+e;for(let n=1;n<arguments.length;n+=1)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified MUI error #"+e+"; visit "+t+" for the full message."}n.d(t,{A:()=>r})},457:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r.A});var r=n(7868)},2372:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>a});var r=n(9386);const o={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function a(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Mui";const a=o[t];return a?`${n}-${a}`:`${r.A.generate(e)}-${t}`}},2532:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(2372);function o(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Mui";const o={};return t.forEach((t=>{o[t]=(0,r.Ay)(e,t,n)})),o}},3382:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s,getFunctionName:()=>a});var r=n(2086);const o=/^\s*function(?:\s|\s*\/\*.*\*\/\s*)+([^(\s/]*)\s*/;function a(e){const t=`${e}`.match(o);return t&&t[1]||""}function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e.displayName||e.name||a(e)||t}function l(e,t,n){const r=i(t);return e.displayName||(""!==r?`${n}(${r})`:n)}function s(e){if(null!=e){if("string"===typeof e)return e;if("function"===typeof e)return i(e,"Component");if("object"===typeof e)switch(e.$$typeof){case r.ForwardRef:return l(e,e.render,"ForwardRef");case r.Memo:return l(e,e.type,"memo");default:return}}}},1668:(e,t,n)=>{"use strict";function r(e){return e&&e.ownerDocument||document}n.d(t,{A:()=>r})},3940:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(1668);function o(e){return(0,r.A)(e).defaultView||window}},3030:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(8168);function o(e,t){const n=(0,r.A)({},t);return Object.keys(e).forEach((a=>{if(a.toString().match(/^(components|slots)$/))n[a]=(0,r.A)({},e[a],n[a]);else if(a.toString().match(/^(componentsProps|slotProps)$/)){const i=e[a]||{},l=t[a];n[a]={},l&&Object.keys(l)?i&&Object.keys(i)?(n[a]=(0,r.A)({},l),Object.keys(i).forEach((e=>{n[a][e]=o(i[e],l[e])}))):n[a]=l:n[a]=i}else void 0===n[a]&&(n[a]=e[a])})),n}},6564:(e,t,n)=>{"use strict";function r(e,t){"function"===typeof e?e(t):e&&(e.current=t)}n.d(t,{A:()=>r})},4440:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(5043);const o="undefined"!==typeof window?r.useLayoutEffect:r.useEffect},1782:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(5043),o=n(4440);const a=function(e){const t=r.useRef(e);return(0,o.A)((()=>{t.current=e})),r.useRef((function(){return(0,t.current)(...arguments)})).current}},3462:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(5043),o=n(6564);function a(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return r.useMemo((()=>t.every((e=>null==e))?null:e=>{t.forEach((t=>{(0,o.A)(t,e)}))}),t)}},5844:(e,t,n)=>{"use strict";var r;n.d(t,{A:()=>l});var o=n(5043);let a=0;const i=(r||(r=n.t(o,2)))["useId".toString()];function l(e){if(void 0!==i){const t=i();return null!=e?e:t}return function(e){const[t,n]=o.useState(e),r=e||t;return o.useEffect((()=>{null==t&&(a+=1,n(`mui-${a}`))}),[t]),r}(e)}},9303:(e,t,n)=>{"use strict";n.d(t,{E:()=>i,A:()=>l});var r=n(5043);const o={};const a=[];class i{constructor(){this.currentId=null,this.clear=()=>{null!==this.currentId&&(clearTimeout(this.currentId),this.currentId=null)},this.disposeEffect=()=>this.clear}static create(){return new i}start(e,t){this.clear(),this.currentId=setTimeout((()=>{this.currentId=null,t()}),e)}}function l(){const e=function(e,t){const n=r.useRef(o);return n.current===o&&(n.current=e(t)),n}(i.create).current;var t;return t=e.disposeEffect,r.useEffect(t,a),e}},219:(e,t,n)=>{"use strict";var r=n(3763),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},a={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},l={};function s(e){return r.isMemo(e)?i:l[e.$$typeof]||o}l[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},l[r.Memo]=i;var c=Object.defineProperty,u=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,p=Object.getOwnPropertyDescriptor,f=Object.getPrototypeOf,m=Object.prototype;e.exports=function e(t,n,r){if("string"!==typeof n){if(m){var o=f(n);o&&o!==m&&e(t,o,r)}var i=u(n);d&&(i=i.concat(d(n)));for(var l=s(t),h=s(n),g=0;g<i.length;++g){var v=i[g];if(!a[v]&&(!r||!r[v])&&(!h||!h[v])&&(!l||!l[v])){var y=p(n,v);try{c(t,v,y)}catch(b){}}}}return t}},4983:(e,t)=>{"use strict";var n="function"===typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,a=n?Symbol.for("react.fragment"):60107,i=n?Symbol.for("react.strict_mode"):60108,l=n?Symbol.for("react.profiler"):60114,s=n?Symbol.for("react.provider"):60109,c=n?Symbol.for("react.context"):60110,u=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,p=n?Symbol.for("react.forward_ref"):60112,f=n?Symbol.for("react.suspense"):60113,m=n?Symbol.for("react.suspense_list"):60120,h=n?Symbol.for("react.memo"):60115,g=n?Symbol.for("react.lazy"):60116,v=n?Symbol.for("react.block"):60121,y=n?Symbol.for("react.fundamental"):60117,b=n?Symbol.for("react.responder"):60118,x=n?Symbol.for("react.scope"):60119;function w(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case u:case d:case a:case l:case i:case f:return e;default:switch(e=e&&e.$$typeof){case c:case p:case g:case h:case s:return e;default:return t}}case o:return t}}}function A(e){return w(e)===d}t.AsyncMode=u,t.ConcurrentMode=d,t.ContextConsumer=c,t.ContextProvider=s,t.Element=r,t.ForwardRef=p,t.Fragment=a,t.Lazy=g,t.Memo=h,t.Portal=o,t.Profiler=l,t.StrictMode=i,t.Suspense=f,t.isAsyncMode=function(e){return A(e)||w(e)===u},t.isConcurrentMode=A,t.isContextConsumer=function(e){return w(e)===c},t.isContextProvider=function(e){return w(e)===s},t.isElement=function(e){return"object"===typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return w(e)===p},t.isFragment=function(e){return w(e)===a},t.isLazy=function(e){return w(e)===g},t.isMemo=function(e){return w(e)===h},t.isPortal=function(e){return w(e)===o},t.isProfiler=function(e){return w(e)===l},t.isStrictMode=function(e){return w(e)===i},t.isSuspense=function(e){return w(e)===f},t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===a||e===d||e===l||e===i||e===f||e===m||"object"===typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===h||e.$$typeof===s||e.$$typeof===c||e.$$typeof===p||e.$$typeof===y||e.$$typeof===b||e.$$typeof===x||e.$$typeof===v)},t.typeOf=w},3763:(e,t,n)=>{"use strict";e.exports=n(4983)},2730:(e,t,n)=>{"use strict";var r=n(5043),o=n(8853);function a(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,l={};function s(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(l[e]=t,e=0;e<t.length;e++)i.add(t[e])}var u=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,p=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,f={},m={};function h(e,t,n,r,o,a,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=i}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){g[e]=new h(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];g[t]=new h(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){g[e]=new h(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){g[e]=new h(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){g[e]=new h(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){g[e]=new h(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){g[e]=new h(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){g[e]=new h(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){g[e]=new h(e,5,!1,e.toLowerCase(),null,!1,!1)}));var v=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var o=g.hasOwnProperty(t)?g[t]:null;(null!==o?0!==o.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,o,r)&&(n=null),r||null===o?function(e){return!!d.call(m,e)||!d.call(f,e)&&(p.test(e)?m[e]=!0:(f[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=null===n?3!==o.type&&"":n:(t=o.attributeName,r=o.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(o=o.type)||4===o&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(v,y);g[t]=new h(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(v,y);g[t]=new h(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(v,y);g[t]=new h(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){g[e]=new h(e,1,!1,e.toLowerCase(),null,!1,!1)})),g.xlinkHref=new h("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){g[e]=new h(e,1,!1,e.toLowerCase(),null,!0,!0)}));var x=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),A=Symbol.for("react.portal"),k=Symbol.for("react.fragment"),S=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),E=Symbol.for("react.provider"),R=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),M=Symbol.for("react.suspense"),O=Symbol.for("react.suspense_list"),T=Symbol.for("react.memo"),j=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var I=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var N=Symbol.iterator;function $(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=N&&e[N]||e["@@iterator"])?e:null}var z,L=Object.assign;function _(e){if(void 0===z)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);z=t&&t[1]||""}return"\n"+z+e}var F=!1;function D(e,t){if(!e||F)return"";F=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&"string"===typeof c.stack){for(var o=c.stack.split("\n"),a=r.stack.split("\n"),i=o.length-1,l=a.length-1;1<=i&&0<=l&&o[i]!==a[l];)l--;for(;1<=i&&0<=l;i--,l--)if(o[i]!==a[l]){if(1!==i||1!==l)do{if(i--,0>--l||o[i]!==a[l]){var s="\n"+o[i].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=i&&0<=l);break}}}finally{F=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?_(e):""}function W(e){switch(e.tag){case 5:return _(e.type);case 16:return _("Lazy");case 13:return _("Suspense");case 19:return _("SuspenseList");case 0:case 2:case 15:return e=D(e.type,!1);case 11:return e=D(e.type.render,!1);case 1:return e=D(e.type,!0);default:return""}}function B(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case k:return"Fragment";case A:return"Portal";case C:return"Profiler";case S:return"StrictMode";case M:return"Suspense";case O:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case R:return(e.displayName||"Context")+".Consumer";case E:return(e._context.displayName||"Context")+".Provider";case P:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case T:return null!==(t=e.displayName||null)?t:B(e.type)||"Memo";case j:t=e._payload,e=e._init;try{return B(e(t))}catch(n){}}return null}function V(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return B(t);case 8:return t===S?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function H(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function U(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function X(e){e._valueTracker||(e._valueTracker=function(e){var t=U(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var o=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function K(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=U(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function q(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function G(e,t){var n=t.checked;return L({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Q(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=H(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Y(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function J(e,t){Y(e,t);var n=H(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,H(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&q(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+H(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(a(91));return L({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function oe(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(a(92));if(te(n)){if(1<n.length)throw Error(a(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:H(n)}}function ae(e,t){var n=H(t.value),r=H(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ie(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function le(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?le(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,ue,de=(ue=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ue(e,t)}))}:ue);function pe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var fe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},me=["Webkit","ms","Moz","O"];function he(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||fe.hasOwnProperty(e)&&fe[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),o=he(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}Object.keys(fe).forEach((function(e){me.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),fe[t]=fe[e]}))}));var ve=L({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(a(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(a(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(a(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(a(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xe=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Ae=null,ke=null,Se=null;function Ce(e){if(e=xo(e)){if("function"!==typeof Ae)throw Error(a(280));var t=e.stateNode;t&&(t=Ao(t),Ae(e.stateNode,e.type,t))}}function Ee(e){ke?Se?Se.push(e):Se=[e]:ke=e}function Re(){if(ke){var e=ke,t=Se;if(Se=ke=null,Ce(e),t)for(e=0;e<t.length;e++)Ce(t[e])}}function Pe(e,t){return e(t)}function Me(){}var Oe=!1;function Te(e,t,n){if(Oe)return e(t,n);Oe=!0;try{return Pe(e,t,n)}finally{Oe=!1,(null!==ke||null!==Se)&&(Me(),Re())}}function je(e,t){var n=e.stateNode;if(null===n)return null;var r=Ao(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(a(231,t,typeof n));return n}var Ie=!1;if(u)try{var Ne={};Object.defineProperty(Ne,"passive",{get:function(){Ie=!0}}),window.addEventListener("test",Ne,Ne),window.removeEventListener("test",Ne,Ne)}catch(ue){Ie=!1}function $e(e,t,n,r,o,a,i,l,s){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var ze=!1,Le=null,_e=!1,Fe=null,De={onError:function(e){ze=!0,Le=e}};function We(e,t,n,r,o,a,i,l,s){ze=!1,Le=null,$e.apply(De,arguments)}function Be(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Ve(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function He(e){if(Be(e)!==e)throw Error(a(188))}function Ue(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Be(e)))throw Error(a(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(null===o)break;var i=o.alternate;if(null===i){if(null!==(r=o.return)){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return He(o),e;if(i===r)return He(o),t;i=i.sibling}throw Error(a(188))}if(n.return!==r.return)n=o,r=i;else{for(var l=!1,s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l)throw Error(a(189))}}if(n.alternate!==r)throw Error(a(190))}if(3!==n.tag)throw Error(a(188));return n.stateNode.current===n?e:t}(e))?Xe(e):null}function Xe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Xe(e);if(null!==t)return t;e=e.sibling}return null}var Ke=o.unstable_scheduleCallback,qe=o.unstable_cancelCallback,Ge=o.unstable_shouldYield,Qe=o.unstable_requestPaint,Ye=o.unstable_now,Je=o.unstable_getCurrentPriorityLevel,Ze=o.unstable_ImmediatePriority,et=o.unstable_UserBlockingPriority,tt=o.unstable_NormalPriority,nt=o.unstable_LowPriority,rt=o.unstable_IdlePriority,ot=null,at=null;var it=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(lt(e)/st|0)|0},lt=Math.log,st=Math.LN2;var ct=64,ut=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function pt(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,o=e.suspendedLanes,a=e.pingedLanes,i=268435455&n;if(0!==i){var l=i&~o;0!==l?r=dt(l):0!==(a&=i)&&(r=dt(a))}else 0!==(i=n&~o)?r=dt(i):0!==a&&(r=dt(a));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&o)&&((o=r&-r)>=(a=t&-t)||16===o&&0!==(4194240&a)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)o=1<<(n=31-it(t)),r|=e[n],t&=~o;return r}function ft(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function mt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function ht(){var e=ct;return 0===(4194240&(ct<<=1))&&(ct=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-it(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-it(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var bt=0;function xt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var wt,At,kt,St,Ct,Et=!1,Rt=[],Pt=null,Mt=null,Ot=null,Tt=new Map,jt=new Map,It=[],Nt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function $t(e,t){switch(e){case"focusin":case"focusout":Pt=null;break;case"dragenter":case"dragleave":Mt=null;break;case"mouseover":case"mouseout":Ot=null;break;case"pointerover":case"pointerout":Tt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":jt.delete(t.pointerId)}}function zt(e,t,n,r,o,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[o]},null!==t&&(null!==(t=xo(t))&&At(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function Lt(e){var t=bo(e.target);if(null!==t){var n=Be(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Ve(n)))return e.blockedOn=t,void Ct(e.priority,(function(){kt(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function _t(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Gt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=xo(n))&&At(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xe=r,n.target.dispatchEvent(r),xe=null,t.shift()}return!0}function Ft(e,t,n){_t(e)&&n.delete(t)}function Dt(){Et=!1,null!==Pt&&_t(Pt)&&(Pt=null),null!==Mt&&_t(Mt)&&(Mt=null),null!==Ot&&_t(Ot)&&(Ot=null),Tt.forEach(Ft),jt.forEach(Ft)}function Wt(e,t){e.blockedOn===t&&(e.blockedOn=null,Et||(Et=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,Dt)))}function Bt(e){function t(t){return Wt(t,e)}if(0<Rt.length){Wt(Rt[0],e);for(var n=1;n<Rt.length;n++){var r=Rt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Pt&&Wt(Pt,e),null!==Mt&&Wt(Mt,e),null!==Ot&&Wt(Ot,e),Tt.forEach(t),jt.forEach(t),n=0;n<It.length;n++)(r=It[n]).blockedOn===e&&(r.blockedOn=null);for(;0<It.length&&null===(n=It[0]).blockedOn;)Lt(n),null===n.blockedOn&&It.shift()}var Vt=x.ReactCurrentBatchConfig,Ht=!0;function Ut(e,t,n,r){var o=bt,a=Vt.transition;Vt.transition=null;try{bt=1,Kt(e,t,n,r)}finally{bt=o,Vt.transition=a}}function Xt(e,t,n,r){var o=bt,a=Vt.transition;Vt.transition=null;try{bt=4,Kt(e,t,n,r)}finally{bt=o,Vt.transition=a}}function Kt(e,t,n,r){if(Ht){var o=Gt(e,t,n,r);if(null===o)Hr(e,t,r,qt,n),$t(e,r);else if(function(e,t,n,r,o){switch(t){case"focusin":return Pt=zt(Pt,e,t,n,r,o),!0;case"dragenter":return Mt=zt(Mt,e,t,n,r,o),!0;case"mouseover":return Ot=zt(Ot,e,t,n,r,o),!0;case"pointerover":var a=o.pointerId;return Tt.set(a,zt(Tt.get(a)||null,e,t,n,r,o)),!0;case"gotpointercapture":return a=o.pointerId,jt.set(a,zt(jt.get(a)||null,e,t,n,r,o)),!0}return!1}(o,e,t,n,r))r.stopPropagation();else if($t(e,r),4&t&&-1<Nt.indexOf(e)){for(;null!==o;){var a=xo(o);if(null!==a&&wt(a),null===(a=Gt(e,t,n,r))&&Hr(e,t,r,qt,n),a===o)break;o=a}null!==o&&r.stopPropagation()}else Hr(e,t,r,null,n)}}var qt=null;function Gt(e,t,n,r){if(qt=null,null!==(e=bo(e=we(r))))if(null===(t=Be(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Ve(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return qt=e,null}function Qt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Je()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Yt=null,Jt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Jt,r=n.length,o="value"in Yt?Yt.value:Yt.textContent,a=o.length;for(e=0;e<r&&n[e]===o[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===o[a-t];t++);return Zt=o.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function on(e){function t(t,n,r,o,a){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(o):o[i]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return L(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var an,ln,sn,cn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},un=on(cn),dn=L({},cn,{view:0,detail:0}),pn=on(dn),fn=L({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Cn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sn&&(sn&&"mousemove"===e.type?(an=e.screenX-sn.screenX,ln=e.screenY-sn.screenY):ln=an=0,sn=e),an)},movementY:function(e){return"movementY"in e?e.movementY:ln}}),mn=on(fn),hn=on(L({},fn,{dataTransfer:0})),gn=on(L({},dn,{relatedTarget:0})),vn=on(L({},cn,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=L({},cn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=on(yn),xn=on(L({},cn,{data:0})),wn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},An={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},kn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Sn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=kn[e])&&!!t[e]}function Cn(){return Sn}var En=L({},dn,{key:function(e){if(e.key){var t=wn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?An[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Cn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Rn=on(En),Pn=on(L({},fn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Mn=on(L({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Cn})),On=on(L({},cn,{propertyName:0,elapsedTime:0,pseudoElement:0})),Tn=L({},fn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),jn=on(Tn),In=[9,13,27,32],Nn=u&&"CompositionEvent"in window,$n=null;u&&"documentMode"in document&&($n=document.documentMode);var zn=u&&"TextEvent"in window&&!$n,Ln=u&&(!Nn||$n&&8<$n&&11>=$n),_n=String.fromCharCode(32),Fn=!1;function Dn(e,t){switch(e){case"keyup":return-1!==In.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Wn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Bn=!1;var Vn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Hn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Vn[e.type]:"textarea"===t}function Un(e,t,n,r){Ee(r),0<(t=Xr(t,"onChange")).length&&(n=new un("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Xn=null,Kn=null;function qn(e){_r(e,0)}function Gn(e){if(K(wo(e)))return e}function Qn(e,t){if("change"===e)return t}var Yn=!1;if(u){var Jn;if(u){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Jn=Zn}else Jn=!1;Yn=Jn&&(!document.documentMode||9<document.documentMode)}function tr(){Xn&&(Xn.detachEvent("onpropertychange",nr),Kn=Xn=null)}function nr(e){if("value"===e.propertyName&&Gn(Kn)){var t=[];Un(t,Kn,e,we(e)),Te(qn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Kn=n,(Xn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function or(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Gn(Kn)}function ar(e,t){if("click"===e)return Gn(t)}function ir(e,t){if("input"===e||"change"===e)return Gn(t)}var lr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function sr(e,t){if(lr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!d.call(t,o)||!lr(e[o],t[o]))return!1}return!0}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ur(e,t){var n,r=cr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=cr(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function pr(){for(var e=window,t=q();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=q((e=t.contentWindow).document)}return t}function fr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function mr(e){var t=pr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&fr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var o=n.textContent.length,a=Math.min(r.start,o);r=void 0===r.end?a:Math.min(r.end,o),!e.extend&&a>r&&(o=r,r=a,a=o),o=ur(n,a);var i=ur(n,r);o&&i&&(1!==e.rangeCount||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(o.node,o.offset),e.removeAllRanges(),a>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var hr=u&&"documentMode"in document&&11>=document.documentMode,gr=null,vr=null,yr=null,br=!1;function xr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==gr||gr!==q(r)||("selectionStart"in(r=gr)&&fr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&sr(yr,r)||(yr=r,0<(r=Xr(vr,"onSelect")).length&&(t=new un("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function wr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ar={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},kr={},Sr={};function Cr(e){if(kr[e])return kr[e];if(!Ar[e])return e;var t,n=Ar[e];for(t in n)if(n.hasOwnProperty(t)&&t in Sr)return kr[e]=n[t];return e}u&&(Sr=document.createElement("div").style,"AnimationEvent"in window||(delete Ar.animationend.animation,delete Ar.animationiteration.animation,delete Ar.animationstart.animation),"TransitionEvent"in window||delete Ar.transitionend.transition);var Er=Cr("animationend"),Rr=Cr("animationiteration"),Pr=Cr("animationstart"),Mr=Cr("transitionend"),Or=new Map,Tr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function jr(e,t){Or.set(e,t),s(t,[e])}for(var Ir=0;Ir<Tr.length;Ir++){var Nr=Tr[Ir];jr(Nr.toLowerCase(),"on"+(Nr[0].toUpperCase()+Nr.slice(1)))}jr(Er,"onAnimationEnd"),jr(Rr,"onAnimationIteration"),jr(Pr,"onAnimationStart"),jr("dblclick","onDoubleClick"),jr("focusin","onFocus"),jr("focusout","onBlur"),jr(Mr,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var $r="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),zr=new Set("cancel close invalid load scroll toggle".split(" ").concat($r));function Lr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,o,i,l,s,c){if(We.apply(this,arguments),ze){if(!ze)throw Error(a(198));var u=Le;ze=!1,Le=null,_e||(_e=!0,Fe=u)}}(r,t,void 0,e),e.currentTarget=null}function _r(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],s=l.instance,c=l.currentTarget;if(l=l.listener,s!==a&&o.isPropagationStopped())break e;Lr(o,l,c),a=s}else for(i=0;i<r.length;i++){if(s=(l=r[i]).instance,c=l.currentTarget,l=l.listener,s!==a&&o.isPropagationStopped())break e;Lr(o,l,c),a=s}}}if(_e)throw e=Fe,_e=!1,Fe=null,e}function Fr(e,t){var n=t[go];void 0===n&&(n=t[go]=new Set);var r=e+"__bubble";n.has(r)||(Vr(t,e,2,!1),n.add(r))}function Dr(e,t,n){var r=0;t&&(r|=4),Vr(n,e,r,t)}var Wr="_reactListening"+Math.random().toString(36).slice(2);function Br(e){if(!e[Wr]){e[Wr]=!0,i.forEach((function(t){"selectionchange"!==t&&(zr.has(t)||Dr(t,!1,e),Dr(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Wr]||(t[Wr]=!0,Dr("selectionchange",!1,t))}}function Vr(e,t,n,r){switch(Qt(t)){case 1:var o=Ut;break;case 4:o=Xt;break;default:o=Kt}n=o.bind(null,t,n,e),o=void 0,!Ie||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Hr(e,t,n,r,o){var a=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var l=r.stateNode.containerInfo;if(l===o||8===l.nodeType&&l.parentNode===o)break;if(4===i)for(i=r.return;null!==i;){var s=i.tag;if((3===s||4===s)&&((s=i.stateNode.containerInfo)===o||8===s.nodeType&&s.parentNode===o))return;i=i.return}for(;null!==l;){if(null===(i=bo(l)))return;if(5===(s=i.tag)||6===s){r=a=i;continue e}l=l.parentNode}}r=r.return}Te((function(){var r=a,o=we(n),i=[];e:{var l=Or.get(e);if(void 0!==l){var s=un,c=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":s=Rn;break;case"focusin":c="focus",s=gn;break;case"focusout":c="blur",s=gn;break;case"beforeblur":case"afterblur":s=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=mn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=hn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Mn;break;case Er:case Rr:case Pr:s=vn;break;case Mr:s=On;break;case"scroll":s=pn;break;case"wheel":s=jn;break;case"copy":case"cut":case"paste":s=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=Pn}var u=0!==(4&t),d=!u&&"scroll"===e,p=u?null!==l?l+"Capture":null:l;u=[];for(var f,m=r;null!==m;){var h=(f=m).stateNode;if(5===f.tag&&null!==h&&(f=h,null!==p&&(null!=(h=je(m,p))&&u.push(Ur(m,h,f)))),d)break;m=m.return}0<u.length&&(l=new s(l,c,null,n,o),i.push({event:l,listeners:u}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||n===xe||!(c=n.relatedTarget||n.fromElement)||!bo(c)&&!c[ho])&&(s||l)&&(l=o.window===o?o:(l=o.ownerDocument)?l.defaultView||l.parentWindow:window,s?(s=r,null!==(c=(c=n.relatedTarget||n.toElement)?bo(c):null)&&(c!==(d=Be(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(s=null,c=r),s!==c)){if(u=mn,h="onMouseLeave",p="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(u=Pn,h="onPointerLeave",p="onPointerEnter",m="pointer"),d=null==s?l:wo(s),f=null==c?l:wo(c),(l=new u(h,m+"leave",s,n,o)).target=d,l.relatedTarget=f,h=null,bo(o)===r&&((u=new u(p,m+"enter",c,n,o)).target=f,u.relatedTarget=d,h=u),d=h,s&&c)e:{for(p=c,m=0,f=u=s;f;f=Kr(f))m++;for(f=0,h=p;h;h=Kr(h))f++;for(;0<m-f;)u=Kr(u),m--;for(;0<f-m;)p=Kr(p),f--;for(;m--;){if(u===p||null!==p&&u===p.alternate)break e;u=Kr(u),p=Kr(p)}u=null}else u=null;null!==s&&qr(i,l,s,u,!1),null!==c&&null!==d&&qr(i,d,c,u,!0)}if("select"===(s=(l=r?wo(r):window).nodeName&&l.nodeName.toLowerCase())||"input"===s&&"file"===l.type)var g=Qn;else if(Hn(l))if(Yn)g=ir;else{g=or;var v=rr}else(s=l.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===l.type||"radio"===l.type)&&(g=ar);switch(g&&(g=g(e,r))?Un(i,g,n,o):(v&&v(e,l,r),"focusout"===e&&(v=l._wrapperState)&&v.controlled&&"number"===l.type&&ee(l,"number",l.value)),v=r?wo(r):window,e){case"focusin":(Hn(v)||"true"===v.contentEditable)&&(gr=v,vr=r,yr=null);break;case"focusout":yr=vr=gr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,xr(i,n,o);break;case"selectionchange":if(hr)break;case"keydown":case"keyup":xr(i,n,o)}var y;if(Nn)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Bn?Dn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Ln&&"ko"!==n.locale&&(Bn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Bn&&(y=en()):(Jt="value"in(Yt=o)?Yt.value:Yt.textContent,Bn=!0)),0<(v=Xr(r,b)).length&&(b=new xn(b,e,null,n,o),i.push({event:b,listeners:v}),y?b.data=y:null!==(y=Wn(n))&&(b.data=y))),(y=zn?function(e,t){switch(e){case"compositionend":return Wn(t);case"keypress":return 32!==t.which?null:(Fn=!0,_n);case"textInput":return(e=t.data)===_n&&Fn?null:e;default:return null}}(e,n):function(e,t){if(Bn)return"compositionend"===e||!Nn&&Dn(e,t)?(e=en(),Zt=Jt=Yt=null,Bn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ln&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Xr(r,"onBeforeInput")).length&&(o=new xn("onBeforeInput","beforeinput",null,n,o),i.push({event:o,listeners:r}),o.data=y))}_r(i,t)}))}function Ur(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Xr(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,a=o.stateNode;5===o.tag&&null!==a&&(o=a,null!=(a=je(e,n))&&r.unshift(Ur(e,a,o)),null!=(a=je(e,t))&&r.push(Ur(e,a,o))),e=e.return}return r}function Kr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function qr(e,t,n,r,o){for(var a=t._reactName,i=[];null!==n&&n!==r;){var l=n,s=l.alternate,c=l.stateNode;if(null!==s&&s===r)break;5===l.tag&&null!==c&&(l=c,o?null!=(s=je(n,a))&&i.unshift(Ur(n,s,l)):o||null!=(s=je(n,a))&&i.push(Ur(n,s,l))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Gr=/\r\n?/g,Qr=/\u0000|\uFFFD/g;function Yr(e){return("string"===typeof e?e:""+e).replace(Gr,"\n").replace(Qr,"")}function Jr(e,t,n){if(t=Yr(t),Yr(e)!==t&&n)throw Error(a(425))}function Zr(){}var eo=null,to=null;function no(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ro="function"===typeof setTimeout?setTimeout:void 0,oo="function"===typeof clearTimeout?clearTimeout:void 0,ao="function"===typeof Promise?Promise:void 0,io="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ao?function(e){return ao.resolve(null).then(e).catch(lo)}:ro;function lo(e){setTimeout((function(){throw e}))}function so(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0===r)return e.removeChild(o),void Bt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=o}while(n);Bt(t)}function co(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function uo(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var po=Math.random().toString(36).slice(2),fo="__reactFiber$"+po,mo="__reactProps$"+po,ho="__reactContainer$"+po,go="__reactEvents$"+po,vo="__reactListeners$"+po,yo="__reactHandles$"+po;function bo(e){var t=e[fo];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ho]||n[fo]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=uo(e);null!==e;){if(n=e[fo])return n;e=uo(e)}return t}n=(e=n).parentNode}return null}function xo(e){return!(e=e[fo]||e[ho])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function wo(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(a(33))}function Ao(e){return e[mo]||null}var ko=[],So=-1;function Co(e){return{current:e}}function Eo(e){0>So||(e.current=ko[So],ko[So]=null,So--)}function Ro(e,t){So++,ko[So]=e.current,e.current=t}var Po={},Mo=Co(Po),Oo=Co(!1),To=Po;function jo(e,t){var n=e.type.contextTypes;if(!n)return Po;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o,a={};for(o in n)a[o]=t[o];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function Io(e){return null!==(e=e.childContextTypes)&&void 0!==e}function No(){Eo(Oo),Eo(Mo)}function $o(e,t,n){if(Mo.current!==Po)throw Error(a(168));Ro(Mo,t),Ro(Oo,n)}function zo(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var o in r=r.getChildContext())if(!(o in t))throw Error(a(108,V(e)||"Unknown",o));return L({},n,r)}function Lo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Po,To=Mo.current,Ro(Mo,e),Ro(Oo,Oo.current),!0}function _o(e,t,n){var r=e.stateNode;if(!r)throw Error(a(169));n?(e=zo(e,t,To),r.__reactInternalMemoizedMergedChildContext=e,Eo(Oo),Eo(Mo),Ro(Mo,e)):Eo(Oo),Ro(Oo,n)}var Fo=null,Do=!1,Wo=!1;function Bo(e){null===Fo?Fo=[e]:Fo.push(e)}function Vo(){if(!Wo&&null!==Fo){Wo=!0;var e=0,t=bt;try{var n=Fo;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Fo=null,Do=!1}catch(o){throw null!==Fo&&(Fo=Fo.slice(e+1)),Ke(Ze,Vo),o}finally{bt=t,Wo=!1}}return null}var Ho=[],Uo=0,Xo=null,Ko=0,qo=[],Go=0,Qo=null,Yo=1,Jo="";function Zo(e,t){Ho[Uo++]=Ko,Ho[Uo++]=Xo,Xo=e,Ko=t}function ea(e,t,n){qo[Go++]=Yo,qo[Go++]=Jo,qo[Go++]=Qo,Qo=e;var r=Yo;e=Jo;var o=32-it(r)-1;r&=~(1<<o),n+=1;var a=32-it(t)+o;if(30<a){var i=o-o%5;a=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Yo=1<<32-it(t)+o|n<<o|r,Jo=a+e}else Yo=1<<a|n<<o|r,Jo=e}function ta(e){null!==e.return&&(Zo(e,1),ea(e,1,0))}function na(e){for(;e===Xo;)Xo=Ho[--Uo],Ho[Uo]=null,Ko=Ho[--Uo],Ho[Uo]=null;for(;e===Qo;)Qo=qo[--Go],qo[Go]=null,Jo=qo[--Go],qo[Go]=null,Yo=qo[--Go],qo[Go]=null}var ra=null,oa=null,aa=!1,ia=null;function la(e,t){var n=Tc(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function sa(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ra=e,oa=co(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ra=e,oa=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Qo?{id:Yo,overflow:Jo}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Tc(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ra=e,oa=null,!0);default:return!1}}function ca(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ua(e){if(aa){var t=oa;if(t){var n=t;if(!sa(e,t)){if(ca(e))throw Error(a(418));t=co(n.nextSibling);var r=ra;t&&sa(e,t)?la(r,n):(e.flags=-4097&e.flags|2,aa=!1,ra=e)}}else{if(ca(e))throw Error(a(418));e.flags=-4097&e.flags|2,aa=!1,ra=e}}}function da(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ra=e}function pa(e){if(e!==ra)return!1;if(!aa)return da(e),aa=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!no(e.type,e.memoizedProps)),t&&(t=oa)){if(ca(e))throw fa(),Error(a(418));for(;t;)la(e,t),t=co(t.nextSibling)}if(da(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(a(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){oa=co(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}oa=null}}else oa=ra?co(e.stateNode.nextSibling):null;return!0}function fa(){for(var e=oa;e;)e=co(e.nextSibling)}function ma(){oa=ra=null,aa=!1}function ha(e){null===ia?ia=[e]:ia.push(e)}var ga=x.ReactCurrentBatchConfig;function va(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(a(309));var r=n.stateNode}if(!r)throw Error(a(147,e));var o=r,i=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===i?t.ref:(t=function(e){var t=o.refs;null===e?delete t[i]:t[i]=e},t._stringRef=i,t)}if("string"!==typeof e)throw Error(a(284));if(!n._owner)throw Error(a(290,e))}return e}function ya(e,t){throw e=Object.prototype.toString.call(t),Error(a(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ba(e){return(0,e._init)(e._payload)}function xa(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function o(e,t){return(e=Ic(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function l(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Lc(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function c(e,t,n,r){var a=n.type;return a===k?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"===typeof a&&null!==a&&a.$$typeof===j&&ba(a)===t.type)?((r=o(t,n.props)).ref=va(e,t,n),r.return=e,r):((r=Nc(n.type,n.key,n.props,null,e.mode,r)).ref=va(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=_c(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function d(e,t,n,r,a){return null===t||7!==t.tag?((t=$c(n,e.mode,r,a)).return=e,t):((t=o(t,n)).return=e,t)}function p(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Lc(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Nc(t.type,t.key,t.props,null,e.mode,n)).ref=va(e,null,t),n.return=e,n;case A:return(t=_c(t,e.mode,n)).return=e,t;case j:return p(e,(0,t._init)(t._payload),n)}if(te(t)||$(t))return(t=$c(t,e.mode,n,null)).return=e,t;ya(e,t)}return null}function f(e,t,n,r){var o=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==o?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===o?c(e,t,n,r):null;case A:return n.key===o?u(e,t,n,r):null;case j:return f(e,t,(o=n._init)(n._payload),r)}if(te(n)||$(n))return null!==o?null:d(e,t,n,r,null);ya(e,n)}return null}function m(e,t,n,r,o){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,o);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return c(t,e=e.get(null===r.key?n:r.key)||null,r,o);case A:return u(t,e=e.get(null===r.key?n:r.key)||null,r,o);case j:return m(e,t,n,(0,r._init)(r._payload),o)}if(te(r)||$(r))return d(t,e=e.get(n)||null,r,o,null);ya(t,r)}return null}function h(o,a,l,s){for(var c=null,u=null,d=a,h=a=0,g=null;null!==d&&h<l.length;h++){d.index>h?(g=d,d=null):g=d.sibling;var v=f(o,d,l[h],s);if(null===v){null===d&&(d=g);break}e&&d&&null===v.alternate&&t(o,d),a=i(v,a,h),null===u?c=v:u.sibling=v,u=v,d=g}if(h===l.length)return n(o,d),aa&&Zo(o,h),c;if(null===d){for(;h<l.length;h++)null!==(d=p(o,l[h],s))&&(a=i(d,a,h),null===u?c=d:u.sibling=d,u=d);return aa&&Zo(o,h),c}for(d=r(o,d);h<l.length;h++)null!==(g=m(d,o,h,l[h],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?h:g.key),a=i(g,a,h),null===u?c=g:u.sibling=g,u=g);return e&&d.forEach((function(e){return t(o,e)})),aa&&Zo(o,h),c}function g(o,l,s,c){var u=$(s);if("function"!==typeof u)throw Error(a(150));if(null==(s=u.call(s)))throw Error(a(151));for(var d=u=null,h=l,g=l=0,v=null,y=s.next();null!==h&&!y.done;g++,y=s.next()){h.index>g?(v=h,h=null):v=h.sibling;var b=f(o,h,y.value,c);if(null===b){null===h&&(h=v);break}e&&h&&null===b.alternate&&t(o,h),l=i(b,l,g),null===d?u=b:d.sibling=b,d=b,h=v}if(y.done)return n(o,h),aa&&Zo(o,g),u;if(null===h){for(;!y.done;g++,y=s.next())null!==(y=p(o,y.value,c))&&(l=i(y,l,g),null===d?u=y:d.sibling=y,d=y);return aa&&Zo(o,g),u}for(h=r(o,h);!y.done;g++,y=s.next())null!==(y=m(h,o,g,y.value,c))&&(e&&null!==y.alternate&&h.delete(null===y.key?g:y.key),l=i(y,l,g),null===d?u=y:d.sibling=y,d=y);return e&&h.forEach((function(e){return t(o,e)})),aa&&Zo(o,g),u}return function e(r,a,i,s){if("object"===typeof i&&null!==i&&i.type===k&&null===i.key&&(i=i.props.children),"object"===typeof i&&null!==i){switch(i.$$typeof){case w:e:{for(var c=i.key,u=a;null!==u;){if(u.key===c){if((c=i.type)===k){if(7===u.tag){n(r,u.sibling),(a=o(u,i.props.children)).return=r,r=a;break e}}else if(u.elementType===c||"object"===typeof c&&null!==c&&c.$$typeof===j&&ba(c)===u.type){n(r,u.sibling),(a=o(u,i.props)).ref=va(r,u,i),a.return=r,r=a;break e}n(r,u);break}t(r,u),u=u.sibling}i.type===k?((a=$c(i.props.children,r.mode,s,i.key)).return=r,r=a):((s=Nc(i.type,i.key,i.props,null,r.mode,s)).ref=va(r,a,i),s.return=r,r=s)}return l(r);case A:e:{for(u=i.key;null!==a;){if(a.key===u){if(4===a.tag&&a.stateNode.containerInfo===i.containerInfo&&a.stateNode.implementation===i.implementation){n(r,a.sibling),(a=o(a,i.children||[])).return=r,r=a;break e}n(r,a);break}t(r,a),a=a.sibling}(a=_c(i,r.mode,s)).return=r,r=a}return l(r);case j:return e(r,a,(u=i._init)(i._payload),s)}if(te(i))return h(r,a,i,s);if($(i))return g(r,a,i,s);ya(r,i)}return"string"===typeof i&&""!==i||"number"===typeof i?(i=""+i,null!==a&&6===a.tag?(n(r,a.sibling),(a=o(a,i)).return=r,r=a):(n(r,a),(a=Lc(i,r.mode,s)).return=r,r=a),l(r)):n(r,a)}}var wa=xa(!0),Aa=xa(!1),ka=Co(null),Sa=null,Ca=null,Ea=null;function Ra(){Ea=Ca=Sa=null}function Pa(e){var t=ka.current;Eo(ka),e._currentValue=t}function Ma(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Oa(e,t){Sa=e,Ea=Ca=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bl=!0),e.firstContext=null)}function Ta(e){var t=e._currentValue;if(Ea!==e)if(e={context:e,memoizedValue:t,next:null},null===Ca){if(null===Sa)throw Error(a(308));Ca=e,Sa.dependencies={lanes:0,firstContext:e}}else Ca=Ca.next=e;return t}var ja=null;function Ia(e){null===ja?ja=[e]:ja.push(e)}function Na(e,t,n,r){var o=t.interleaved;return null===o?(n.next=n,Ia(t)):(n.next=o.next,o.next=n),t.interleaved=n,$a(e,r)}function $a(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var za=!1;function La(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function _a(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Fa(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Da(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Ps)){var o=r.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,$a(e,n)}return null===(o=r.interleaved)?(t.next=t,Ia(r)):(t.next=o.next,o.next=t),r.interleaved=t,$a(e,n)}function Wa(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function Ba(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===a?o=a=i:a=a.next=i,n=n.next}while(null!==n);null===a?o=a=t:a=a.next=t}else o=a=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:a,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Va(e,t,n,r){var o=e.updateQueue;za=!1;var a=o.firstBaseUpdate,i=o.lastBaseUpdate,l=o.shared.pending;if(null!==l){o.shared.pending=null;var s=l,c=s.next;s.next=null,null===i?a=c:i.next=c,i=s;var u=e.alternate;null!==u&&((l=(u=u.updateQueue).lastBaseUpdate)!==i&&(null===l?u.firstBaseUpdate=c:l.next=c,u.lastBaseUpdate=s))}if(null!==a){var d=o.baseState;for(i=0,u=c=s=null,l=a;;){var p=l.lane,f=l.eventTime;if((r&p)===p){null!==u&&(u=u.next={eventTime:f,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var m=e,h=l;switch(p=t,f=n,h.tag){case 1:if("function"===typeof(m=h.payload)){d=m.call(f,d,p);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(p="function"===typeof(m=h.payload)?m.call(f,d,p):m)||void 0===p)break e;d=L({},d,p);break e;case 2:za=!0}}null!==l.callback&&0!==l.lane&&(e.flags|=64,null===(p=o.effects)?o.effects=[l]:p.push(l))}else f={eventTime:f,lane:p,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===u?(c=u=f,s=d):u=u.next=f,i|=p;if(null===(l=l.next)){if(null===(l=o.shared.pending))break;l=(p=l).next,p.next=null,o.lastBaseUpdate=p,o.shared.pending=null}}if(null===u&&(s=d),o.baseState=s,o.firstBaseUpdate=c,o.lastBaseUpdate=u,null!==(t=o.shared.interleaved)){o=t;do{i|=o.lane,o=o.next}while(o!==t)}else null===a&&(o.shared.lanes=0);zs|=i,e.lanes=i,e.memoizedState=d}}function Ha(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(null!==o){if(r.callback=null,r=n,"function"!==typeof o)throw Error(a(191,o));o.call(r)}}}var Ua={},Xa=Co(Ua),Ka=Co(Ua),qa=Co(Ua);function Ga(e){if(e===Ua)throw Error(a(174));return e}function Qa(e,t){switch(Ro(qa,t),Ro(Ka,e),Ro(Xa,Ua),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Eo(Xa),Ro(Xa,t)}function Ya(){Eo(Xa),Eo(Ka),Eo(qa)}function Ja(e){Ga(qa.current);var t=Ga(Xa.current),n=se(t,e.type);t!==n&&(Ro(Ka,e),Ro(Xa,n))}function Za(e){Ka.current===e&&(Eo(Xa),Eo(Ka))}var ei=Co(0);function ti(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ni=[];function ri(){for(var e=0;e<ni.length;e++)ni[e]._workInProgressVersionPrimary=null;ni.length=0}var oi=x.ReactCurrentDispatcher,ai=x.ReactCurrentBatchConfig,ii=0,li=null,si=null,ci=null,ui=!1,di=!1,pi=0,fi=0;function mi(){throw Error(a(321))}function hi(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!lr(e[n],t[n]))return!1;return!0}function gi(e,t,n,r,o,i){if(ii=i,li=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,oi.current=null===e||null===e.memoizedState?Zi:el,e=n(r,o),di){i=0;do{if(di=!1,pi=0,25<=i)throw Error(a(301));i+=1,ci=si=null,t.updateQueue=null,oi.current=tl,e=n(r,o)}while(di)}if(oi.current=Ji,t=null!==si&&null!==si.next,ii=0,ci=si=li=null,ui=!1,t)throw Error(a(300));return e}function vi(){var e=0!==pi;return pi=0,e}function yi(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ci?li.memoizedState=ci=e:ci=ci.next=e,ci}function bi(){if(null===si){var e=li.alternate;e=null!==e?e.memoizedState:null}else e=si.next;var t=null===ci?li.memoizedState:ci.next;if(null!==t)ci=t,si=e;else{if(null===e)throw Error(a(310));e={memoizedState:(si=e).memoizedState,baseState:si.baseState,baseQueue:si.baseQueue,queue:si.queue,next:null},null===ci?li.memoizedState=ci=e:ci=ci.next=e}return ci}function xi(e,t){return"function"===typeof t?t(e):t}function wi(e){var t=bi(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=si,o=r.baseQueue,i=n.pending;if(null!==i){if(null!==o){var l=o.next;o.next=i.next,i.next=l}r.baseQueue=o=i,n.pending=null}if(null!==o){i=o.next,r=r.baseState;var s=l=null,c=null,u=i;do{var d=u.lane;if((ii&d)===d)null!==c&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var p={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===c?(s=c=p,l=r):c=c.next=p,li.lanes|=d,zs|=d}u=u.next}while(null!==u&&u!==i);null===c?l=r:c.next=s,lr(r,t.memoizedState)||(bl=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=c,n.lastRenderedState=r}if(null!==(e=n.interleaved)){o=e;do{i=o.lane,li.lanes|=i,zs|=i,o=o.next}while(o!==e)}else null===o&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ai(e){var t=bi(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(null!==o){n.pending=null;var l=o=o.next;do{i=e(i,l.action),l=l.next}while(l!==o);lr(i,t.memoizedState)||(bl=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function ki(){}function Si(e,t){var n=li,r=bi(),o=t(),i=!lr(r.memoizedState,o);if(i&&(r.memoizedState=o,bl=!0),r=r.queue,zi(Ri.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||null!==ci&&1&ci.memoizedState.tag){if(n.flags|=2048,Ti(9,Ei.bind(null,n,r,o,t),void 0,null),null===Ms)throw Error(a(349));0!==(30&ii)||Ci(n,t,o)}return o}function Ci(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=li.updateQueue)?(t={lastEffect:null,stores:null},li.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ei(e,t,n,r){t.value=n,t.getSnapshot=r,Pi(t)&&Mi(e)}function Ri(e,t,n){return n((function(){Pi(t)&&Mi(e)}))}function Pi(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!lr(e,n)}catch(r){return!0}}function Mi(e){var t=$a(e,1);null!==t&&nc(t,e,1,-1)}function Oi(e){var t=yi();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xi,lastRenderedState:e},t.queue=e,e=e.dispatch=qi.bind(null,li,e),[t.memoizedState,e]}function Ti(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=li.updateQueue)?(t={lastEffect:null,stores:null},li.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function ji(){return bi().memoizedState}function Ii(e,t,n,r){var o=yi();li.flags|=e,o.memoizedState=Ti(1|t,n,void 0,void 0===r?null:r)}function Ni(e,t,n,r){var o=bi();r=void 0===r?null:r;var a=void 0;if(null!==si){var i=si.memoizedState;if(a=i.destroy,null!==r&&hi(r,i.deps))return void(o.memoizedState=Ti(t,n,a,r))}li.flags|=e,o.memoizedState=Ti(1|t,n,a,r)}function $i(e,t){return Ii(8390656,8,e,t)}function zi(e,t){return Ni(2048,8,e,t)}function Li(e,t){return Ni(4,2,e,t)}function _i(e,t){return Ni(4,4,e,t)}function Fi(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Di(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ni(4,4,Fi.bind(null,t,e),n)}function Wi(){}function Bi(e,t){var n=bi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&hi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Vi(e,t){var n=bi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&hi(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Hi(e,t,n){return 0===(21&ii)?(e.baseState&&(e.baseState=!1,bl=!0),e.memoizedState=n):(lr(n,t)||(n=ht(),li.lanes|=n,zs|=n,e.baseState=!0),t)}function Ui(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=ai.transition;ai.transition={};try{e(!1),t()}finally{bt=n,ai.transition=r}}function Xi(){return bi().memoizedState}function Ki(e,t,n){var r=tc(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Gi(e))Qi(t,n);else if(null!==(n=Na(e,t,n,r))){nc(n,e,r,ec()),Yi(n,t,r)}}function qi(e,t,n){var r=tc(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Gi(e))Qi(t,o);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var i=t.lastRenderedState,l=a(i,n);if(o.hasEagerState=!0,o.eagerState=l,lr(l,i)){var s=t.interleaved;return null===s?(o.next=o,Ia(t)):(o.next=s.next,s.next=o),void(t.interleaved=o)}}catch(c){}null!==(n=Na(e,t,o,r))&&(nc(n,e,r,o=ec()),Yi(n,t,r))}}function Gi(e){var t=e.alternate;return e===li||null!==t&&t===li}function Qi(e,t){di=ui=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Yi(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var Ji={readContext:Ta,useCallback:mi,useContext:mi,useEffect:mi,useImperativeHandle:mi,useInsertionEffect:mi,useLayoutEffect:mi,useMemo:mi,useReducer:mi,useRef:mi,useState:mi,useDebugValue:mi,useDeferredValue:mi,useTransition:mi,useMutableSource:mi,useSyncExternalStore:mi,useId:mi,unstable_isNewReconciler:!1},Zi={readContext:Ta,useCallback:function(e,t){return yi().memoizedState=[e,void 0===t?null:t],e},useContext:Ta,useEffect:$i,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ii(4194308,4,Fi.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ii(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ii(4,2,e,t)},useMemo:function(e,t){var n=yi();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=yi();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ki.bind(null,li,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},yi().memoizedState=e},useState:Oi,useDebugValue:Wi,useDeferredValue:function(e){return yi().memoizedState=e},useTransition:function(){var e=Oi(!1),t=e[0];return e=Ui.bind(null,e[1]),yi().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=li,o=yi();if(aa){if(void 0===n)throw Error(a(407));n=n()}else{if(n=t(),null===Ms)throw Error(a(349));0!==(30&ii)||Ci(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,$i(Ri.bind(null,r,i,e),[e]),r.flags|=2048,Ti(9,Ei.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=yi(),t=Ms.identifierPrefix;if(aa){var n=Jo;t=":"+t+"R"+(n=(Yo&~(1<<32-it(Yo)-1)).toString(32)+n),0<(n=pi++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=fi++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},el={readContext:Ta,useCallback:Bi,useContext:Ta,useEffect:zi,useImperativeHandle:Di,useInsertionEffect:Li,useLayoutEffect:_i,useMemo:Vi,useReducer:wi,useRef:ji,useState:function(){return wi(xi)},useDebugValue:Wi,useDeferredValue:function(e){return Hi(bi(),si.memoizedState,e)},useTransition:function(){return[wi(xi)[0],bi().memoizedState]},useMutableSource:ki,useSyncExternalStore:Si,useId:Xi,unstable_isNewReconciler:!1},tl={readContext:Ta,useCallback:Bi,useContext:Ta,useEffect:zi,useImperativeHandle:Di,useInsertionEffect:Li,useLayoutEffect:_i,useMemo:Vi,useReducer:Ai,useRef:ji,useState:function(){return Ai(xi)},useDebugValue:Wi,useDeferredValue:function(e){var t=bi();return null===si?t.memoizedState=e:Hi(t,si.memoizedState,e)},useTransition:function(){return[Ai(xi)[0],bi().memoizedState]},useMutableSource:ki,useSyncExternalStore:Si,useId:Xi,unstable_isNewReconciler:!1};function nl(e,t){if(e&&e.defaultProps){for(var n in t=L({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function rl(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:L({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ol={isMounted:function(e){return!!(e=e._reactInternals)&&Be(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ec(),o=tc(e),a=Fa(r,o);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=Da(e,a,o))&&(nc(t,e,o,r),Wa(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ec(),o=tc(e),a=Fa(r,o);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=Da(e,a,o))&&(nc(t,e,o,r),Wa(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ec(),r=tc(e),o=Fa(n,r);o.tag=2,void 0!==t&&null!==t&&(o.callback=t),null!==(t=Da(e,o,r))&&(nc(t,e,r,n),Wa(t,e,r))}};function al(e,t,n,r,o,a,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,i):!t.prototype||!t.prototype.isPureReactComponent||(!sr(n,r)||!sr(o,a))}function il(e,t,n){var r=!1,o=Po,a=t.contextType;return"object"===typeof a&&null!==a?a=Ta(a):(o=Io(t)?To:Mo.current,a=(r=null!==(r=t.contextTypes)&&void 0!==r)?jo(e,o):Po),t=new t(n,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ol,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=a),t}function ll(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ol.enqueueReplaceState(t,t.state,null)}function sl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},La(e);var a=t.contextType;"object"===typeof a&&null!==a?o.context=Ta(a):(a=Io(t)?To:Mo.current,o.context=jo(e,a)),o.state=e.memoizedState,"function"===typeof(a=t.getDerivedStateFromProps)&&(rl(e,t,a,n),o.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof o.getSnapshotBeforeUpdate||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||(t=o.state,"function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),t!==o.state&&ol.enqueueReplaceState(o,o.state,null),Va(e,n,o,r),o.state=e.memoizedState),"function"===typeof o.componentDidMount&&(e.flags|=4194308)}function cl(e,t){try{var n="",r=t;do{n+=W(r),r=r.return}while(r);var o=n}catch(a){o="\nError generating stack: "+a.message+"\n"+a.stack}return{value:e,source:t,stack:o,digest:null}}function ul(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function dl(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var pl="function"===typeof WeakMap?WeakMap:Map;function fl(e,t,n){(n=Fa(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Hs||(Hs=!0,Us=r),dl(0,t)},n}function ml(e,t,n){(n=Fa(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){dl(0,t)}}var a=e.stateNode;return null!==a&&"function"===typeof a.componentDidCatch&&(n.callback=function(){dl(0,t),"function"!==typeof r&&(null===Xs?Xs=new Set([this]):Xs.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function hl(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new pl;var o=new Set;r.set(t,o)}else void 0===(o=r.get(t))&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Cc.bind(null,e,t,n),t.then(e,e))}function gl(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function vl(e,t,n,r,o){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Fa(-1,1)).tag=2,Da(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=o,e)}var yl=x.ReactCurrentOwner,bl=!1;function xl(e,t,n,r){t.child=null===e?Aa(t,null,n,r):wa(t,e.child,n,r)}function wl(e,t,n,r,o){n=n.render;var a=t.ref;return Oa(t,o),r=gi(e,t,n,r,a,o),n=vi(),null===e||bl?(aa&&n&&ta(t),t.flags|=1,xl(e,t,r,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Hl(e,t,o))}function Al(e,t,n,r,o){if(null===e){var a=n.type;return"function"!==typeof a||jc(a)||void 0!==a.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Nc(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,kl(e,t,a,r,o))}if(a=e.child,0===(e.lanes&o)){var i=a.memoizedProps;if((n=null!==(n=n.compare)?n:sr)(i,r)&&e.ref===t.ref)return Hl(e,t,o)}return t.flags|=1,(e=Ic(a,r)).ref=t.ref,e.return=t,t.child=e}function kl(e,t,n,r,o){if(null!==e){var a=e.memoizedProps;if(sr(a,r)&&e.ref===t.ref){if(bl=!1,t.pendingProps=r=a,0===(e.lanes&o))return t.lanes=e.lanes,Hl(e,t,o);0!==(131072&e.flags)&&(bl=!0)}}return El(e,t,n,r,o)}function Sl(e,t,n){var r=t.pendingProps,o=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ro(Is,js),js|=n;else{if(0===(1073741824&n))return e=null!==a?a.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ro(Is,js),js|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==a?a.baseLanes:n,Ro(Is,js),js|=r}else null!==a?(r=a.baseLanes|n,t.memoizedState=null):r=n,Ro(Is,js),js|=r;return xl(e,t,o,n),t.child}function Cl(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function El(e,t,n,r,o){var a=Io(n)?To:Mo.current;return a=jo(t,a),Oa(t,o),n=gi(e,t,n,r,a,o),r=vi(),null===e||bl?(aa&&r&&ta(t),t.flags|=1,xl(e,t,n,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Hl(e,t,o))}function Rl(e,t,n,r,o){if(Io(n)){var a=!0;Lo(t)}else a=!1;if(Oa(t,o),null===t.stateNode)Vl(e,t),il(t,n,r),sl(t,n,r,o),r=!0;else if(null===e){var i=t.stateNode,l=t.memoizedProps;i.props=l;var s=i.context,c=n.contextType;"object"===typeof c&&null!==c?c=Ta(c):c=jo(t,c=Io(n)?To:Mo.current);var u=n.getDerivedStateFromProps,d="function"===typeof u||"function"===typeof i.getSnapshotBeforeUpdate;d||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(l!==r||s!==c)&&ll(t,i,r,c),za=!1;var p=t.memoizedState;i.state=p,Va(t,r,i,o),s=t.memoizedState,l!==r||p!==s||Oo.current||za?("function"===typeof u&&(rl(t,n,u,r),s=t.memoizedState),(l=za||al(t,n,l,r,p,s,c))?(d||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||("function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"===typeof i.componentDidMount&&(t.flags|=4194308)):("function"===typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),i.props=r,i.state=s,i.context=c,r=l):("function"===typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,_a(e,t),l=t.memoizedProps,c=t.type===t.elementType?l:nl(t.type,l),i.props=c,d=t.pendingProps,p=i.context,"object"===typeof(s=n.contextType)&&null!==s?s=Ta(s):s=jo(t,s=Io(n)?To:Mo.current);var f=n.getDerivedStateFromProps;(u="function"===typeof f||"function"===typeof i.getSnapshotBeforeUpdate)||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(l!==d||p!==s)&&ll(t,i,r,s),za=!1,p=t.memoizedState,i.state=p,Va(t,r,i,o);var m=t.memoizedState;l!==d||p!==m||Oo.current||za?("function"===typeof f&&(rl(t,n,f,r),m=t.memoizedState),(c=za||al(t,n,c,r,p,m,s)||!1)?(u||"function"!==typeof i.UNSAFE_componentWillUpdate&&"function"!==typeof i.componentWillUpdate||("function"===typeof i.componentWillUpdate&&i.componentWillUpdate(r,m,s),"function"===typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,m,s)),"function"===typeof i.componentDidUpdate&&(t.flags|=4),"function"===typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof i.componentDidUpdate||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=m),i.props=r,i.state=m,i.context=s,r=c):("function"!==typeof i.componentDidUpdate||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return Pl(e,t,n,r,a,o)}function Pl(e,t,n,r,o,a){Cl(e,t);var i=0!==(128&t.flags);if(!r&&!i)return o&&_o(t,n,!1),Hl(e,t,a);r=t.stateNode,yl.current=t;var l=i&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=wa(t,e.child,null,a),t.child=wa(t,null,l,a)):xl(e,t,l,a),t.memoizedState=r.state,o&&_o(t,n,!0),t.child}function Ml(e){var t=e.stateNode;t.pendingContext?$o(0,t.pendingContext,t.pendingContext!==t.context):t.context&&$o(0,t.context,!1),Qa(e,t.containerInfo)}function Ol(e,t,n,r,o){return ma(),ha(o),t.flags|=256,xl(e,t,n,r),t.child}var Tl,jl,Il,Nl,$l={dehydrated:null,treeContext:null,retryLane:0};function zl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ll(e,t,n){var r,o=t.pendingProps,i=ei.current,l=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&i)),r?(l=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(i|=1),Ro(ei,1&i),null===e)return ua(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=o.children,e=o.fallback,l?(o=t.mode,l=t.child,s={mode:"hidden",children:s},0===(1&o)&&null!==l?(l.childLanes=0,l.pendingProps=s):l=zc(s,o,0,null),e=$c(e,o,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=zl(n),t.memoizedState=$l,e):_l(t,s));if(null!==(i=e.memoizedState)&&null!==(r=i.dehydrated))return function(e,t,n,r,o,i,l){if(n)return 256&t.flags?(t.flags&=-257,Fl(e,t,l,r=ul(Error(a(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=zc({mode:"visible",children:r.children},o,0,null),(i=$c(i,o,l,null)).flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,0!==(1&t.mode)&&wa(t,e.child,null,l),t.child.memoizedState=zl(l),t.memoizedState=$l,i);if(0===(1&t.mode))return Fl(e,t,l,null);if("$!"===o.data){if(r=o.nextSibling&&o.nextSibling.dataset)var s=r.dgst;return r=s,Fl(e,t,l,r=ul(i=Error(a(419)),r,void 0))}if(s=0!==(l&e.childLanes),bl||s){if(null!==(r=Ms)){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}0!==(o=0!==(o&(r.suspendedLanes|l))?0:o)&&o!==i.retryLane&&(i.retryLane=o,$a(e,o),nc(r,e,o,-1))}return hc(),Fl(e,t,l,r=ul(Error(a(421))))}return"$?"===o.data?(t.flags|=128,t.child=e.child,t=Rc.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,oa=co(o.nextSibling),ra=t,aa=!0,ia=null,null!==e&&(qo[Go++]=Yo,qo[Go++]=Jo,qo[Go++]=Qo,Yo=e.id,Jo=e.overflow,Qo=t),t=_l(t,r.children),t.flags|=4096,t)}(e,t,s,o,r,i,n);if(l){l=o.fallback,s=t.mode,r=(i=e.child).sibling;var c={mode:"hidden",children:o.children};return 0===(1&s)&&t.child!==i?((o=t.child).childLanes=0,o.pendingProps=c,t.deletions=null):(o=Ic(i,c)).subtreeFlags=14680064&i.subtreeFlags,null!==r?l=Ic(r,l):(l=$c(l,s,n,null)).flags|=2,l.return=t,o.return=t,o.sibling=l,t.child=o,o=l,l=t.child,s=null===(s=e.child.memoizedState)?zl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},l.memoizedState=s,l.childLanes=e.childLanes&~n,t.memoizedState=$l,o}return e=(l=e.child).sibling,o=Ic(l,{mode:"visible",children:o.children}),0===(1&t.mode)&&(o.lanes=n),o.return=t,o.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=o,t.memoizedState=null,o}function _l(e,t){return(t=zc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Fl(e,t,n,r){return null!==r&&ha(r),wa(t,e.child,null,n),(e=_l(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Dl(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Ma(e.return,t,n)}function Wl(e,t,n,r,o){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=o)}function Bl(e,t,n){var r=t.pendingProps,o=r.revealOrder,a=r.tail;if(xl(e,t,r.children,n),0!==(2&(r=ei.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Dl(e,n,t);else if(19===e.tag)Dl(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ro(ei,r),0===(1&t.mode))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===ti(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Wl(t,!1,o,n,a);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===ti(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Wl(t,!0,n,null,a);break;case"together":Wl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Vl(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Hl(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),zs|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(a(153));if(null!==t.child){for(n=Ic(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ic(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Ul(e,t){if(!aa)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Xl(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=14680064&o.subtreeFlags,r|=14680064&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Kl(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Xl(t),null;case 1:case 17:return Io(t.type)&&No(),Xl(t),null;case 3:return r=t.stateNode,Ya(),Eo(Oo),Eo(Mo),ri(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(pa(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ia&&(ic(ia),ia=null))),jl(e,t),Xl(t),null;case 5:Za(t);var o=Ga(qa.current);if(n=t.type,null!==e&&null!=t.stateNode)Il(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(a(166));return Xl(t),null}if(e=Ga(Xa.current),pa(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[fo]=t,r[mo]=i,e=0!==(1&t.mode),n){case"dialog":Fr("cancel",r),Fr("close",r);break;case"iframe":case"object":case"embed":Fr("load",r);break;case"video":case"audio":for(o=0;o<$r.length;o++)Fr($r[o],r);break;case"source":Fr("error",r);break;case"img":case"image":case"link":Fr("error",r),Fr("load",r);break;case"details":Fr("toggle",r);break;case"input":Q(r,i),Fr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Fr("invalid",r);break;case"textarea":oe(r,i),Fr("invalid",r)}for(var s in ye(n,i),o=null,i)if(i.hasOwnProperty(s)){var c=i[s];"children"===s?"string"===typeof c?r.textContent!==c&&(!0!==i.suppressHydrationWarning&&Jr(r.textContent,c,e),o=["children",c]):"number"===typeof c&&r.textContent!==""+c&&(!0!==i.suppressHydrationWarning&&Jr(r.textContent,c,e),o=["children",""+c]):l.hasOwnProperty(s)&&null!=c&&"onScroll"===s&&Fr("scroll",r)}switch(n){case"input":X(r),Z(r,i,!0);break;case"textarea":X(r),ie(r);break;case"select":case"option":break;default:"function"===typeof i.onClick&&(r.onclick=Zr)}r=o,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===o.nodeType?o:o.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=le(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[fo]=t,e[mo]=r,Tl(e,t,!1,!1),t.stateNode=e;e:{switch(s=be(n,r),n){case"dialog":Fr("cancel",e),Fr("close",e),o=r;break;case"iframe":case"object":case"embed":Fr("load",e),o=r;break;case"video":case"audio":for(o=0;o<$r.length;o++)Fr($r[o],e);o=r;break;case"source":Fr("error",e),o=r;break;case"img":case"image":case"link":Fr("error",e),Fr("load",e),o=r;break;case"details":Fr("toggle",e),o=r;break;case"input":Q(e,r),o=G(e,r),Fr("invalid",e);break;case"option":default:o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=L({},r,{value:void 0}),Fr("invalid",e);break;case"textarea":oe(e,r),o=re(e,r),Fr("invalid",e)}for(i in ye(n,o),c=o)if(c.hasOwnProperty(i)){var u=c[i];"style"===i?ge(e,u):"dangerouslySetInnerHTML"===i?null!=(u=u?u.__html:void 0)&&de(e,u):"children"===i?"string"===typeof u?("textarea"!==n||""!==u)&&pe(e,u):"number"===typeof u&&pe(e,""+u):"suppressContentEditableWarning"!==i&&"suppressHydrationWarning"!==i&&"autoFocus"!==i&&(l.hasOwnProperty(i)?null!=u&&"onScroll"===i&&Fr("scroll",e):null!=u&&b(e,i,u,s))}switch(n){case"input":X(e),Z(e,r,!1);break;case"textarea":X(e),ie(e);break;case"option":null!=r.value&&e.setAttribute("value",""+H(r.value));break;case"select":e.multiple=!!r.multiple,null!=(i=r.value)?ne(e,!!r.multiple,i,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof o.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Xl(t),null;case 6:if(e&&null!=t.stateNode)Nl(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(a(166));if(n=Ga(qa.current),Ga(Xa.current),pa(t)){if(r=t.stateNode,n=t.memoizedProps,r[fo]=t,(i=r.nodeValue!==n)&&null!==(e=ra))switch(e.tag){case 3:Jr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Jr(r.nodeValue,n,0!==(1&e.mode))}i&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fo]=t,t.stateNode=r}return Xl(t),null;case 13:if(Eo(ei),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(aa&&null!==oa&&0!==(1&t.mode)&&0===(128&t.flags))fa(),ma(),t.flags|=98560,i=!1;else if(i=pa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!i)throw Error(a(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(a(317));i[fo]=t}else ma(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Xl(t),i=!1}else null!==ia&&(ic(ia),ia=null),i=!0;if(!i)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&ei.current)?0===Ns&&(Ns=3):hc())),null!==t.updateQueue&&(t.flags|=4),Xl(t),null);case 4:return Ya(),jl(e,t),null===e&&Br(t.stateNode.containerInfo),Xl(t),null;case 10:return Pa(t.type._context),Xl(t),null;case 19:if(Eo(ei),null===(i=t.memoizedState))return Xl(t),null;if(r=0!==(128&t.flags),null===(s=i.rendering))if(r)Ul(i,!1);else{if(0!==Ns||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=ti(e))){for(t.flags|=128,Ul(i,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(i=n).flags&=14680066,null===(s=i.alternate)?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ro(ei,1&ei.current|2),t.child}e=e.sibling}null!==i.tail&&Ye()>Bs&&(t.flags|=128,r=!0,Ul(i,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ti(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Ul(i,!0),null===i.tail&&"hidden"===i.tailMode&&!s.alternate&&!aa)return Xl(t),null}else 2*Ye()-i.renderingStartTime>Bs&&1073741824!==n&&(t.flags|=128,r=!0,Ul(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=i.last)?n.sibling=s:t.child=s,i.last=s)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Ye(),t.sibling=null,n=ei.current,Ro(ei,r?1&n|2:1&n),t):(Xl(t),null);case 22:case 23:return dc(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&js)&&(Xl(t),6&t.subtreeFlags&&(t.flags|=8192)):Xl(t),null;case 24:case 25:return null}throw Error(a(156,t.tag))}function ql(e,t){switch(na(t),t.tag){case 1:return Io(t.type)&&No(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Ya(),Eo(Oo),Eo(Mo),ri(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Za(t),null;case 13:if(Eo(ei),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(a(340));ma()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Eo(ei),null;case 4:return Ya(),null;case 10:return Pa(t.type._context),null;case 22:case 23:return dc(),null;default:return null}}Tl=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},jl=function(){},Il=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Ga(Xa.current);var a,i=null;switch(n){case"input":o=G(e,o),r=G(e,r),i=[];break;case"select":o=L({},o,{value:void 0}),r=L({},r,{value:void 0}),i=[];break;case"textarea":o=re(e,o),r=re(e,r),i=[];break;default:"function"!==typeof o.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(u in ye(n,r),n=null,o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&null!=o[u])if("style"===u){var s=o[u];for(a in s)s.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(l.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var c=r[u];if(s=null!=o?o[u]:void 0,r.hasOwnProperty(u)&&c!==s&&(null!=c||null!=s))if("style"===u)if(s){for(a in s)!s.hasOwnProperty(a)||c&&c.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in c)c.hasOwnProperty(a)&&s[a]!==c[a]&&(n||(n={}),n[a]=c[a])}else n||(i||(i=[]),i.push(u,n)),n=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,s=s?s.__html:void 0,null!=c&&s!==c&&(i=i||[]).push(u,c)):"children"===u?"string"!==typeof c&&"number"!==typeof c||(i=i||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(l.hasOwnProperty(u)?(null!=c&&"onScroll"===u&&Fr("scroll",e),i||s===c||(i=[])):(i=i||[]).push(u,c))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}},Nl=function(e,t,n,r){n!==r&&(t.flags|=4)};var Gl=!1,Ql=!1,Yl="function"===typeof WeakSet?WeakSet:Set,Jl=null;function Zl(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Sc(e,t,r)}else n.current=null}function es(e,t,n){try{n()}catch(r){Sc(e,t,r)}}var ts=!1;function ns(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var o=r=r.next;do{if((o.tag&e)===e){var a=o.destroy;o.destroy=void 0,void 0!==a&&es(t,n,a)}o=o.next}while(o!==r)}}function rs(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function os(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function as(e){var t=e.alternate;null!==t&&(e.alternate=null,as(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fo],delete t[mo],delete t[go],delete t[vo],delete t[yo])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function is(e){return 5===e.tag||3===e.tag||4===e.tag}function ls(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||is(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ss(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(ss(e,t,n),e=e.sibling;null!==e;)ss(e,t,n),e=e.sibling}function cs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(cs(e,t,n),e=e.sibling;null!==e;)cs(e,t,n),e=e.sibling}var us=null,ds=!1;function ps(e,t,n){for(n=n.child;null!==n;)fs(e,t,n),n=n.sibling}function fs(e,t,n){if(at&&"function"===typeof at.onCommitFiberUnmount)try{at.onCommitFiberUnmount(ot,n)}catch(l){}switch(n.tag){case 5:Ql||Zl(n,t);case 6:var r=us,o=ds;us=null,ps(e,t,n),ds=o,null!==(us=r)&&(ds?(e=us,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):us.removeChild(n.stateNode));break;case 18:null!==us&&(ds?(e=us,n=n.stateNode,8===e.nodeType?so(e.parentNode,n):1===e.nodeType&&so(e,n),Bt(e)):so(us,n.stateNode));break;case 4:r=us,o=ds,us=n.stateNode.containerInfo,ds=!0,ps(e,t,n),us=r,ds=o;break;case 0:case 11:case 14:case 15:if(!Ql&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){o=r=r.next;do{var a=o,i=a.destroy;a=a.tag,void 0!==i&&(0!==(2&a)||0!==(4&a))&&es(n,t,i),o=o.next}while(o!==r)}ps(e,t,n);break;case 1:if(!Ql&&(Zl(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Sc(n,t,l)}ps(e,t,n);break;case 21:ps(e,t,n);break;case 22:1&n.mode?(Ql=(r=Ql)||null!==n.memoizedState,ps(e,t,n),Ql=r):ps(e,t,n);break;default:ps(e,t,n)}}function ms(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Yl),t.forEach((function(t){var r=Pc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function hs(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,l=t,s=l;e:for(;null!==s;){switch(s.tag){case 5:us=s.stateNode,ds=!1;break e;case 3:case 4:us=s.stateNode.containerInfo,ds=!0;break e}s=s.return}if(null===us)throw Error(a(160));fs(i,l,o),us=null,ds=!1;var c=o.alternate;null!==c&&(c.return=null),o.return=null}catch(u){Sc(o,t,u)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gs(t,e),t=t.sibling}function gs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(hs(t,e),vs(e),4&r){try{ns(3,e,e.return),rs(3,e)}catch(g){Sc(e,e.return,g)}try{ns(5,e,e.return)}catch(g){Sc(e,e.return,g)}}break;case 1:hs(t,e),vs(e),512&r&&null!==n&&Zl(n,n.return);break;case 5:if(hs(t,e),vs(e),512&r&&null!==n&&Zl(n,n.return),32&e.flags){var o=e.stateNode;try{pe(o,"")}catch(g){Sc(e,e.return,g)}}if(4&r&&null!=(o=e.stateNode)){var i=e.memoizedProps,l=null!==n?n.memoizedProps:i,s=e.type,c=e.updateQueue;if(e.updateQueue=null,null!==c)try{"input"===s&&"radio"===i.type&&null!=i.name&&Y(o,i),be(s,l);var u=be(s,i);for(l=0;l<c.length;l+=2){var d=c[l],p=c[l+1];"style"===d?ge(o,p):"dangerouslySetInnerHTML"===d?de(o,p):"children"===d?pe(o,p):b(o,d,p,u)}switch(s){case"input":J(o,i);break;case"textarea":ae(o,i);break;case"select":var f=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var m=i.value;null!=m?ne(o,!!i.multiple,m,!1):f!==!!i.multiple&&(null!=i.defaultValue?ne(o,!!i.multiple,i.defaultValue,!0):ne(o,!!i.multiple,i.multiple?[]:"",!1))}o[mo]=i}catch(g){Sc(e,e.return,g)}}break;case 6:if(hs(t,e),vs(e),4&r){if(null===e.stateNode)throw Error(a(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(g){Sc(e,e.return,g)}}break;case 3:if(hs(t,e),vs(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Bt(t.containerInfo)}catch(g){Sc(e,e.return,g)}break;case 4:default:hs(t,e),vs(e);break;case 13:hs(t,e),vs(e),8192&(o=e.child).flags&&(i=null!==o.memoizedState,o.stateNode.isHidden=i,!i||null!==o.alternate&&null!==o.alternate.memoizedState||(Ws=Ye())),4&r&&ms(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Ql=(u=Ql)||d,hs(t,e),Ql=u):hs(t,e),vs(e),8192&r){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!d&&0!==(1&e.mode))for(Jl=e,d=e.child;null!==d;){for(p=Jl=d;null!==Jl;){switch(m=(f=Jl).child,f.tag){case 0:case 11:case 14:case 15:ns(4,f,f.return);break;case 1:Zl(f,f.return);var h=f.stateNode;if("function"===typeof h.componentWillUnmount){r=f,n=f.return;try{t=r,h.props=t.memoizedProps,h.state=t.memoizedState,h.componentWillUnmount()}catch(g){Sc(r,n,g)}}break;case 5:Zl(f,f.return);break;case 22:if(null!==f.memoizedState){ws(p);continue}}null!==m?(m.return=f,Jl=m):ws(p)}d=d.sibling}e:for(d=null,p=e;;){if(5===p.tag){if(null===d){d=p;try{o=p.stateNode,u?"function"===typeof(i=o.style).setProperty?i.setProperty("display","none","important"):i.display="none":(s=p.stateNode,l=void 0!==(c=p.memoizedProps.style)&&null!==c&&c.hasOwnProperty("display")?c.display:null,s.style.display=he("display",l))}catch(g){Sc(e,e.return,g)}}}else if(6===p.tag){if(null===d)try{p.stateNode.nodeValue=u?"":p.memoizedProps}catch(g){Sc(e,e.return,g)}}else if((22!==p.tag&&23!==p.tag||null===p.memoizedState||p===e)&&null!==p.child){p.child.return=p,p=p.child;continue}if(p===e)break e;for(;null===p.sibling;){if(null===p.return||p.return===e)break e;d===p&&(d=null),p=p.return}d===p&&(d=null),p.sibling.return=p.return,p=p.sibling}}break;case 19:hs(t,e),vs(e),4&r&&ms(e);case 21:}}function vs(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(is(n)){var r=n;break e}n=n.return}throw Error(a(160))}switch(r.tag){case 5:var o=r.stateNode;32&r.flags&&(pe(o,""),r.flags&=-33),cs(e,ls(e),o);break;case 3:case 4:var i=r.stateNode.containerInfo;ss(e,ls(e),i);break;default:throw Error(a(161))}}catch(l){Sc(e,e.return,l)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function ys(e,t,n){Jl=e,bs(e,t,n)}function bs(e,t,n){for(var r=0!==(1&e.mode);null!==Jl;){var o=Jl,a=o.child;if(22===o.tag&&r){var i=null!==o.memoizedState||Gl;if(!i){var l=o.alternate,s=null!==l&&null!==l.memoizedState||Ql;l=Gl;var c=Ql;if(Gl=i,(Ql=s)&&!c)for(Jl=o;null!==Jl;)s=(i=Jl).child,22===i.tag&&null!==i.memoizedState?As(o):null!==s?(s.return=i,Jl=s):As(o);for(;null!==a;)Jl=a,bs(a,t,n),a=a.sibling;Jl=o,Gl=l,Ql=c}xs(e)}else 0!==(8772&o.subtreeFlags)&&null!==a?(a.return=o,Jl=a):xs(e)}}function xs(e){for(;null!==Jl;){var t=Jl;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Ql||rs(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Ql)if(null===n)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:nl(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;null!==i&&Ha(t,i,r);break;case 3:var l=t.updateQueue;if(null!==l){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Ha(t,l,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var d=u.memoizedState;if(null!==d){var p=d.dehydrated;null!==p&&Bt(p)}}}break;default:throw Error(a(163))}Ql||512&t.flags&&os(t)}catch(f){Sc(t,t.return,f)}}if(t===e){Jl=null;break}if(null!==(n=t.sibling)){n.return=t.return,Jl=n;break}Jl=t.return}}function ws(e){for(;null!==Jl;){var t=Jl;if(t===e){Jl=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Jl=n;break}Jl=t.return}}function As(e){for(;null!==Jl;){var t=Jl;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rs(4,t)}catch(s){Sc(t,n,s)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var o=t.return;try{r.componentDidMount()}catch(s){Sc(t,o,s)}}var a=t.return;try{os(t)}catch(s){Sc(t,a,s)}break;case 5:var i=t.return;try{os(t)}catch(s){Sc(t,i,s)}}}catch(s){Sc(t,t.return,s)}if(t===e){Jl=null;break}var l=t.sibling;if(null!==l){l.return=t.return,Jl=l;break}Jl=t.return}}var ks,Ss=Math.ceil,Cs=x.ReactCurrentDispatcher,Es=x.ReactCurrentOwner,Rs=x.ReactCurrentBatchConfig,Ps=0,Ms=null,Os=null,Ts=0,js=0,Is=Co(0),Ns=0,$s=null,zs=0,Ls=0,_s=0,Fs=null,Ds=null,Ws=0,Bs=1/0,Vs=null,Hs=!1,Us=null,Xs=null,Ks=!1,qs=null,Gs=0,Qs=0,Ys=null,Js=-1,Zs=0;function ec(){return 0!==(6&Ps)?Ye():-1!==Js?Js:Js=Ye()}function tc(e){return 0===(1&e.mode)?1:0!==(2&Ps)&&0!==Ts?Ts&-Ts:null!==ga.transition?(0===Zs&&(Zs=ht()),Zs):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Qt(e.type)}function nc(e,t,n,r){if(50<Qs)throw Qs=0,Ys=null,Error(a(185));vt(e,n,r),0!==(2&Ps)&&e===Ms||(e===Ms&&(0===(2&Ps)&&(Ls|=n),4===Ns&&lc(e,Ts)),rc(e,r),1===n&&0===Ps&&0===(1&t.mode)&&(Bs=Ye()+500,Do&&Vo()))}function rc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,a=e.pendingLanes;0<a;){var i=31-it(a),l=1<<i,s=o[i];-1===s?0!==(l&n)&&0===(l&r)||(o[i]=ft(l,t)):s<=t&&(e.expiredLanes|=l),a&=~l}}(e,t);var r=pt(e,e===Ms?Ts:0);if(0===r)null!==n&&qe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&qe(n),1===t)0===e.tag?function(e){Do=!0,Bo(e)}(sc.bind(null,e)):Bo(sc.bind(null,e)),io((function(){0===(6&Ps)&&Vo()})),n=null;else{switch(xt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Mc(n,oc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function oc(e,t){if(Js=-1,Zs=0,0!==(6&Ps))throw Error(a(327));var n=e.callbackNode;if(Ac()&&e.callbackNode!==n)return null;var r=pt(e,e===Ms?Ts:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gc(e,r);else{t=r;var o=Ps;Ps|=2;var i=mc();for(Ms===e&&Ts===t||(Vs=null,Bs=Ye()+500,pc(e,t));;)try{yc();break}catch(s){fc(e,s)}Ra(),Cs.current=i,Ps=o,null!==Os?t=0:(Ms=null,Ts=0,t=Ns)}if(0!==t){if(2===t&&(0!==(o=mt(e))&&(r=o,t=ac(e,o))),1===t)throw n=$s,pc(e,0),lc(e,r),rc(e,Ye()),n;if(6===t)lc(e,r);else{if(o=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var o=n[r],a=o.getSnapshot;o=o.value;try{if(!lr(a(),o))return!1}catch(l){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(o)&&(2===(t=gc(e,r))&&(0!==(i=mt(e))&&(r=i,t=ac(e,i))),1===t))throw n=$s,pc(e,0),lc(e,r),rc(e,Ye()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(a(345));case 2:case 5:wc(e,Ds,Vs);break;case 3:if(lc(e,r),(130023424&r)===r&&10<(t=Ws+500-Ye())){if(0!==pt(e,0))break;if(((o=e.suspendedLanes)&r)!==r){ec(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=ro(wc.bind(null,e,Ds,Vs),t);break}wc(e,Ds,Vs);break;case 4:if(lc(e,r),(4194240&r)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-it(r);i=1<<l,(l=t[l])>o&&(o=l),r&=~i}if(r=o,10<(r=(120>(r=Ye()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Ss(r/1960))-r)){e.timeoutHandle=ro(wc.bind(null,e,Ds,Vs),r);break}wc(e,Ds,Vs);break;default:throw Error(a(329))}}}return rc(e,Ye()),e.callbackNode===n?oc.bind(null,e):null}function ac(e,t){var n=Fs;return e.current.memoizedState.isDehydrated&&(pc(e,t).flags|=256),2!==(e=gc(e,t))&&(t=Ds,Ds=n,null!==t&&ic(t)),e}function ic(e){null===Ds?Ds=e:Ds.push.apply(Ds,e)}function lc(e,t){for(t&=~_s,t&=~Ls,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-it(t),r=1<<n;e[n]=-1,t&=~r}}function sc(e){if(0!==(6&Ps))throw Error(a(327));Ac();var t=pt(e,0);if(0===(1&t))return rc(e,Ye()),null;var n=gc(e,t);if(0!==e.tag&&2===n){var r=mt(e);0!==r&&(t=r,n=ac(e,r))}if(1===n)throw n=$s,pc(e,0),lc(e,t),rc(e,Ye()),n;if(6===n)throw Error(a(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wc(e,Ds,Vs),rc(e,Ye()),null}function cc(e,t){var n=Ps;Ps|=1;try{return e(t)}finally{0===(Ps=n)&&(Bs=Ye()+500,Do&&Vo())}}function uc(e){null!==qs&&0===qs.tag&&0===(6&Ps)&&Ac();var t=Ps;Ps|=1;var n=Rs.transition,r=bt;try{if(Rs.transition=null,bt=1,e)return e()}finally{bt=r,Rs.transition=n,0===(6&(Ps=t))&&Vo()}}function dc(){js=Is.current,Eo(Is)}function pc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,oo(n)),null!==Os)for(n=Os.return;null!==n;){var r=n;switch(na(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&No();break;case 3:Ya(),Eo(Oo),Eo(Mo),ri();break;case 5:Za(r);break;case 4:Ya();break;case 13:case 19:Eo(ei);break;case 10:Pa(r.type._context);break;case 22:case 23:dc()}n=n.return}if(Ms=e,Os=e=Ic(e.current,null),Ts=js=t,Ns=0,$s=null,_s=Ls=zs=0,Ds=Fs=null,null!==ja){for(t=0;t<ja.length;t++)if(null!==(r=(n=ja[t]).interleaved)){n.interleaved=null;var o=r.next,a=n.pending;if(null!==a){var i=a.next;a.next=o,r.next=i}n.pending=r}ja=null}return e}function fc(e,t){for(;;){var n=Os;try{if(Ra(),oi.current=Ji,ui){for(var r=li.memoizedState;null!==r;){var o=r.queue;null!==o&&(o.pending=null),r=r.next}ui=!1}if(ii=0,ci=si=li=null,di=!1,pi=0,Es.current=null,null===n||null===n.return){Ns=1,$s=t,Os=null;break}e:{var i=e,l=n.return,s=n,c=t;if(t=Ts,s.flags|=32768,null!==c&&"object"===typeof c&&"function"===typeof c.then){var u=c,d=s,p=d.tag;if(0===(1&d.mode)&&(0===p||11===p||15===p)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var m=gl(l);if(null!==m){m.flags&=-257,vl(m,l,s,0,t),1&m.mode&&hl(i,u,t),c=u;var h=(t=m).updateQueue;if(null===h){var g=new Set;g.add(c),t.updateQueue=g}else h.add(c);break e}if(0===(1&t)){hl(i,u,t),hc();break e}c=Error(a(426))}else if(aa&&1&s.mode){var v=gl(l);if(null!==v){0===(65536&v.flags)&&(v.flags|=256),vl(v,l,s,0,t),ha(cl(c,s));break e}}i=c=cl(c,s),4!==Ns&&(Ns=2),null===Fs?Fs=[i]:Fs.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t,Ba(i,fl(0,c,t));break e;case 1:s=c;var y=i.type,b=i.stateNode;if(0===(128&i.flags)&&("function"===typeof y.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===Xs||!Xs.has(b)))){i.flags|=65536,t&=-t,i.lanes|=t,Ba(i,ml(i,s,t));break e}}i=i.return}while(null!==i)}xc(n)}catch(x){t=x,Os===n&&null!==n&&(Os=n=n.return);continue}break}}function mc(){var e=Cs.current;return Cs.current=Ji,null===e?Ji:e}function hc(){0!==Ns&&3!==Ns&&2!==Ns||(Ns=4),null===Ms||0===(268435455&zs)&&0===(268435455&Ls)||lc(Ms,Ts)}function gc(e,t){var n=Ps;Ps|=2;var r=mc();for(Ms===e&&Ts===t||(Vs=null,pc(e,t));;)try{vc();break}catch(o){fc(e,o)}if(Ra(),Ps=n,Cs.current=r,null!==Os)throw Error(a(261));return Ms=null,Ts=0,Ns}function vc(){for(;null!==Os;)bc(Os)}function yc(){for(;null!==Os&&!Ge();)bc(Os)}function bc(e){var t=ks(e.alternate,e,js);e.memoizedProps=e.pendingProps,null===t?xc(e):Os=t,Es.current=null}function xc(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Kl(n,t,js)))return void(Os=n)}else{if(null!==(n=ql(n,t)))return n.flags&=32767,void(Os=n);if(null===e)return Ns=6,void(Os=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Os=t);Os=t=e}while(null!==t);0===Ns&&(Ns=5)}function wc(e,t,n){var r=bt,o=Rs.transition;try{Rs.transition=null,bt=1,function(e,t,n,r){do{Ac()}while(null!==qs);if(0!==(6&Ps))throw Error(a(327));n=e.finishedWork;var o=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(a(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-it(n),a=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~a}}(e,i),e===Ms&&(Os=Ms=null,Ts=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Ks||(Ks=!0,Mc(tt,(function(){return Ac(),null}))),i=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||i){i=Rs.transition,Rs.transition=null;var l=bt;bt=1;var s=Ps;Ps|=4,Es.current=null,function(e,t){if(eo=Ht,fr(e=pr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(w){n=null;break e}var l=0,s=-1,c=-1,u=0,d=0,p=e,f=null;t:for(;;){for(var m;p!==n||0!==o&&3!==p.nodeType||(s=l+o),p!==i||0!==r&&3!==p.nodeType||(c=l+r),3===p.nodeType&&(l+=p.nodeValue.length),null!==(m=p.firstChild);)f=p,p=m;for(;;){if(p===e)break t;if(f===n&&++u===o&&(s=l),f===i&&++d===r&&(c=l),null!==(m=p.nextSibling))break;f=(p=f).parentNode}p=m}n=-1===s||-1===c?null:{start:s,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(to={focusedElem:e,selectionRange:n},Ht=!1,Jl=t;null!==Jl;)if(e=(t=Jl).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Jl=e;else for(;null!==Jl;){t=Jl;try{var h=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==h){var g=h.memoizedProps,v=h.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?g:nl(t.type,g),v);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(a(163))}}catch(w){Sc(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Jl=e;break}Jl=t.return}h=ts,ts=!1}(e,n),gs(n,e),mr(to),Ht=!!eo,to=eo=null,e.current=n,ys(n,e,o),Qe(),Ps=s,bt=l,Rs.transition=i}else e.current=n;if(Ks&&(Ks=!1,qs=e,Gs=o),i=e.pendingLanes,0===i&&(Xs=null),function(e){if(at&&"function"===typeof at.onCommitFiberRoot)try{at.onCommitFiberRoot(ot,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),rc(e,Ye()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Hs)throw Hs=!1,e=Us,Us=null,e;0!==(1&Gs)&&0!==e.tag&&Ac(),i=e.pendingLanes,0!==(1&i)?e===Ys?Qs++:(Qs=0,Ys=e):Qs=0,Vo()}(e,t,n,r)}finally{Rs.transition=o,bt=r}return null}function Ac(){if(null!==qs){var e=xt(Gs),t=Rs.transition,n=bt;try{if(Rs.transition=null,bt=16>e?16:e,null===qs)var r=!1;else{if(e=qs,qs=null,Gs=0,0!==(6&Ps))throw Error(a(331));var o=Ps;for(Ps|=4,Jl=e.current;null!==Jl;){var i=Jl,l=i.child;if(0!==(16&Jl.flags)){var s=i.deletions;if(null!==s){for(var c=0;c<s.length;c++){var u=s[c];for(Jl=u;null!==Jl;){var d=Jl;switch(d.tag){case 0:case 11:case 15:ns(8,d,i)}var p=d.child;if(null!==p)p.return=d,Jl=p;else for(;null!==Jl;){var f=(d=Jl).sibling,m=d.return;if(as(d),d===u){Jl=null;break}if(null!==f){f.return=m,Jl=f;break}Jl=m}}}var h=i.alternate;if(null!==h){var g=h.child;if(null!==g){h.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}Jl=i}}if(0!==(2064&i.subtreeFlags)&&null!==l)l.return=i,Jl=l;else e:for(;null!==Jl;){if(0!==(2048&(i=Jl).flags))switch(i.tag){case 0:case 11:case 15:ns(9,i,i.return)}var y=i.sibling;if(null!==y){y.return=i.return,Jl=y;break e}Jl=i.return}}var b=e.current;for(Jl=b;null!==Jl;){var x=(l=Jl).child;if(0!==(2064&l.subtreeFlags)&&null!==x)x.return=l,Jl=x;else e:for(l=b;null!==Jl;){if(0!==(2048&(s=Jl).flags))try{switch(s.tag){case 0:case 11:case 15:rs(9,s)}}catch(A){Sc(s,s.return,A)}if(s===l){Jl=null;break e}var w=s.sibling;if(null!==w){w.return=s.return,Jl=w;break e}Jl=s.return}}if(Ps=o,Vo(),at&&"function"===typeof at.onPostCommitFiberRoot)try{at.onPostCommitFiberRoot(ot,e)}catch(A){}r=!0}return r}finally{bt=n,Rs.transition=t}}return!1}function kc(e,t,n){e=Da(e,t=fl(0,t=cl(n,t),1),1),t=ec(),null!==e&&(vt(e,1,t),rc(e,t))}function Sc(e,t,n){if(3===e.tag)kc(e,e,n);else for(;null!==t;){if(3===t.tag){kc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Xs||!Xs.has(r))){t=Da(t,e=ml(t,e=cl(n,e),1),1),e=ec(),null!==t&&(vt(t,1,e),rc(t,e));break}}t=t.return}}function Cc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=ec(),e.pingedLanes|=e.suspendedLanes&n,Ms===e&&(Ts&n)===n&&(4===Ns||3===Ns&&(130023424&Ts)===Ts&&500>Ye()-Ws?pc(e,0):_s|=n),rc(e,t)}function Ec(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ut,0===(130023424&(ut<<=1))&&(ut=4194304)));var n=ec();null!==(e=$a(e,t))&&(vt(e,t,n),rc(e,n))}function Rc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Ec(e,n)}function Pc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;null!==o&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(a(314))}null!==r&&r.delete(t),Ec(e,n)}function Mc(e,t){return Ke(e,t)}function Oc(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Tc(e,t,n,r){return new Oc(e,t,n,r)}function jc(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ic(e,t){var n=e.alternate;return null===n?((n=Tc(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Nc(e,t,n,r,o,i){var l=2;if(r=e,"function"===typeof e)jc(e)&&(l=1);else if("string"===typeof e)l=5;else e:switch(e){case k:return $c(n.children,o,i,t);case S:l=8,o|=8;break;case C:return(e=Tc(12,n,t,2|o)).elementType=C,e.lanes=i,e;case M:return(e=Tc(13,n,t,o)).elementType=M,e.lanes=i,e;case O:return(e=Tc(19,n,t,o)).elementType=O,e.lanes=i,e;case I:return zc(n,o,i,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case E:l=10;break e;case R:l=9;break e;case P:l=11;break e;case T:l=14;break e;case j:l=16,r=null;break e}throw Error(a(130,null==e?e:typeof e,""))}return(t=Tc(l,n,t,o)).elementType=e,t.type=r,t.lanes=i,t}function $c(e,t,n,r){return(e=Tc(7,e,r,t)).lanes=n,e}function zc(e,t,n,r){return(e=Tc(22,e,r,t)).elementType=I,e.lanes=n,e.stateNode={isHidden:!1},e}function Lc(e,t,n){return(e=Tc(6,e,null,t)).lanes=n,e}function _c(e,t,n){return(t=Tc(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Fc(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Dc(e,t,n,r,o,a,i,l,s){return e=new Fc(e,t,n,l,s),1===t?(t=1,!0===a&&(t|=8)):t=0,a=Tc(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},La(a),e}function Wc(e){if(!e)return Po;e:{if(Be(e=e._reactInternals)!==e||1!==e.tag)throw Error(a(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Io(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(a(171))}if(1===e.tag){var n=e.type;if(Io(n))return zo(e,n,t)}return t}function Bc(e,t,n,r,o,a,i,l,s){return(e=Dc(n,r,!0,e,0,a,0,l,s)).context=Wc(null),n=e.current,(a=Fa(r=ec(),o=tc(n))).callback=void 0!==t&&null!==t?t:null,Da(n,a,o),e.current.lanes=o,vt(e,o,r),rc(e,r),e}function Vc(e,t,n,r){var o=t.current,a=ec(),i=tc(o);return n=Wc(n),null===t.context?t.context=n:t.pendingContext=n,(t=Fa(a,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Da(o,t,i))&&(nc(e,o,i,a),Wa(e,o,i)),i}function Hc(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Uc(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Xc(e,t){Uc(e,t),(e=e.alternate)&&Uc(e,t)}ks=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Oo.current)bl=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bl=!1,function(e,t,n){switch(t.tag){case 3:Ml(t),ma();break;case 5:Ja(t);break;case 1:Io(t.type)&&Lo(t);break;case 4:Qa(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;Ro(ka,r._currentValue),r._currentValue=o;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ro(ei,1&ei.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Ll(e,t,n):(Ro(ei,1&ei.current),null!==(e=Hl(e,t,n))?e.sibling:null);Ro(ei,1&ei.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Bl(e,t,n);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),Ro(ei,ei.current),r)break;return null;case 22:case 23:return t.lanes=0,Sl(e,t,n)}return Hl(e,t,n)}(e,t,n);bl=0!==(131072&e.flags)}else bl=!1,aa&&0!==(1048576&t.flags)&&ea(t,Ko,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Vl(e,t),e=t.pendingProps;var o=jo(t,Mo.current);Oa(t,n),o=gi(null,t,r,e,o,n);var i=vi();return t.flags|=1,"object"===typeof o&&null!==o&&"function"===typeof o.render&&void 0===o.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Io(r)?(i=!0,Lo(t)):i=!1,t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,La(t),o.updater=ol,t.stateNode=o,o._reactInternals=t,sl(t,r,e,n),t=Pl(null,t,r,!0,i,n)):(t.tag=0,aa&&i&&ta(t),xl(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Vl(e,t),e=t.pendingProps,r=(o=r._init)(r._payload),t.type=r,o=t.tag=function(e){if("function"===typeof e)return jc(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===P)return 11;if(e===T)return 14}return 2}(r),e=nl(r,e),o){case 0:t=El(null,t,r,e,n);break e;case 1:t=Rl(null,t,r,e,n);break e;case 11:t=wl(null,t,r,e,n);break e;case 14:t=Al(null,t,r,nl(r.type,e),n);break e}throw Error(a(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,El(e,t,r,o=t.elementType===r?o:nl(r,o),n);case 1:return r=t.type,o=t.pendingProps,Rl(e,t,r,o=t.elementType===r?o:nl(r,o),n);case 3:e:{if(Ml(t),null===e)throw Error(a(387));r=t.pendingProps,o=(i=t.memoizedState).element,_a(e,t),Va(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=Ol(e,t,r,n,o=cl(Error(a(423)),t));break e}if(r!==o){t=Ol(e,t,r,n,o=cl(Error(a(424)),t));break e}for(oa=co(t.stateNode.containerInfo.firstChild),ra=t,aa=!0,ia=null,n=Aa(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ma(),r===o){t=Hl(e,t,n);break e}xl(e,t,r,n)}t=t.child}return t;case 5:return Ja(t),null===e&&ua(t),r=t.type,o=t.pendingProps,i=null!==e?e.memoizedProps:null,l=o.children,no(r,o)?l=null:null!==i&&no(r,i)&&(t.flags|=32),Cl(e,t),xl(e,t,l,n),t.child;case 6:return null===e&&ua(t),null;case 13:return Ll(e,t,n);case 4:return Qa(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=wa(t,null,r,n):xl(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,wl(e,t,r,o=t.elementType===r?o:nl(r,o),n);case 7:return xl(e,t,t.pendingProps,n),t.child;case 8:case 12:return xl(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,l=o.value,Ro(ka,r._currentValue),r._currentValue=l,null!==i)if(lr(i.value,l)){if(i.children===o.children&&!Oo.current){t=Hl(e,t,n);break e}}else for(null!==(i=t.child)&&(i.return=t);null!==i;){var s=i.dependencies;if(null!==s){l=i.child;for(var c=s.firstContext;null!==c;){if(c.context===r){if(1===i.tag){(c=Fa(-1,n&-n)).tag=2;var u=i.updateQueue;if(null!==u){var d=(u=u.shared).pending;null===d?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}i.lanes|=n,null!==(c=i.alternate)&&(c.lanes|=n),Ma(i.return,n,t),s.lanes|=n;break}c=c.next}}else if(10===i.tag)l=i.type===t.type?null:i.child;else if(18===i.tag){if(null===(l=i.return))throw Error(a(341));l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),Ma(l,n,t),l=i.sibling}else l=i.child;if(null!==l)l.return=i;else for(l=i;null!==l;){if(l===t){l=null;break}if(null!==(i=l.sibling)){i.return=l.return,l=i;break}l=l.return}i=l}xl(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Oa(t,n),r=r(o=Ta(o)),t.flags|=1,xl(e,t,r,n),t.child;case 14:return o=nl(r=t.type,t.pendingProps),Al(e,t,r,o=nl(r.type,o),n);case 15:return kl(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:nl(r,o),Vl(e,t),t.tag=1,Io(r)?(e=!0,Lo(t)):e=!1,Oa(t,n),il(t,r,o),sl(t,r,o,n),Pl(null,t,r,!0,e,n);case 19:return Bl(e,t,n);case 22:return Sl(e,t,n)}throw Error(a(156,t.tag))};var Kc="function"===typeof reportError?reportError:function(e){console.error(e)};function qc(e){this._internalRoot=e}function Gc(e){this._internalRoot=e}function Qc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Yc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Jc(){}function Zc(e,t,n,r,o){var a=n._reactRootContainer;if(a){var i=a;if("function"===typeof o){var l=o;o=function(){var e=Hc(i);l.call(e)}}Vc(t,i,e,o)}else i=function(e,t,n,r,o){if(o){if("function"===typeof r){var a=r;r=function(){var e=Hc(i);a.call(e)}}var i=Bc(t,r,e,0,null,!1,0,"",Jc);return e._reactRootContainer=i,e[ho]=i.current,Br(8===e.nodeType?e.parentNode:e),uc(),i}for(;o=e.lastChild;)e.removeChild(o);if("function"===typeof r){var l=r;r=function(){var e=Hc(s);l.call(e)}}var s=Dc(e,0,!1,null,0,!1,0,"",Jc);return e._reactRootContainer=s,e[ho]=s.current,Br(8===e.nodeType?e.parentNode:e),uc((function(){Vc(t,s,n,r)})),s}(n,t,e,o,r);return Hc(i)}Gc.prototype.render=qc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(a(409));Vc(e,t,null,null)},Gc.prototype.unmount=qc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;uc((function(){Vc(null,e,null,null)})),t[ho]=null}},Gc.prototype.unstable_scheduleHydration=function(e){if(e){var t=St();e={blockedOn:null,target:e,priority:t};for(var n=0;n<It.length&&0!==t&&t<It[n].priority;n++);It.splice(n,0,e),0===n&&Lt(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),rc(t,Ye()),0===(6&Ps)&&(Bs=Ye()+500,Vo()))}break;case 13:uc((function(){var t=$a(e,1);if(null!==t){var n=ec();nc(t,e,1,n)}})),Xc(e,1)}},At=function(e){if(13===e.tag){var t=$a(e,134217728);if(null!==t)nc(t,e,134217728,ec());Xc(e,134217728)}},kt=function(e){if(13===e.tag){var t=tc(e),n=$a(e,t);if(null!==n)nc(n,e,t,ec());Xc(e,t)}},St=function(){return bt},Ct=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},Ae=function(e,t,n){switch(t){case"input":if(J(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Ao(r);if(!o)throw Error(a(90));K(r),J(r,o)}}}break;case"textarea":ae(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Pe=cc,Me=uc;var eu={usingClientEntryPoint:!1,Events:[xo,wo,Ao,Ee,Re,cc]},tu={findFiberByHostInstance:bo,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nu={bundleType:tu.bundleType,version:tu.version,rendererPackageName:tu.rendererPackageName,rendererConfig:tu.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Ue(e))?null:e.stateNode},findFiberByHostInstance:tu.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ru=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ru.isDisabled&&ru.supportsFiber)try{ot=ru.inject(nu),at=ru}catch(ue){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=eu,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Qc(t))throw Error(a(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:A,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Qc(e))throw Error(a(299));var n=!1,r="",o=Kc;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(o=t.onRecoverableError)),t=Dc(e,1,!1,null,0,n,0,r,o),e[ho]=t.current,Br(8===e.nodeType?e.parentNode:e),new qc(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(a(188));throw e=Object.keys(e).join(","),Error(a(268,e))}return e=null===(e=Ue(t))?null:e.stateNode},t.flushSync=function(e){return uc(e)},t.hydrate=function(e,t,n){if(!Yc(t))throw Error(a(200));return Zc(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Qc(e))throw Error(a(405));var r=null!=n&&n.hydratedSources||null,o=!1,i="",l=Kc;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(o=!0),void 0!==n.identifierPrefix&&(i=n.identifierPrefix),void 0!==n.onRecoverableError&&(l=n.onRecoverableError)),t=Bc(t,null,e,1,null!=n?n:null,o,0,i,l),e[ho]=t.current,Br(e),r)for(e=0;e<r.length;e++)o=(o=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Gc(t)},t.render=function(e,t,n){if(!Yc(t))throw Error(a(200));return Zc(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Yc(e))throw Error(a(40));return!!e._reactRootContainer&&(uc((function(){Zc(null,null,e,!1,(function(){e._reactRootContainer=null,e[ho]=null}))})),!0)},t.unstable_batchedUpdates=cc,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Yc(n))throw Error(a(200));if(null==e||void 0===e._reactInternals)throw Error(a(38));return Zc(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},4391:(e,t,n)=>{"use strict";var r=n(7950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},7950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(2730)},5082:(e,t)=>{"use strict";var n,r=Symbol.for("react.element"),o=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),c=Symbol.for("react.context"),u=Symbol.for("react.server_context"),d=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),m=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),g=Symbol.for("react.offscreen");function v(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case a:case l:case i:case p:case f:return e;default:switch(e=e&&e.$$typeof){case u:case c:case d:case h:case m:case s:return e;default:return t}}case o:return t}}}n=Symbol.for("react.module.reference"),t.ForwardRef=d,t.Memo=m},2086:(e,t,n)=>{"use strict";e.exports=n(5082)},1153:(e,t,n)=>{"use strict";var r=n(5043),o=Symbol.for("react.element"),a=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,a={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)i.call(t,r)&&!s.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===a[r]&&(a[r]=t[r]);return{$$typeof:o,type:e,key:c,ref:u,props:a,_owner:l.current}}t.Fragment=a,t.jsx=c,t.jsxs=c},4202:(e,t)=>{"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),f=Symbol.iterator;var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var x=b.prototype=new y;x.constructor=b,h(x,v.prototype),x.isPureReactComponent=!0;var w=Array.isArray,A=Object.prototype.hasOwnProperty,k={current:null},S={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,r){var o,a={},i=null,l=null;if(null!=t)for(o in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(i=""+t.key),t)A.call(t,o)&&!S.hasOwnProperty(o)&&(a[o]=t[o]);var s=arguments.length-2;if(1===s)a.children=r;else if(1<s){for(var c=Array(s),u=0;u<s;u++)c[u]=arguments[u+2];a.children=c}if(e&&e.defaultProps)for(o in s=e.defaultProps)void 0===a[o]&&(a[o]=s[o]);return{$$typeof:n,type:e,key:i,ref:l,props:a,_owner:k.current}}function E(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var R=/\/+/g;function P(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function M(e,t,o,a,i){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var s=!1;if(null===e)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return i=i(s=e),e=""===a?"."+P(s,0):a,w(i)?(o="",null!=e&&(o=e.replace(R,"$&/")+"/"),M(i,t,o,"",(function(e){return e}))):null!=i&&(E(i)&&(i=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(i,o+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(R,"$&/")+"/")+e)),t.push(i)),1;if(s=0,a=""===a?".":a+":",w(e))for(var c=0;c<e.length;c++){var u=a+P(l=e[c],c);s+=M(l,t,o,u,i)}else if(u=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=f&&e[f]||e["@@iterator"])?e:null}(e),"function"===typeof u)for(e=u.call(e),c=0;!(l=e.next()).done;)s+=M(l=l.value,t,o,u=a+P(l,c++),i);else if("object"===l)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function O(e,t,n){if(null==e)return e;var r=[],o=0;return M(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function T(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var j={current:null},I={transition:null},N={ReactCurrentDispatcher:j,ReactCurrentBatchConfig:I,ReactCurrentOwner:k};function $(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:O,forEach:function(e,t,n){O(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return O(e,(function(){t++})),t},toArray:function(e){return O(e,(function(e){return e}))||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=o,t.Profiler=i,t.PureComponent=b,t.StrictMode=a,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=N,t.act=$,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=h({},e.props),a=e.key,i=e.ref,l=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,l=k.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(c in t)A.call(t,c)&&!S.hasOwnProperty(c)&&(o[c]=void 0===t[c]&&void 0!==s?s[c]:t[c])}var c=arguments.length-2;if(1===c)o.children=r;else if(1<c){s=Array(c);for(var u=0;u<c;u++)s[u]=arguments[u+2];o.children=s}return{$$typeof:n,type:e.type,key:a,ref:i,props:o,_owner:l}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:l,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:p,_payload:{_status:-1,_result:e},_init:T}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=I.transition;I.transition={};try{e()}finally{I.transition=t}},t.unstable_act=$,t.useCallback=function(e,t){return j.current.useCallback(e,t)},t.useContext=function(e){return j.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return j.current.useDeferredValue(e)},t.useEffect=function(e,t){return j.current.useEffect(e,t)},t.useId=function(){return j.current.useId()},t.useImperativeHandle=function(e,t,n){return j.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return j.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return j.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return j.current.useMemo(e,t)},t.useReducer=function(e,t,n){return j.current.useReducer(e,t,n)},t.useRef=function(e){return j.current.useRef(e)},t.useState=function(e){return j.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return j.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return j.current.useTransition()},t.version="18.3.1"},5043:(e,t,n)=>{"use strict";e.exports=n(4202)},579:(e,t,n)=>{"use strict";e.exports=n(1153)},7234:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,o=e[r];if(!(0<a(o,t)))break e;e[r]=t,e[n]=o,n=r}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length,i=o>>>1;r<i;){var l=2*(r+1)-1,s=e[l],c=l+1,u=e[c];if(0>a(s,n))c<o&&0>a(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[l]=n,r=l);else{if(!(c<o&&0>a(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();t.unstable_now=function(){return l.now()-s}}var c=[],u=[],d=1,p=null,f=3,m=!1,h=!1,g=!1,v="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(u);null!==t;){if(null===t.callback)o(u);else{if(!(t.startTime<=e))break;o(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function w(e){if(g=!1,x(e),!h)if(null!==r(c))h=!0,I(A);else{var t=r(u);null!==t&&N(w,t.startTime-e)}}function A(e,n){h=!1,g&&(g=!1,y(E),E=-1),m=!0;var a=f;try{for(x(n),p=r(c);null!==p&&(!(p.expirationTime>n)||e&&!M());){var i=p.callback;if("function"===typeof i){p.callback=null,f=p.priorityLevel;var l=i(p.expirationTime<=n);n=t.unstable_now(),"function"===typeof l?p.callback=l:p===r(c)&&o(c),x(n)}else o(c);p=r(c)}if(null!==p)var s=!0;else{var d=r(u);null!==d&&N(w,d.startTime-n),s=!1}return s}finally{p=null,f=a,m=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,S=!1,C=null,E=-1,R=5,P=-1;function M(){return!(t.unstable_now()-P<R)}function O(){if(null!==C){var e=t.unstable_now();P=e;var n=!0;try{n=C(!0,e)}finally{n?k():(S=!1,C=null)}}else S=!1}if("function"===typeof b)k=function(){b(O)};else if("undefined"!==typeof MessageChannel){var T=new MessageChannel,j=T.port2;T.port1.onmessage=O,k=function(){j.postMessage(null)}}else k=function(){v(O,0)};function I(e){C=e,S||(S=!0,k())}function N(e,n){E=v((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){h||m||(h=!0,I(A))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):R=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return f},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},t.unstable_scheduleCallback=function(e,o,a){var i=t.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?i+a:i:a=i,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=1073741823;break;case 4:l=1e4;break;default:l=5e3}return e={id:d++,callback:o,priorityLevel:e,startTime:a,expirationTime:l=a+l,sortIndex:-1},a>i?(e.sortIndex=a,n(u,e),null===r(c)&&e===r(u)&&(g?(y(E),E=-1):g=!0,N(w,a-i))):(e.sortIndex=l,n(c,e),h||m||(h=!0,I(A))),e},t.unstable_shouldYield=M,t.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}},8853:(e,t,n)=>{"use strict";e.exports=n(7234)},4230:(e,t,n)=>{"use strict";e.exports=n.p+"static/media/image.1db4356a342720d2477c.png"},4634:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},4994:e=>{e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},4893:e=>{e.exports=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n},e.exports.__esModule=!0,e.exports.default=e.exports},8168:(e,t,n)=>{"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(null,arguments)}n.d(t,{A:()=>r})},8587:(e,t,n)=>{"use strict";function r(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n}n.d(t,{A:()=>r})},8387:(e,t,n)=>{"use strict";function r(e){var t,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(n=r(e[t]))&&(o&&(o+=" "),o+=n)}else for(n in e)e[n]&&(o&&(o+=" "),o+=n);return o}n.d(t,{A:()=>o});const o=function(){for(var e,t,n=0,o="",a=arguments.length;n<a;n++)(e=arguments[n])&&(t=r(e))&&(o&&(o+=" "),o+=t);return o}}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,o){if(1&o&&(r=this(r)),8&o)return r;if("object"===typeof r&&r){if(4&o&&r.__esModule)return r;if(16&o&&"function"===typeof r.then)return r}var a=Object.create(null);n.r(a);var i={};e=e||[null,t({}),t([]),t(t)];for(var l=2&o&&r;"object"==typeof l&&!~e.indexOf(l);l=t(l))Object.getOwnPropertyNames(l).forEach((e=>i[e]=()=>r[e]));return i.default=()=>r,n.d(a,i),a}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{"use strict";var e=n(5043),t=n(4391),r=n(8279),o=n(8168),a=n(8587);const i=e.createContext(null);function l(){return e.useContext(i)}const s="function"===typeof Symbol&&Symbol.for?Symbol.for("mui.nested"):"__THEME_NESTED__";var c=n(579);const u=function(t){const{children:n,theme:r}=t,a=l(),u=e.useMemo((()=>{const e=null===a?r:function(e,t){if("function"===typeof t)return t(e);return(0,o.A)({},e,t)}(a,r);return null!=e&&(e[s]=null!==a),e}),[r,a]);return(0,c.jsx)(i.Provider,{value:u,children:n})};var d=n(4575);const p=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;const n=e.useContext(d.T);return n&&(r=n,0!==Object.keys(r).length)?n:t;var r},f=["value"],m=e.createContext();const h=()=>{const t=e.useContext(m);return null!=t&&t},g=function(e){let{value:t}=e,n=(0,a.A)(e,f);return(0,c.jsx)(m.Provider,(0,o.A)({value:null==t||t},n))};var v=n(3654);const y={};function b(t,n,r){let a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return e.useMemo((()=>{const e=t&&n[t]||n;if("function"===typeof r){const i=r(e),l=t?(0,o.A)({},n,{[t]:i}):i;return a?()=>l:l}return t?(0,o.A)({},n,{[t]:r}):(0,o.A)({},n,r)}),[t,n,r,a])}const x=function(e){const{children:t,theme:n,themeId:r}=e,o=p(y),a=l()||y,i=b(r,o,n),s=b(r,a,n,!0),f="rtl"===i.direction;return(0,c.jsx)(u,{theme:s,children:(0,c.jsx)(d.T.Provider,{value:i,children:(0,c.jsx)(g,{value:f,children:(0,c.jsx)(v.A,{value:null==i?void 0:i.components,children:t})})})})};var w=n(3375);const A=["theme"];function k(e){let{theme:t}=e,n=(0,a.A)(e,A);const r=t[w.A];return(0,c.jsx)(x,(0,o.A)({},n,{themeId:r?w.A:void 0,theme:r||t}))}var S=n(8206),C=n(869),E=n(8280);const R=(0,E.A)();const P=function(){return p(arguments.length>0&&void 0!==arguments[0]?arguments[0]:R)};const M=function(e){let{styles:t,themeId:n,defaultTheme:r={}}=e;const o=P(r),a="function"===typeof t?t(n&&o[n]||o):t;return(0,c.jsx)(C.A,{styles:a})};var O=n(5170);const T=function(e){return(0,c.jsx)(M,(0,o.A)({},e,{defaultTheme:O.A,themeId:w.A}))},j=(e,t)=>(0,o.A)({WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",boxSizing:"border-box",WebkitTextSizeAdjust:"100%"},t&&!e.vars&&{colorScheme:e.palette.mode}),I=e=>(0,o.A)({color:(e.vars||e).palette.text.primary},e.typography.body1,{backgroundColor:(e.vars||e).palette.background.default,"@media print":{backgroundColor:(e.vars||e).palette.common.white}});const N=function(t){const n=(0,S.b)({props:t,name:"MuiCssBaseline"}),{children:r,enableColorScheme:a=!1}=n;return(0,c.jsxs)(e.Fragment,{children:[(0,c.jsx)(T,{styles:e=>function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];var n;const r={};t&&e.colorSchemes&&Object.entries(e.colorSchemes).forEach((t=>{let[n,o]=t;var a;r[e.getColorSchemeSelector(n).replace(/\s*&/,"")]={colorScheme:null==(a=o.palette)?void 0:a.mode}}));let a=(0,o.A)({html:j(e,t),"*, *::before, *::after":{boxSizing:"inherit"},"strong, b":{fontWeight:e.typography.fontWeightBold},body:(0,o.A)({margin:0},I(e),{"&::backdrop":{backgroundColor:(e.vars||e).palette.background.default}})},r);const i=null==(n=e.components)||null==(n=n.MuiCssBaseline)?void 0:n.styleOverrides;return i&&(a=[a,i]),a}(e,a)}),r]})};var $=n(8387),z=n(3174),L=n(8812),_=n(8698);const F=["className","component"];var D=n(9386),W=n(2532);const B=(0,W.A)("MuiBox",["root"]),V=(0,r.A)(),H=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{themeId:n,defaultTheme:r,defaultClassName:i="MuiBox-root",generateClassName:l}=t,s=(0,z.default)("div",{shouldForwardProp:e=>"theme"!==e&&"sx"!==e&&"as"!==e})(L.A);return e.forwardRef((function(e,t){const u=P(r),d=(0,_.A)(e),{className:p,component:f="div"}=d,m=(0,a.A)(d,F);return(0,c.jsx)(s,(0,o.A)({as:f,ref:t,className:(0,$.A)(p,l?l(i):i),theme:n&&u[n]||u},m))}))}({themeId:w.A,defaultTheme:V,defaultClassName:B.root,generateClassName:D.A.generate});var U=n(8610),X=n(7266),K=n(4535),q=n(5849),G=n(3319),Q=n(3574);function Y(e,t){return Y=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Y(e,t)}function J(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,Y(e,t)}const Z=e.createContext(null);function ee(t,n){var r=Object.create(null);return t&&e.Children.map(t,(function(e){return e})).forEach((function(t){r[t.key]=function(t){return n&&(0,e.isValidElement)(t)?n(t):t}(t)})),r}function te(e,t,n){return null!=n[t]?n[t]:e.props[t]}function ne(t,n,r){var o=ee(t.children),a=function(e,t){function n(n){return n in t?t[n]:e[n]}e=e||{},t=t||{};var r,o=Object.create(null),a=[];for(var i in e)i in t?a.length&&(o[i]=a,a=[]):a.push(i);var l={};for(var s in t){if(o[s])for(r=0;r<o[s].length;r++){var c=o[s][r];l[o[s][r]]=n(c)}l[s]=n(s)}for(r=0;r<a.length;r++)l[a[r]]=n(a[r]);return l}(n,o);return Object.keys(a).forEach((function(i){var l=a[i];if((0,e.isValidElement)(l)){var s=i in n,c=i in o,u=n[i],d=(0,e.isValidElement)(u)&&!u.props.in;!c||s&&!d?c||!s||d?c&&s&&(0,e.isValidElement)(u)&&(a[i]=(0,e.cloneElement)(l,{onExited:r.bind(null,l),in:u.props.in,exit:te(l,"exit",t),enter:te(l,"enter",t)})):a[i]=(0,e.cloneElement)(l,{in:!1}):a[i]=(0,e.cloneElement)(l,{onExited:r.bind(null,l),in:!0,exit:te(l,"exit",t),enter:te(l,"enter",t)})}})),a}var re=Object.values||function(e){return Object.keys(e).map((function(t){return e[t]}))},oe=function(t){function n(e,n){var r,o=(r=t.call(this,e,n)||this).handleExited.bind(function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(r));return r.state={contextValue:{isMounting:!0},handleExited:o,firstRender:!0},r}J(n,t);var r=n.prototype;return r.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},r.componentWillUnmount=function(){this.mounted=!1},n.getDerivedStateFromProps=function(t,n){var r,o,a=n.children,i=n.handleExited;return{children:n.firstRender?(r=t,o=i,ee(r.children,(function(t){return(0,e.cloneElement)(t,{onExited:o.bind(null,t),in:!0,appear:te(t,"appear",r),enter:te(t,"enter",r),exit:te(t,"exit",r)})}))):ne(t,a,i),firstRender:!1}},r.handleExited=function(e,t){var n=ee(this.props.children);e.key in n||(e.props.onExited&&e.props.onExited(t),this.mounted&&this.setState((function(t){var n=(0,o.A)({},t.children);return delete n[e.key],{children:n}})))},r.render=function(){var t=this.props,n=t.component,r=t.childFactory,o=(0,a.A)(t,["component","childFactory"]),i=this.state.contextValue,l=re(this.state.children).map(r);return delete o.appear,delete o.enter,delete o.exit,null===n?e.createElement(Z.Provider,{value:i},l):e.createElement(Z.Provider,{value:i},e.createElement(n,o,l))},n}(e.Component);oe.propTypes={},oe.defaultProps={component:"div",childFactory:function(e){return e}};const ae=oe;var ie=n(3290),le=n(9303);const se=function(t){const{className:n,classes:r,pulsate:o=!1,rippleX:a,rippleY:i,rippleSize:l,in:s,onExited:u,timeout:d}=t,[p,f]=e.useState(!1),m=(0,$.A)(n,r.ripple,r.rippleVisible,o&&r.ripplePulsate),h={width:l,height:l,top:-l/2+i,left:-l/2+a},g=(0,$.A)(r.child,p&&r.childLeaving,o&&r.childPulsate);return s||p||f(!0),e.useEffect((()=>{if(!s&&null!=u){const e=setTimeout(u,d);return()=>{clearTimeout(e)}}}),[u,s,d]),(0,c.jsx)("span",{className:m,style:h,children:(0,c.jsx)("span",{className:g})})};const ce=(0,W.A)("MuiTouchRipple",["root","ripple","rippleVisible","ripplePulsate","child","childLeaving","childPulsate"]),ue=["center","classes","className"];let de,pe,fe,me,he=e=>e;const ge=(0,ie.i7)(de||(de=he`
  0% {
    transform: scale(0);
    opacity: 0.1;
  }

  100% {
    transform: scale(1);
    opacity: 0.3;
  }
`)),ve=(0,ie.i7)(pe||(pe=he`
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
`)),ye=(0,ie.i7)(fe||(fe=he`
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.92);
  }

  100% {
    transform: scale(1);
  }
`)),be=(0,K.Ay)("span",{name:"MuiTouchRipple",slot:"Root"})({overflow:"hidden",pointerEvents:"none",position:"absolute",zIndex:0,top:0,right:0,bottom:0,left:0,borderRadius:"inherit"}),xe=(0,K.Ay)(se,{name:"MuiTouchRipple",slot:"Ripple"})(me||(me=he`
  opacity: 0;
  position: absolute;

  &.${0} {
    opacity: 0.3;
    transform: scale(1);
    animation-name: ${0};
    animation-duration: ${0}ms;
    animation-timing-function: ${0};
  }

  &.${0} {
    animation-duration: ${0}ms;
  }

  & .${0} {
    opacity: 1;
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: currentColor;
  }

  & .${0} {
    opacity: 0;
    animation-name: ${0};
    animation-duration: ${0}ms;
    animation-timing-function: ${0};
  }

  & .${0} {
    position: absolute;
    /* @noflip */
    left: 0px;
    top: 0;
    animation-name: ${0};
    animation-duration: 2500ms;
    animation-timing-function: ${0};
    animation-iteration-count: infinite;
    animation-delay: 200ms;
  }
`),ce.rippleVisible,ge,550,(e=>{let{theme:t}=e;return t.transitions.easing.easeInOut}),ce.ripplePulsate,(e=>{let{theme:t}=e;return t.transitions.duration.shorter}),ce.child,ce.childLeaving,ve,550,(e=>{let{theme:t}=e;return t.transitions.easing.easeInOut}),ce.childPulsate,ye,(e=>{let{theme:t}=e;return t.transitions.easing.easeInOut})),we=e.forwardRef((function(t,n){const r=(0,S.b)({props:t,name:"MuiTouchRipple"}),{center:i=!1,classes:l={},className:s}=r,u=(0,a.A)(r,ue),[d,p]=e.useState([]),f=e.useRef(0),m=e.useRef(null);e.useEffect((()=>{m.current&&(m.current(),m.current=null)}),[d]);const h=e.useRef(!1),g=(0,le.A)(),v=e.useRef(null),y=e.useRef(null),b=e.useCallback((e=>{const{pulsate:t,rippleX:n,rippleY:r,rippleSize:o,cb:a}=e;p((e=>[...e,(0,c.jsx)(xe,{classes:{ripple:(0,$.A)(l.ripple,ce.ripple),rippleVisible:(0,$.A)(l.rippleVisible,ce.rippleVisible),ripplePulsate:(0,$.A)(l.ripplePulsate,ce.ripplePulsate),child:(0,$.A)(l.child,ce.child),childLeaving:(0,$.A)(l.childLeaving,ce.childLeaving),childPulsate:(0,$.A)(l.childPulsate,ce.childPulsate)},timeout:550,pulsate:t,rippleX:n,rippleY:r,rippleSize:o},f.current)])),f.current+=1,m.current=a}),[l]),x=e.useCallback((function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>{};const{pulsate:r=!1,center:o=i||t.pulsate,fakeElement:a=!1}=t;if("mousedown"===(null==e?void 0:e.type)&&h.current)return void(h.current=!1);"touchstart"===(null==e?void 0:e.type)&&(h.current=!0);const l=a?null:y.current,s=l?l.getBoundingClientRect():{width:0,height:0,left:0,top:0};let c,u,d;if(o||void 0===e||0===e.clientX&&0===e.clientY||!e.clientX&&!e.touches)c=Math.round(s.width/2),u=Math.round(s.height/2);else{const{clientX:t,clientY:n}=e.touches&&e.touches.length>0?e.touches[0]:e;c=Math.round(t-s.left),u=Math.round(n-s.top)}if(o)d=Math.sqrt((2*s.width**2+s.height**2)/3),d%2===0&&(d+=1);else{const e=2*Math.max(Math.abs((l?l.clientWidth:0)-c),c)+2,t=2*Math.max(Math.abs((l?l.clientHeight:0)-u),u)+2;d=Math.sqrt(e**2+t**2)}null!=e&&e.touches?null===v.current&&(v.current=()=>{b({pulsate:r,rippleX:c,rippleY:u,rippleSize:d,cb:n})},g.start(80,(()=>{v.current&&(v.current(),v.current=null)}))):b({pulsate:r,rippleX:c,rippleY:u,rippleSize:d,cb:n})}),[i,b,g]),w=e.useCallback((()=>{x({},{pulsate:!0})}),[x]),A=e.useCallback(((e,t)=>{if(g.clear(),"touchend"===(null==e?void 0:e.type)&&v.current)return v.current(),v.current=null,void g.start(0,(()=>{A(e,t)}));v.current=null,p((e=>e.length>0?e.slice(1):e)),m.current=t}),[g]);return e.useImperativeHandle(n,(()=>({pulsate:w,start:x,stop:A})),[w,x,A]),(0,c.jsx)(be,(0,o.A)({className:(0,$.A)(ce.root,l.root,s),ref:y},u,{children:(0,c.jsx)(ae,{component:null,exit:!0,children:d})}))}));var Ae=n(2372);function ke(e){return(0,Ae.Ay)("MuiButtonBase",e)}const Se=(0,W.A)("MuiButtonBase",["root","disabled","focusVisible"]),Ce=["action","centerRipple","children","className","component","disabled","disableRipple","disableTouchRipple","focusRipple","focusVisibleClassName","LinkComponent","onBlur","onClick","onContextMenu","onDragLeave","onFocus","onFocusVisible","onKeyDown","onKeyUp","onMouseDown","onMouseLeave","onMouseUp","onTouchEnd","onTouchMove","onTouchStart","tabIndex","TouchRippleProps","touchRippleRef","type"],Ee=(0,K.Ay)("button",{name:"MuiButtonBase",slot:"Root",overridesResolver:(e,t)=>t.root})({display:"inline-flex",alignItems:"center",justifyContent:"center",position:"relative",boxSizing:"border-box",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none",textDecoration:"none",color:"inherit","&::-moz-focus-inner":{borderStyle:"none"},[`&.${Se.disabled}`]:{pointerEvents:"none",cursor:"default"},"@media print":{colorAdjust:"exact"}}),Re=e.forwardRef((function(t,n){const r=(0,S.b)({props:t,name:"MuiButtonBase"}),{action:i,centerRipple:l=!1,children:s,className:u,component:d="button",disabled:p=!1,disableRipple:f=!1,disableTouchRipple:m=!1,focusRipple:h=!1,LinkComponent:g="a",onBlur:v,onClick:y,onContextMenu:b,onDragLeave:x,onFocus:w,onFocusVisible:A,onKeyDown:k,onKeyUp:C,onMouseDown:E,onMouseLeave:R,onMouseUp:P,onTouchEnd:M,onTouchMove:O,onTouchStart:T,tabIndex:j=0,TouchRippleProps:I,touchRippleRef:N,type:z}=r,L=(0,a.A)(r,Ce),_=e.useRef(null),F=e.useRef(null),D=(0,q.A)(F,N),{isFocusVisibleRef:W,onFocus:B,onBlur:V,ref:H}=(0,Q.A)(),[X,K]=e.useState(!1);p&&X&&K(!1),e.useImperativeHandle(i,(()=>({focusVisible:()=>{K(!0),_.current.focus()}})),[]);const[Y,J]=e.useState(!1);e.useEffect((()=>{J(!0)}),[]);const Z=Y&&!f&&!p;function ee(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:m;return(0,G.A)((r=>{t&&t(r);return!n&&F.current&&F.current[e](r),!0}))}e.useEffect((()=>{X&&h&&!f&&Y&&F.current.pulsate()}),[f,h,X,Y]);const te=ee("start",E),ne=ee("stop",b),re=ee("stop",x),oe=ee("stop",P),ae=ee("stop",(e=>{X&&e.preventDefault(),R&&R(e)})),ie=ee("start",T),le=ee("stop",M),se=ee("stop",O),ce=ee("stop",(e=>{V(e),!1===W.current&&K(!1),v&&v(e)}),!1),ue=(0,G.A)((e=>{_.current||(_.current=e.currentTarget),B(e),!0===W.current&&(K(!0),A&&A(e)),w&&w(e)})),de=()=>{const e=_.current;return d&&"button"!==d&&!("A"===e.tagName&&e.href)},pe=e.useRef(!1),fe=(0,G.A)((e=>{h&&!pe.current&&X&&F.current&&" "===e.key&&(pe.current=!0,F.current.stop(e,(()=>{F.current.start(e)}))),e.target===e.currentTarget&&de()&&" "===e.key&&e.preventDefault(),k&&k(e),e.target===e.currentTarget&&de()&&"Enter"===e.key&&!p&&(e.preventDefault(),y&&y(e))})),me=(0,G.A)((e=>{h&&" "===e.key&&F.current&&X&&!e.defaultPrevented&&(pe.current=!1,F.current.stop(e,(()=>{F.current.pulsate(e)}))),C&&C(e),y&&e.target===e.currentTarget&&de()&&" "===e.key&&!e.defaultPrevented&&y(e)}));let he=d;"button"===he&&(L.href||L.to)&&(he=g);const ge={};"button"===he?(ge.type=void 0===z?"button":z,ge.disabled=p):(L.href||L.to||(ge.role="button"),p&&(ge["aria-disabled"]=p));const ve=(0,q.A)(n,H,_);const ye=(0,o.A)({},r,{centerRipple:l,component:d,disabled:p,disableRipple:f,disableTouchRipple:m,focusRipple:h,tabIndex:j,focusVisible:X}),be=(e=>{const{disabled:t,focusVisible:n,focusVisibleClassName:r,classes:o}=e,a={root:["root",t&&"disabled",n&&"focusVisible"]},i=(0,U.A)(a,ke,o);return n&&r&&(i.root+=` ${r}`),i})(ye);return(0,c.jsxs)(Ee,(0,o.A)({as:he,className:(0,$.A)(be.root,u),ownerState:ye,onBlur:ce,onClick:y,onContextMenu:ne,onFocus:ue,onKeyDown:fe,onKeyUp:me,onMouseDown:te,onMouseLeave:ae,onMouseUp:oe,onDragLeave:re,onTouchEnd:le,onTouchMove:se,onTouchStart:ie,ref:ve,tabIndex:p?-1:j,type:z},ge,L,{children:[s,Z?(0,c.jsx)(we,(0,o.A)({ref:D,center:l},I)):null]}))}));var Pe=n(6803);function Me(e){return(0,Ae.Ay)("MuiIconButton",e)}const Oe=(0,W.A)("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge"]),Te=["edge","children","className","color","disabled","disableFocusRipple","size"],je=(0,K.Ay)(Re,{name:"MuiIconButton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,"default"!==n.color&&t[`color${(0,Pe.A)(n.color)}`],n.edge&&t[`edge${(0,Pe.A)(n.edge)}`],t[`size${(0,Pe.A)(n.size)}`]]}})((e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({textAlign:"center",flex:"0 0 auto",fontSize:t.typography.pxToRem(24),padding:8,borderRadius:"50%",overflow:"visible",color:(t.vars||t).palette.action.active,transition:t.transitions.create("background-color",{duration:t.transitions.duration.shortest})},!n.disableRipple&&{"&:hover":{backgroundColor:t.vars?`rgba(${t.vars.palette.action.activeChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,X.X4)(t.palette.action.active,t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}}},"start"===n.edge&&{marginLeft:"small"===n.size?-3:-12},"end"===n.edge&&{marginRight:"small"===n.size?-3:-12})}),(e=>{let{theme:t,ownerState:n}=e;var r;const a=null==(r=(t.vars||t).palette)?void 0:r[n.color];return(0,o.A)({},"inherit"===n.color&&{color:"inherit"},"inherit"!==n.color&&"default"!==n.color&&(0,o.A)({color:null==a?void 0:a.main},!n.disableRipple&&{"&:hover":(0,o.A)({},a&&{backgroundColor:t.vars?`rgba(${a.mainChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,X.X4)(a.main,t.palette.action.hoverOpacity)},{"@media (hover: none)":{backgroundColor:"transparent"}})}),"small"===n.size&&{padding:5,fontSize:t.typography.pxToRem(18)},"large"===n.size&&{padding:12,fontSize:t.typography.pxToRem(28)},{[`&.${Oe.disabled}`]:{backgroundColor:"transparent",color:(t.vars||t).palette.action.disabled}})})),Ie=e.forwardRef((function(e,t){const n=(0,S.b)({props:e,name:"MuiIconButton"}),{edge:r=!1,children:i,className:l,color:s="default",disabled:u=!1,disableFocusRipple:d=!1,size:p="medium"}=n,f=(0,a.A)(n,Te),m=(0,o.A)({},n,{edge:r,color:s,disabled:u,disableFocusRipple:d,size:p}),h=(e=>{const{classes:t,disabled:n,color:r,edge:o,size:a}=e,i={root:["root",n&&"disabled","default"!==r&&`color${(0,Pe.A)(r)}`,o&&`edge${(0,Pe.A)(o)}`,`size${(0,Pe.A)(a)}`]};return(0,U.A)(i,Me,t)})(m);return(0,c.jsx)(je,(0,o.A)({className:(0,$.A)(h.root,l),centerRipple:!0,focusRipple:!d,disabled:u,ref:t},f,{ownerState:m,children:i}))}));var Ne=n(3030),$e=n(1475);function ze(e){return(0,Ae.Ay)("MuiButton",e)}const Le=(0,W.A)("MuiButton",["root","text","textInherit","textPrimary","textSecondary","textSuccess","textError","textInfo","textWarning","outlined","outlinedInherit","outlinedPrimary","outlinedSecondary","outlinedSuccess","outlinedError","outlinedInfo","outlinedWarning","contained","containedInherit","containedPrimary","containedSecondary","containedSuccess","containedError","containedInfo","containedWarning","disableElevation","focusVisible","disabled","colorInherit","colorPrimary","colorSecondary","colorSuccess","colorError","colorInfo","colorWarning","textSizeSmall","textSizeMedium","textSizeLarge","outlinedSizeSmall","outlinedSizeMedium","outlinedSizeLarge","containedSizeSmall","containedSizeMedium","containedSizeLarge","sizeMedium","sizeSmall","sizeLarge","fullWidth","startIcon","endIcon","icon","iconSizeSmall","iconSizeMedium","iconSizeLarge"]);const _e=e.createContext({});const Fe=e.createContext(void 0),De=["children","color","component","className","disabled","disableElevation","disableFocusRipple","endIcon","focusVisibleClassName","fullWidth","size","startIcon","type","variant"],We=e=>(0,o.A)({},"small"===e.size&&{"& > *:nth-of-type(1)":{fontSize:18}},"medium"===e.size&&{"& > *:nth-of-type(1)":{fontSize:20}},"large"===e.size&&{"& > *:nth-of-type(1)":{fontSize:22}}),Be=(0,K.Ay)(Re,{shouldForwardProp:e=>(0,$e.A)(e)||"classes"===e,name:"MuiButton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[n.variant],t[`${n.variant}${(0,Pe.A)(n.color)}`],t[`size${(0,Pe.A)(n.size)}`],t[`${n.variant}Size${(0,Pe.A)(n.size)}`],"inherit"===n.color&&t.colorInherit,n.disableElevation&&t.disableElevation,n.fullWidth&&t.fullWidth]}})((e=>{let{theme:t,ownerState:n}=e;var r,a;const i="light"===t.palette.mode?t.palette.grey[300]:t.palette.grey[800],l="light"===t.palette.mode?t.palette.grey.A100:t.palette.grey[700];return(0,o.A)({},t.typography.button,{minWidth:64,padding:"6px 16px",borderRadius:(t.vars||t).shape.borderRadius,transition:t.transitions.create(["background-color","box-shadow","border-color","color"],{duration:t.transitions.duration.short}),"&:hover":(0,o.A)({textDecoration:"none",backgroundColor:t.vars?`rgba(${t.vars.palette.text.primaryChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,X.X4)(t.palette.text.primary,t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},"text"===n.variant&&"inherit"!==n.color&&{backgroundColor:t.vars?`rgba(${t.vars.palette[n.color].mainChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,X.X4)(t.palette[n.color].main,t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},"outlined"===n.variant&&"inherit"!==n.color&&{border:`1px solid ${(t.vars||t).palette[n.color].main}`,backgroundColor:t.vars?`rgba(${t.vars.palette[n.color].mainChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,X.X4)(t.palette[n.color].main,t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},"contained"===n.variant&&{backgroundColor:t.vars?t.vars.palette.Button.inheritContainedHoverBg:l,boxShadow:(t.vars||t).shadows[4],"@media (hover: none)":{boxShadow:(t.vars||t).shadows[2],backgroundColor:(t.vars||t).palette.grey[300]}},"contained"===n.variant&&"inherit"!==n.color&&{backgroundColor:(t.vars||t).palette[n.color].dark,"@media (hover: none)":{backgroundColor:(t.vars||t).palette[n.color].main}}),"&:active":(0,o.A)({},"contained"===n.variant&&{boxShadow:(t.vars||t).shadows[8]}),[`&.${Le.focusVisible}`]:(0,o.A)({},"contained"===n.variant&&{boxShadow:(t.vars||t).shadows[6]}),[`&.${Le.disabled}`]:(0,o.A)({color:(t.vars||t).palette.action.disabled},"outlined"===n.variant&&{border:`1px solid ${(t.vars||t).palette.action.disabledBackground}`},"contained"===n.variant&&{color:(t.vars||t).palette.action.disabled,boxShadow:(t.vars||t).shadows[0],backgroundColor:(t.vars||t).palette.action.disabledBackground})},"text"===n.variant&&{padding:"6px 8px"},"text"===n.variant&&"inherit"!==n.color&&{color:(t.vars||t).palette[n.color].main},"outlined"===n.variant&&{padding:"5px 15px",border:"1px solid currentColor"},"outlined"===n.variant&&"inherit"!==n.color&&{color:(t.vars||t).palette[n.color].main,border:t.vars?`1px solid rgba(${t.vars.palette[n.color].mainChannel} / 0.5)`:`1px solid ${(0,X.X4)(t.palette[n.color].main,.5)}`},"contained"===n.variant&&{color:t.vars?t.vars.palette.text.primary:null==(r=(a=t.palette).getContrastText)?void 0:r.call(a,t.palette.grey[300]),backgroundColor:t.vars?t.vars.palette.Button.inheritContainedBg:i,boxShadow:(t.vars||t).shadows[2]},"contained"===n.variant&&"inherit"!==n.color&&{color:(t.vars||t).palette[n.color].contrastText,backgroundColor:(t.vars||t).palette[n.color].main},"inherit"===n.color&&{color:"inherit",borderColor:"currentColor"},"small"===n.size&&"text"===n.variant&&{padding:"4px 5px",fontSize:t.typography.pxToRem(13)},"large"===n.size&&"text"===n.variant&&{padding:"8px 11px",fontSize:t.typography.pxToRem(15)},"small"===n.size&&"outlined"===n.variant&&{padding:"3px 9px",fontSize:t.typography.pxToRem(13)},"large"===n.size&&"outlined"===n.variant&&{padding:"7px 21px",fontSize:t.typography.pxToRem(15)},"small"===n.size&&"contained"===n.variant&&{padding:"4px 10px",fontSize:t.typography.pxToRem(13)},"large"===n.size&&"contained"===n.variant&&{padding:"8px 22px",fontSize:t.typography.pxToRem(15)},n.fullWidth&&{width:"100%"})}),(e=>{let{ownerState:t}=e;return t.disableElevation&&{boxShadow:"none","&:hover":{boxShadow:"none"},[`&.${Le.focusVisible}`]:{boxShadow:"none"},"&:active":{boxShadow:"none"},[`&.${Le.disabled}`]:{boxShadow:"none"}}})),Ve=(0,K.Ay)("span",{name:"MuiButton",slot:"StartIcon",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.startIcon,t[`iconSize${(0,Pe.A)(n.size)}`]]}})((e=>{let{ownerState:t}=e;return(0,o.A)({display:"inherit",marginRight:8,marginLeft:-4},"small"===t.size&&{marginLeft:-2},We(t))})),He=(0,K.Ay)("span",{name:"MuiButton",slot:"EndIcon",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.endIcon,t[`iconSize${(0,Pe.A)(n.size)}`]]}})((e=>{let{ownerState:t}=e;return(0,o.A)({display:"inherit",marginRight:-4,marginLeft:8},"small"===t.size&&{marginRight:-2},We(t))})),Ue=e.forwardRef((function(t,n){const r=e.useContext(_e),i=e.useContext(Fe),l=(0,Ne.A)(r,t),s=(0,S.b)({props:l,name:"MuiButton"}),{children:u,color:d="primary",component:p="button",className:f,disabled:m=!1,disableElevation:h=!1,disableFocusRipple:g=!1,endIcon:v,focusVisibleClassName:y,fullWidth:b=!1,size:x="medium",startIcon:w,type:A,variant:k="text"}=s,C=(0,a.A)(s,De),E=(0,o.A)({},s,{color:d,component:p,disabled:m,disableElevation:h,disableFocusRipple:g,fullWidth:b,size:x,type:A,variant:k}),R=(e=>{const{color:t,disableElevation:n,fullWidth:r,size:a,variant:i,classes:l}=e,s={root:["root",i,`${i}${(0,Pe.A)(t)}`,`size${(0,Pe.A)(a)}`,`${i}Size${(0,Pe.A)(a)}`,`color${(0,Pe.A)(t)}`,n&&"disableElevation",r&&"fullWidth"],label:["label"],startIcon:["icon","startIcon",`iconSize${(0,Pe.A)(a)}`],endIcon:["icon","endIcon",`iconSize${(0,Pe.A)(a)}`]},c=(0,U.A)(s,ze,l);return(0,o.A)({},l,c)})(E),P=w&&(0,c.jsx)(Ve,{className:R.startIcon,ownerState:E,children:w}),M=v&&(0,c.jsx)(He,{className:R.endIcon,ownerState:E,children:v}),O=i||"";return(0,c.jsxs)(Be,(0,o.A)({ownerState:E,className:(0,$.A)(r.className,R.root,f,O),component:p,disabled:m,focusRipple:!g,focusVisibleClassName:(0,$.A)(R.focusVisible,y),ref:n,type:A},C,{classes:R,children:[P,u,M]}))}));var Xe=n(7598);function Ke(e){let{props:t,name:n,defaultTheme:r,themeId:o}=e,a=P(r);o&&(a=a[o]||a);const i=function(e){const{theme:t,name:n,props:r}=e;return t&&t.components&&t.components[n]&&t.components[n].defaultProps?(0,Ne.A)(t.components[n].defaultProps,r):r}({theme:a,name:n,props:t});return i}var qe=n(9172);const Ge=["ownerState"],Qe=["variants"],Ye=["name","slot","skipVariantsResolver","skipSx","overridesResolver"];function Je(e){return"ownerState"!==e&&"theme"!==e&&"sx"!==e&&"as"!==e}const Ze=(0,E.A)(),et=e=>e?e.charAt(0).toLowerCase()+e.slice(1):e;function tt(e){let{defaultTheme:t,theme:n,themeId:r}=e;return o=n,0===Object.keys(o).length?t:n[r]||n;var o}function nt(e){return e?(t,n)=>n[e]:null}function rt(e,t){let{ownerState:n}=t,r=(0,a.A)(t,Ge);const i="function"===typeof e?e((0,o.A)({ownerState:n},r)):e;if(Array.isArray(i))return i.flatMap((e=>rt(e,(0,o.A)({ownerState:n},r))));if(i&&"object"===typeof i&&Array.isArray(i.variants)){const{variants:e=[]}=i;let t=(0,a.A)(i,Qe);return e.forEach((e=>{let a=!0;"function"===typeof e.props?a=e.props((0,o.A)({ownerState:n},r,n)):Object.keys(e.props).forEach((t=>{(null==n?void 0:n[t])!==e.props[t]&&r[t]!==e.props[t]&&(a=!1)})),a&&(Array.isArray(t)||(t=[t]),t.push("function"===typeof e.style?e.style((0,o.A)({ownerState:n},r,n)):e.style))})),t}return i}const ot=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{themeId:t,defaultTheme:n=Ze,rootShouldForwardProp:r=Je,slotShouldForwardProp:i=Je}=e,l=e=>(0,L.A)((0,o.A)({},e,{theme:tt((0,o.A)({},e,{defaultTheme:n,themeId:t}))}));return l.__mui_systemSx=!0,function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(0,z.internal_processStyles)(e,(e=>e.filter((e=>!(null!=e&&e.__mui_systemSx)))));const{name:c,slot:u,skipVariantsResolver:d,skipSx:p,overridesResolver:f=nt(et(u))}=s,m=(0,a.A)(s,Ye),h=void 0!==d?d:u&&"Root"!==u&&"root"!==u||!1,g=p||!1;let v=Je;"Root"===u||"root"===u?v=r:u?v=i:function(e){return"string"===typeof e&&e.charCodeAt(0)>96}(e)&&(v=void 0);const y=(0,z.default)(e,(0,o.A)({shouldForwardProp:v,label:undefined},m)),b=e=>"function"===typeof e&&e.__emotion_real!==e||(0,qe.Q)(e)?r=>rt(e,(0,o.A)({},r,{theme:tt({theme:r.theme,defaultTheme:n,themeId:t})})):e,x=function(r){let a=b(r);for(var i=arguments.length,s=new Array(i>1?i-1:0),u=1;u<i;u++)s[u-1]=arguments[u];const d=s?s.map(b):[];c&&f&&d.push((e=>{const r=tt((0,o.A)({},e,{defaultTheme:n,themeId:t}));if(!r.components||!r.components[c]||!r.components[c].styleOverrides)return null;const a=r.components[c].styleOverrides,i={};return Object.entries(a).forEach((t=>{let[n,a]=t;i[n]=rt(a,(0,o.A)({},e,{theme:r}))})),f(e,i)})),c&&!h&&d.push((e=>{var r;const a=tt((0,o.A)({},e,{defaultTheme:n,themeId:t}));return rt({variants:null==a||null==(r=a.components)||null==(r=r[c])?void 0:r.variants},(0,o.A)({},e,{theme:a}))})),g||d.push(l);const p=d.length-s.length;if(Array.isArray(r)&&p>0){const e=new Array(p).fill("");a=[...r,...e],a.raw=[...r.raw,...e]}const m=y(a,...d);return e.muiName&&(m.muiName=e.muiName),m};return y.withConfig&&(x.withConfig=y.withConfig),x}}(),at=["className","component","disableGutters","fixed","maxWidth","classes"],it=(0,E.A)(),lt=ot("div",{name:"MuiContainer",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[`maxWidth${(0,Xe.A)(String(n.maxWidth))}`],n.fixed&&t.fixed,n.disableGutters&&t.disableGutters]}}),st=e=>Ke({props:e,name:"MuiContainer",defaultTheme:it});const ct=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{createStyledComponent:n=lt,useThemeProps:r=st,componentName:i="MuiContainer"}=t,l=n((e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({width:"100%",marginLeft:"auto",boxSizing:"border-box",marginRight:"auto",display:"block"},!n.disableGutters&&{paddingLeft:t.spacing(2),paddingRight:t.spacing(2),[t.breakpoints.up("sm")]:{paddingLeft:t.spacing(3),paddingRight:t.spacing(3)}})}),(e=>{let{theme:t,ownerState:n}=e;return n.fixed&&Object.keys(t.breakpoints.values).reduce(((e,n)=>{const r=n,o=t.breakpoints.values[r];return 0!==o&&(e[t.breakpoints.up(r)]={maxWidth:`${o}${t.breakpoints.unit}`}),e}),{})}),(e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({},"xs"===n.maxWidth&&{[t.breakpoints.up("xs")]:{maxWidth:Math.max(t.breakpoints.values.xs,444)}},n.maxWidth&&"xs"!==n.maxWidth&&{[t.breakpoints.up(n.maxWidth)]:{maxWidth:`${t.breakpoints.values[n.maxWidth]}${t.breakpoints.unit}`}})}));return e.forwardRef((function(e,t){const n=r(e),{className:s,component:u="div",disableGutters:d=!1,fixed:p=!1,maxWidth:f="lg"}=n,m=(0,a.A)(n,at),h=(0,o.A)({},n,{component:u,disableGutters:d,fixed:p,maxWidth:f}),g=((e,t)=>{const{classes:n,fixed:r,disableGutters:o,maxWidth:a}=e,i={root:["root",a&&`maxWidth${(0,Xe.A)(String(a))}`,r&&"fixed",o&&"disableGutters"]};return(0,U.A)(i,(e=>(0,Ae.Ay)(t,e)),n)})(h,i);return(0,c.jsx)(l,(0,o.A)({as:u,ownerState:h,className:(0,$.A)(g.root,s),ref:t},m))}))}({createStyledComponent:(0,K.Ay)("div",{name:"MuiContainer",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[`maxWidth${(0,Pe.A)(String(n.maxWidth))}`],n.fixed&&t.fixed,n.disableGutters&&t.disableGutters]}}),useThemeProps:e=>(0,S.b)({props:e,name:"MuiContainer"})}),ut=e=>{let t;return t=e<1?5.11916*e**2:4.5*Math.log(e+1)+2,(t/100).toFixed(2)};function dt(e){return(0,Ae.Ay)("MuiPaper",e)}(0,W.A)("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);const pt=["className","component","elevation","square","variant"],ft=(0,K.Ay)("div",{name:"MuiPaper",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[n.variant],!n.square&&t.rounded,"elevation"===n.variant&&t[`elevation${n.elevation}`]]}})((e=>{let{theme:t,ownerState:n}=e;var r;return(0,o.A)({backgroundColor:(t.vars||t).palette.background.paper,color:(t.vars||t).palette.text.primary,transition:t.transitions.create("box-shadow")},!n.square&&{borderRadius:t.shape.borderRadius},"outlined"===n.variant&&{border:`1px solid ${(t.vars||t).palette.divider}`},"elevation"===n.variant&&(0,o.A)({boxShadow:(t.vars||t).shadows[n.elevation]},!t.vars&&"dark"===t.palette.mode&&{backgroundImage:`linear-gradient(${(0,X.X4)("#fff",ut(n.elevation))}, ${(0,X.X4)("#fff",ut(n.elevation))})`},t.vars&&{backgroundImage:null==(r=t.vars.overlays)?void 0:r[n.elevation]}))})),mt=e.forwardRef((function(e,t){const n=(0,S.b)({props:e,name:"MuiPaper"}),{className:r,component:i="div",elevation:l=1,square:s=!1,variant:u="elevation"}=n,d=(0,a.A)(n,pt),p=(0,o.A)({},n,{component:i,elevation:l,square:s,variant:u}),f=(e=>{const{square:t,elevation:n,variant:r,classes:o}=e,a={root:["root",r,!t&&"rounded","elevation"===r&&`elevation${n}`]};return(0,U.A)(a,dt,o)})(p);return(0,c.jsx)(ft,(0,o.A)({as:i,ownerState:p,className:(0,$.A)(f.root,r),ref:t},d))}));function ht(e){return(0,Ae.Ay)("MuiTypography",e)}(0,W.A)("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);const gt=["align","className","component","gutterBottom","noWrap","paragraph","variant","variantMapping"],vt=(0,K.Ay)("span",{name:"MuiTypography",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.variant&&t[n.variant],"inherit"!==n.align&&t[`align${(0,Pe.A)(n.align)}`],n.noWrap&&t.noWrap,n.gutterBottom&&t.gutterBottom,n.paragraph&&t.paragraph]}})((e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({margin:0},"inherit"===n.variant&&{font:"inherit"},"inherit"!==n.variant&&t.typography[n.variant],"inherit"!==n.align&&{textAlign:n.align},n.noWrap&&{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},n.gutterBottom&&{marginBottom:"0.35em"},n.paragraph&&{marginBottom:16})})),yt={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},bt={primary:"primary.main",textPrimary:"text.primary",secondary:"secondary.main",textSecondary:"text.secondary",error:"error.main"},xt=e.forwardRef((function(e,t){const n=(0,S.b)({props:e,name:"MuiTypography"}),r=(e=>bt[e]||e)(n.color),i=(0,_.A)((0,o.A)({},n,{color:r})),{align:l="inherit",className:s,component:u,gutterBottom:d=!1,noWrap:p=!1,paragraph:f=!1,variant:m="body1",variantMapping:h=yt}=i,g=(0,a.A)(i,gt),v=(0,o.A)({},i,{align:l,color:r,className:s,component:u,gutterBottom:d,noWrap:p,paragraph:f,variant:m,variantMapping:h}),y=u||(f?"p":h[m]||yt[m])||"span",b=(e=>{const{align:t,gutterBottom:n,noWrap:r,paragraph:o,variant:a,classes:i}=e,l={root:["root",a,"inherit"!==e.align&&`align${(0,Pe.A)(t)}`,n&&"gutterBottom",r&&"noWrap",o&&"paragraph"]};return(0,U.A)(l,ht,i)})(v);return(0,c.jsx)(vt,(0,o.A)({as:y,ref:t,ownerState:v,className:(0,$.A)(b.root,s)},g))}));const wt=function(e){return"string"===typeof e};const At=function(e,t,n){return void 0===e||wt(e)?t:(0,o.A)({},t,{ownerState:(0,o.A)({},t.ownerState,n)})};function kt(){const e=P(O.A);return e[w.A]||e}var St=n(7950);const Ct=!1;var Et="unmounted",Rt="exited",Pt="entering",Mt="entered",Ot="exiting",Tt=function(t){function n(e,n){var r;r=t.call(this,e,n)||this;var o,a=n&&!n.isMounting?e.enter:e.appear;return r.appearStatus=null,e.in?a?(o=Rt,r.appearStatus=Pt):o=Mt:o=e.unmountOnExit||e.mountOnEnter?Et:Rt,r.state={status:o},r.nextCallback=null,r}J(n,t),n.getDerivedStateFromProps=function(e,t){return e.in&&t.status===Et?{status:Rt}:null};var r=n.prototype;return r.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},r.componentDidUpdate=function(e){var t=null;if(e!==this.props){var n=this.state.status;this.props.in?n!==Pt&&n!==Mt&&(t=Pt):n!==Pt&&n!==Mt||(t=Ot)}this.updateStatus(!1,t)},r.componentWillUnmount=function(){this.cancelNextCallback()},r.getTimeouts=function(){var e,t,n,r=this.props.timeout;return e=t=n=r,null!=r&&"number"!==typeof r&&(e=r.exit,t=r.enter,n=void 0!==r.appear?r.appear:t),{exit:e,enter:t,appear:n}},r.updateStatus=function(e,t){if(void 0===e&&(e=!1),null!==t)if(this.cancelNextCallback(),t===Pt){if(this.props.unmountOnExit||this.props.mountOnEnter){var n=this.props.nodeRef?this.props.nodeRef.current:St.findDOMNode(this);n&&function(e){e.scrollTop}(n)}this.performEnter(e)}else this.performExit();else this.props.unmountOnExit&&this.state.status===Rt&&this.setState({status:Et})},r.performEnter=function(e){var t=this,n=this.props.enter,r=this.context?this.context.isMounting:e,o=this.props.nodeRef?[r]:[St.findDOMNode(this),r],a=o[0],i=o[1],l=this.getTimeouts(),s=r?l.appear:l.enter;!e&&!n||Ct?this.safeSetState({status:Mt},(function(){t.props.onEntered(a)})):(this.props.onEnter(a,i),this.safeSetState({status:Pt},(function(){t.props.onEntering(a,i),t.onTransitionEnd(s,(function(){t.safeSetState({status:Mt},(function(){t.props.onEntered(a,i)}))}))})))},r.performExit=function(){var e=this,t=this.props.exit,n=this.getTimeouts(),r=this.props.nodeRef?void 0:St.findDOMNode(this);t&&!Ct?(this.props.onExit(r),this.safeSetState({status:Ot},(function(){e.props.onExiting(r),e.onTransitionEnd(n.exit,(function(){e.safeSetState({status:Rt},(function(){e.props.onExited(r)}))}))}))):this.safeSetState({status:Rt},(function(){e.props.onExited(r)}))},r.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},r.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},r.setNextCallback=function(e){var t=this,n=!0;return this.nextCallback=function(r){n&&(n=!1,t.nextCallback=null,e(r))},this.nextCallback.cancel=function(){n=!1},this.nextCallback},r.onTransitionEnd=function(e,t){this.setNextCallback(t);var n=this.props.nodeRef?this.props.nodeRef.current:St.findDOMNode(this),r=null==e&&!this.props.addEndListener;if(n&&!r){if(this.props.addEndListener){var o=this.props.nodeRef?[this.nextCallback]:[n,this.nextCallback],a=o[0],i=o[1];this.props.addEndListener(a,i)}null!=e&&setTimeout(this.nextCallback,e)}else setTimeout(this.nextCallback,0)},r.render=function(){var t=this.state.status;if(t===Et)return null;var n=this.props,r=n.children,o=(n.in,n.mountOnEnter,n.unmountOnExit,n.appear,n.enter,n.exit,n.timeout,n.addEndListener,n.onEnter,n.onEntering,n.onEntered,n.onExit,n.onExiting,n.onExited,n.nodeRef,(0,a.A)(n,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return e.createElement(Z.Provider,{value:null},"function"===typeof r?r(t,o):e.cloneElement(e.Children.only(r),o))},n}(e.Component);function jt(){}Tt.contextType=Z,Tt.propTypes={},Tt.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:jt,onEntering:jt,onEntered:jt,onExit:jt,onExiting:jt,onExited:jt},Tt.UNMOUNTED=Et,Tt.EXITED=Rt,Tt.ENTERING=Pt,Tt.ENTERED=Mt,Tt.EXITING=Ot;const It=Tt,Nt=e=>e.scrollTop;function $t(e,t){var n,r;const{timeout:o,easing:a,style:i={}}=e;return{duration:null!=(n=i.transitionDuration)?n:"number"===typeof o?o:o[t.mode]||0,easing:null!=(r=i.transitionTimingFunction)?r:"object"===typeof a?a[t.mode]:a,delay:i.transitionDelay}}const zt=["addEndListener","appear","children","easing","in","onEnter","onEntered","onEntering","onExit","onExited","onExiting","style","timeout","TransitionComponent"];function Lt(e){return`scale(${e}, ${e**2})`}const _t={entering:{opacity:1,transform:Lt(1)},entered:{opacity:1,transform:"none"}},Ft="undefined"!==typeof navigator&&/^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent)&&/(os |version\/)15(.|_)4/i.test(navigator.userAgent),Dt=e.forwardRef((function(t,n){const{addEndListener:r,appear:i=!0,children:l,easing:s,in:u,onEnter:d,onEntered:p,onEntering:f,onExit:m,onExited:h,onExiting:g,style:v,timeout:y="auto",TransitionComponent:b=It}=t,x=(0,a.A)(t,zt),w=(0,le.A)(),A=e.useRef(),k=kt(),S=e.useRef(null),C=(0,q.A)(S,l.ref,n),E=e=>t=>{if(e){const n=S.current;void 0===t?e(n):e(n,t)}},R=E(f),P=E(((e,t)=>{Nt(e);const{duration:n,delay:r,easing:o}=$t({style:v,timeout:y,easing:s},{mode:"enter"});let a;"auto"===y?(a=k.transitions.getAutoHeightDuration(e.clientHeight),A.current=a):a=n,e.style.transition=[k.transitions.create("opacity",{duration:a,delay:r}),k.transitions.create("transform",{duration:Ft?a:.666*a,delay:r,easing:o})].join(","),d&&d(e,t)})),M=E(p),O=E(g),T=E((e=>{const{duration:t,delay:n,easing:r}=$t({style:v,timeout:y,easing:s},{mode:"exit"});let o;"auto"===y?(o=k.transitions.getAutoHeightDuration(e.clientHeight),A.current=o):o=t,e.style.transition=[k.transitions.create("opacity",{duration:o,delay:n}),k.transitions.create("transform",{duration:Ft?o:.666*o,delay:Ft?n:n||.333*o,easing:r})].join(","),e.style.opacity=0,e.style.transform=Lt(.75),m&&m(e)})),j=E(h);return(0,c.jsx)(b,(0,o.A)({appear:i,in:u,nodeRef:S,onEnter:P,onEntered:M,onEntering:R,onExit:T,onExited:j,onExiting:O,addEndListener:e=>{"auto"===y&&w.start(A.current||0,e),r&&r(S.current,e)},timeout:"auto"===y?null:y},x,{children:(t,n)=>e.cloneElement(l,(0,o.A)({style:(0,o.A)({opacity:0,transform:Lt(.75),visibility:"exited"!==t||u?void 0:"hidden"},_t[t],v,l.props.style),ref:C},n))}))}));Dt.muiSupportAuto=!0;const Wt=Dt;var Bt=n(2374),Vt=n(3462),Ht=n(4440),Ut=n(1668);function Xt(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Kt(e){return e instanceof Xt(e).Element||e instanceof Element}function qt(e){return e instanceof Xt(e).HTMLElement||e instanceof HTMLElement}function Gt(e){return"undefined"!==typeof ShadowRoot&&(e instanceof Xt(e).ShadowRoot||e instanceof ShadowRoot)}var Qt=Math.max,Yt=Math.min,Jt=Math.round;function Zt(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function en(){return!/^((?!chrome|android).)*safari/i.test(Zt())}function tn(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),o=1,a=1;t&&qt(e)&&(o=e.offsetWidth>0&&Jt(r.width)/e.offsetWidth||1,a=e.offsetHeight>0&&Jt(r.height)/e.offsetHeight||1);var i=(Kt(e)?Xt(e):window).visualViewport,l=!en()&&n,s=(r.left+(l&&i?i.offsetLeft:0))/o,c=(r.top+(l&&i?i.offsetTop:0))/a,u=r.width/o,d=r.height/a;return{width:u,height:d,top:c,right:s+u,bottom:c+d,left:s,x:s,y:c}}function nn(e){var t=Xt(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function rn(e){return e?(e.nodeName||"").toLowerCase():null}function on(e){return((Kt(e)?e.ownerDocument:e.document)||window.document).documentElement}function an(e){return tn(on(e)).left+nn(e).scrollLeft}function ln(e){return Xt(e).getComputedStyle(e)}function sn(e){var t=ln(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function cn(e,t,n){void 0===n&&(n=!1);var r=qt(t),o=qt(t)&&function(e){var t=e.getBoundingClientRect(),n=Jt(t.width)/e.offsetWidth||1,r=Jt(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),a=on(t),i=tn(e,o,n),l={scrollLeft:0,scrollTop:0},s={x:0,y:0};return(r||!r&&!n)&&(("body"!==rn(t)||sn(a))&&(l=function(e){return e!==Xt(e)&&qt(e)?{scrollLeft:(t=e).scrollLeft,scrollTop:t.scrollTop}:nn(e);var t}(t)),qt(t)?((s=tn(t,!0)).x+=t.clientLeft,s.y+=t.clientTop):a&&(s.x=an(a))),{x:i.left+l.scrollLeft-s.x,y:i.top+l.scrollTop-s.y,width:i.width,height:i.height}}function un(e){var t=tn(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function dn(e){return"html"===rn(e)?e:e.assignedSlot||e.parentNode||(Gt(e)?e.host:null)||on(e)}function pn(e){return["html","body","#document"].indexOf(rn(e))>=0?e.ownerDocument.body:qt(e)&&sn(e)?e:pn(dn(e))}function fn(e,t){var n;void 0===t&&(t=[]);var r=pn(e),o=r===(null==(n=e.ownerDocument)?void 0:n.body),a=Xt(r),i=o?[a].concat(a.visualViewport||[],sn(r)?r:[]):r,l=t.concat(i);return o?l:l.concat(fn(dn(i)))}function mn(e){return["table","td","th"].indexOf(rn(e))>=0}function hn(e){return qt(e)&&"fixed"!==ln(e).position?e.offsetParent:null}function gn(e){for(var t=Xt(e),n=hn(e);n&&mn(n)&&"static"===ln(n).position;)n=hn(n);return n&&("html"===rn(n)||"body"===rn(n)&&"static"===ln(n).position)?t:n||function(e){var t=/firefox/i.test(Zt());if(/Trident/i.test(Zt())&&qt(e)&&"fixed"===ln(e).position)return null;var n=dn(e);for(Gt(n)&&(n=n.host);qt(n)&&["html","body"].indexOf(rn(n))<0;){var r=ln(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}var vn="top",yn="bottom",bn="right",xn="left",wn="auto",An=[vn,yn,bn,xn],kn="start",Sn="end",Cn="viewport",En="popper",Rn=An.reduce((function(e,t){return e.concat([t+"-"+kn,t+"-"+Sn])}),[]),Pn=[].concat(An,[wn]).reduce((function(e,t){return e.concat([t,t+"-"+kn,t+"-"+Sn])}),[]),Mn=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function On(e){var t=new Map,n=new Set,r=[];function o(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&o(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||o(e)})),r}function Tn(e){var t;return function(){return t||(t=new Promise((function(n){Promise.resolve().then((function(){t=void 0,n(e())}))}))),t}}var jn={placement:"bottom",modifiers:[],strategy:"absolute"};function In(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"===typeof e.getBoundingClientRect)}))}function Nn(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,o=t.defaultOptions,a=void 0===o?jn:o;return function(e,t,n){void 0===n&&(n=a);var o={placement:"bottom",orderedModifiers:[],options:Object.assign({},jn,a),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},i=[],l=!1,s={state:o,setOptions:function(n){var l="function"===typeof n?n(o.options):n;c(),o.options=Object.assign({},a,o.options,l),o.scrollParents={reference:Kt(e)?fn(e):e.contextElement?fn(e.contextElement):[],popper:fn(t)};var u=function(e){var t=On(e);return Mn.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}(function(e){var t=e.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{});return Object.keys(t).map((function(e){return t[e]}))}([].concat(r,o.options.modifiers)));return o.orderedModifiers=u.filter((function(e){return e.enabled})),o.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,a=e.effect;if("function"===typeof a){var l=a({state:o,name:t,instance:s,options:r}),c=function(){};i.push(l||c)}})),s.update()},forceUpdate:function(){if(!l){var e=o.elements,t=e.reference,n=e.popper;if(In(t,n)){o.rects={reference:cn(t,gn(n),"fixed"===o.options.strategy),popper:un(n)},o.reset=!1,o.placement=o.options.placement,o.orderedModifiers.forEach((function(e){return o.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0;r<o.orderedModifiers.length;r++)if(!0!==o.reset){var a=o.orderedModifiers[r],i=a.fn,c=a.options,u=void 0===c?{}:c,d=a.name;"function"===typeof i&&(o=i({state:o,options:u,name:d,instance:s})||o)}else o.reset=!1,r=-1}}},update:Tn((function(){return new Promise((function(e){s.forceUpdate(),e(o)}))})),destroy:function(){c(),l=!0}};if(!In(e,t))return s;function c(){i.forEach((function(e){return e()})),i=[]}return s.setOptions(n).then((function(e){!l&&n.onFirstUpdate&&n.onFirstUpdate(e)})),s}}var $n={passive:!0};function zn(e){return e.split("-")[0]}function Ln(e){return e.split("-")[1]}function _n(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Fn(e){var t,n=e.reference,r=e.element,o=e.placement,a=o?zn(o):null,i=o?Ln(o):null,l=n.x+n.width/2-r.width/2,s=n.y+n.height/2-r.height/2;switch(a){case vn:t={x:l,y:n.y-r.height};break;case yn:t={x:l,y:n.y+n.height};break;case bn:t={x:n.x+n.width,y:s};break;case xn:t={x:n.x-r.width,y:s};break;default:t={x:n.x,y:n.y}}var c=a?_n(a):null;if(null!=c){var u="y"===c?"height":"width";switch(i){case kn:t[c]=t[c]-(n[u]/2-r[u]/2);break;case Sn:t[c]=t[c]+(n[u]/2-r[u]/2)}}return t}var Dn={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Wn(e){var t,n=e.popper,r=e.popperRect,o=e.placement,a=e.variation,i=e.offsets,l=e.position,s=e.gpuAcceleration,c=e.adaptive,u=e.roundOffsets,d=e.isFixed,p=i.x,f=void 0===p?0:p,m=i.y,h=void 0===m?0:m,g="function"===typeof u?u({x:f,y:h}):{x:f,y:h};f=g.x,h=g.y;var v=i.hasOwnProperty("x"),y=i.hasOwnProperty("y"),b=xn,x=vn,w=window;if(c){var A=gn(n),k="clientHeight",S="clientWidth";if(A===Xt(n)&&"static"!==ln(A=on(n)).position&&"absolute"===l&&(k="scrollHeight",S="scrollWidth"),o===vn||(o===xn||o===bn)&&a===Sn)x=yn,h-=(d&&A===w&&w.visualViewport?w.visualViewport.height:A[k])-r.height,h*=s?1:-1;if(o===xn||(o===vn||o===yn)&&a===Sn)b=bn,f-=(d&&A===w&&w.visualViewport?w.visualViewport.width:A[S])-r.width,f*=s?1:-1}var C,E=Object.assign({position:l},c&&Dn),R=!0===u?function(e,t){var n=e.x,r=e.y,o=t.devicePixelRatio||1;return{x:Jt(n*o)/o||0,y:Jt(r*o)/o||0}}({x:f,y:h},Xt(n)):{x:f,y:h};return f=R.x,h=R.y,s?Object.assign({},E,((C={})[x]=y?"0":"",C[b]=v?"0":"",C.transform=(w.devicePixelRatio||1)<=1?"translate("+f+"px, "+h+"px)":"translate3d("+f+"px, "+h+"px, 0)",C)):Object.assign({},E,((t={})[x]=y?h+"px":"",t[b]=v?f+"px":"",t.transform="",t))}var Bn={left:"right",right:"left",bottom:"top",top:"bottom"};function Vn(e){return e.replace(/left|right|bottom|top/g,(function(e){return Bn[e]}))}var Hn={start:"end",end:"start"};function Un(e){return e.replace(/start|end/g,(function(e){return Hn[e]}))}function Xn(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Gt(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function Kn(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function qn(e,t,n){return t===Cn?Kn(function(e,t){var n=Xt(e),r=on(e),o=n.visualViewport,a=r.clientWidth,i=r.clientHeight,l=0,s=0;if(o){a=o.width,i=o.height;var c=en();(c||!c&&"fixed"===t)&&(l=o.offsetLeft,s=o.offsetTop)}return{width:a,height:i,x:l+an(e),y:s}}(e,n)):Kt(t)?function(e,t){var n=tn(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):Kn(function(e){var t,n=on(e),r=nn(e),o=null==(t=e.ownerDocument)?void 0:t.body,a=Qt(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),i=Qt(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),l=-r.scrollLeft+an(e),s=-r.scrollTop;return"rtl"===ln(o||n).direction&&(l+=Qt(n.clientWidth,o?o.clientWidth:0)-a),{width:a,height:i,x:l,y:s}}(on(e)))}function Gn(e,t,n,r){var o="clippingParents"===t?function(e){var t=fn(dn(e)),n=["absolute","fixed"].indexOf(ln(e).position)>=0&&qt(e)?gn(e):e;return Kt(n)?t.filter((function(e){return Kt(e)&&Xn(e,n)&&"body"!==rn(e)})):[]}(e):[].concat(t),a=[].concat(o,[n]),i=a[0],l=a.reduce((function(t,n){var o=qn(e,n,r);return t.top=Qt(o.top,t.top),t.right=Yt(o.right,t.right),t.bottom=Yt(o.bottom,t.bottom),t.left=Qt(o.left,t.left),t}),qn(e,i,r));return l.width=l.right-l.left,l.height=l.bottom-l.top,l.x=l.left,l.y=l.top,l}function Qn(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Yn(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}function Jn(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=void 0===r?e.placement:r,a=n.strategy,i=void 0===a?e.strategy:a,l=n.boundary,s=void 0===l?"clippingParents":l,c=n.rootBoundary,u=void 0===c?Cn:c,d=n.elementContext,p=void 0===d?En:d,f=n.altBoundary,m=void 0!==f&&f,h=n.padding,g=void 0===h?0:h,v=Qn("number"!==typeof g?g:Yn(g,An)),y=p===En?"reference":En,b=e.rects.popper,x=e.elements[m?y:p],w=Gn(Kt(x)?x:x.contextElement||on(e.elements.popper),s,u,i),A=tn(e.elements.reference),k=Fn({reference:A,element:b,strategy:"absolute",placement:o}),S=Kn(Object.assign({},b,k)),C=p===En?S:A,E={top:w.top-C.top+v.top,bottom:C.bottom-w.bottom+v.bottom,left:w.left-C.left+v.left,right:C.right-w.right+v.right},R=e.modifiersData.offset;if(p===En&&R){var P=R[o];Object.keys(E).forEach((function(e){var t=[bn,yn].indexOf(e)>=0?1:-1,n=[vn,yn].indexOf(e)>=0?"y":"x";E[e]+=P[n]*t}))}return E}function Zn(e,t,n){return Qt(e,Yt(t,n))}function er(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function tr(e){return[vn,bn,yn,xn].some((function(t){return e[t]>=0}))}var nr=Nn({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,a=void 0===o||o,i=r.resize,l=void 0===i||i,s=Xt(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&c.forEach((function(e){e.addEventListener("scroll",n.update,$n)})),l&&s.addEventListener("resize",n.update,$n),function(){a&&c.forEach((function(e){e.removeEventListener("scroll",n.update,$n)})),l&&s.removeEventListener("resize",n.update,$n)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Fn({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=void 0===r||r,a=n.adaptive,i=void 0===a||a,l=n.roundOffsets,s=void 0===l||l,c={placement:zn(t.placement),variation:Ln(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,Wn(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:i,roundOffsets:s})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,Wn(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:s})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},{name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},o=t.elements[e];qt(o)&&rn(o)&&(Object.assign(o.style,n),Object.keys(r).forEach((function(e){var t=r[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var r=t.elements[e],o=t.attributes[e]||{},a=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});qt(r)&&rn(r)&&(Object.assign(r.style,a),Object.keys(o).forEach((function(e){r.removeAttribute(e)})))}))}},requires:["computeStyles"]},{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.offset,a=void 0===o?[0,0]:o,i=Pn.reduce((function(e,n){return e[n]=function(e,t,n){var r=zn(e),o=[xn,vn].indexOf(r)>=0?-1:1,a="function"===typeof n?n(Object.assign({},t,{placement:e})):n,i=a[0],l=a[1];return i=i||0,l=(l||0)*o,[xn,bn].indexOf(r)>=0?{x:l,y:i}:{x:i,y:l}}(n,t.rects,a),e}),{}),l=i[t.placement],s=l.x,c=l.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=s,t.modifiersData.popperOffsets.y+=c),t.modifiersData[r]=i}},{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,a=void 0===o||o,i=n.altAxis,l=void 0===i||i,s=n.fallbackPlacements,c=n.padding,u=n.boundary,d=n.rootBoundary,p=n.altBoundary,f=n.flipVariations,m=void 0===f||f,h=n.allowedAutoPlacements,g=t.options.placement,v=zn(g),y=s||(v===g||!m?[Vn(g)]:function(e){if(zn(e)===wn)return[];var t=Vn(e);return[Un(e),t,Un(t)]}(g)),b=[g].concat(y).reduce((function(e,n){return e.concat(zn(n)===wn?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=n.boundary,a=n.rootBoundary,i=n.padding,l=n.flipVariations,s=n.allowedAutoPlacements,c=void 0===s?Pn:s,u=Ln(r),d=u?l?Rn:Rn.filter((function(e){return Ln(e)===u})):An,p=d.filter((function(e){return c.indexOf(e)>=0}));0===p.length&&(p=d);var f=p.reduce((function(t,n){return t[n]=Jn(e,{placement:n,boundary:o,rootBoundary:a,padding:i})[zn(n)],t}),{});return Object.keys(f).sort((function(e,t){return f[e]-f[t]}))}(t,{placement:n,boundary:u,rootBoundary:d,padding:c,flipVariations:m,allowedAutoPlacements:h}):n)}),[]),x=t.rects.reference,w=t.rects.popper,A=new Map,k=!0,S=b[0],C=0;C<b.length;C++){var E=b[C],R=zn(E),P=Ln(E)===kn,M=[vn,yn].indexOf(R)>=0,O=M?"width":"height",T=Jn(t,{placement:E,boundary:u,rootBoundary:d,altBoundary:p,padding:c}),j=M?P?bn:xn:P?yn:vn;x[O]>w[O]&&(j=Vn(j));var I=Vn(j),N=[];if(a&&N.push(T[R]<=0),l&&N.push(T[j]<=0,T[I]<=0),N.every((function(e){return e}))){S=E,k=!1;break}A.set(E,N)}if(k)for(var $=function(e){var t=b.find((function(t){var n=A.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return S=t,"break"},z=m?3:1;z>0;z--){if("break"===$(z))break}t.placement!==S&&(t.modifiersData[r]._skip=!0,t.placement=S,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,a=void 0===o||o,i=n.altAxis,l=void 0!==i&&i,s=n.boundary,c=n.rootBoundary,u=n.altBoundary,d=n.padding,p=n.tether,f=void 0===p||p,m=n.tetherOffset,h=void 0===m?0:m,g=Jn(t,{boundary:s,rootBoundary:c,padding:d,altBoundary:u}),v=zn(t.placement),y=Ln(t.placement),b=!y,x=_n(v),w="x"===x?"y":"x",A=t.modifiersData.popperOffsets,k=t.rects.reference,S=t.rects.popper,C="function"===typeof h?h(Object.assign({},t.rects,{placement:t.placement})):h,E="number"===typeof C?{mainAxis:C,altAxis:C}:Object.assign({mainAxis:0,altAxis:0},C),R=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,P={x:0,y:0};if(A){if(a){var M,O="y"===x?vn:xn,T="y"===x?yn:bn,j="y"===x?"height":"width",I=A[x],N=I+g[O],$=I-g[T],z=f?-S[j]/2:0,L=y===kn?k[j]:S[j],_=y===kn?-S[j]:-k[j],F=t.elements.arrow,D=f&&F?un(F):{width:0,height:0},W=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},B=W[O],V=W[T],H=Zn(0,k[j],D[j]),U=b?k[j]/2-z-H-B-E.mainAxis:L-H-B-E.mainAxis,X=b?-k[j]/2+z+H+V+E.mainAxis:_+H+V+E.mainAxis,K=t.elements.arrow&&gn(t.elements.arrow),q=K?"y"===x?K.clientTop||0:K.clientLeft||0:0,G=null!=(M=null==R?void 0:R[x])?M:0,Q=I+X-G,Y=Zn(f?Yt(N,I+U-G-q):N,I,f?Qt($,Q):$);A[x]=Y,P[x]=Y-I}if(l){var J,Z="x"===x?vn:xn,ee="x"===x?yn:bn,te=A[w],ne="y"===w?"height":"width",re=te+g[Z],oe=te-g[ee],ae=-1!==[vn,xn].indexOf(v),ie=null!=(J=null==R?void 0:R[w])?J:0,le=ae?re:te-k[ne]-S[ne]-ie+E.altAxis,se=ae?te+k[ne]+S[ne]-ie-E.altAxis:oe,ce=f&&ae?function(e,t,n){var r=Zn(e,t,n);return r>n?n:r}(le,te,se):Zn(f?le:re,te,f?se:oe);A[w]=ce,P[w]=ce-te}t.modifiersData[r]=P}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,o=e.options,a=n.elements.arrow,i=n.modifiersData.popperOffsets,l=zn(n.placement),s=_n(l),c=[xn,bn].indexOf(l)>=0?"height":"width";if(a&&i){var u=function(e,t){return Qn("number"!==typeof(e="function"===typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:Yn(e,An))}(o.padding,n),d=un(a),p="y"===s?vn:xn,f="y"===s?yn:bn,m=n.rects.reference[c]+n.rects.reference[s]-i[s]-n.rects.popper[c],h=i[s]-n.rects.reference[s],g=gn(a),v=g?"y"===s?g.clientHeight||0:g.clientWidth||0:0,y=m/2-h/2,b=u[p],x=v-d[c]-u[f],w=v/2-d[c]/2+y,A=Zn(b,w,x),k=s;n.modifiersData[r]=((t={})[k]=A,t.centerOffset=A-w,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!==typeof r||(r=t.elements.popper.querySelector(r)))&&Xn(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,a=t.modifiersData.preventOverflow,i=Jn(t,{elementContext:"reference"}),l=Jn(t,{altBoundary:!0}),s=er(i,r),c=er(l,o,a),u=tr(s),d=tr(c);t.modifiersData[n]={referenceClippingOffsets:s,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":d})}}]});const rr=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(void 0===e)return{};const n={};return Object.keys(e).filter((n=>n.match(/^on[A-Z]/)&&"function"===typeof e[n]&&!t.includes(n))).forEach((t=>{n[t]=e[t]})),n};const or=function(e){if(void 0===e)return{};const t={};return Object.keys(e).filter((t=>!(t.match(/^on[A-Z]/)&&"function"===typeof e[t]))).forEach((n=>{t[n]=e[n]})),t};const ar=function(e){const{getSlotProps:t,additionalProps:n,externalSlotProps:r,externalForwardedProps:a,className:i}=e;if(!t){const e=(0,$.A)(null==n?void 0:n.className,i,null==a?void 0:a.className,null==r?void 0:r.className),t=(0,o.A)({},null==n?void 0:n.style,null==a?void 0:a.style,null==r?void 0:r.style),l=(0,o.A)({},n,a,r);return e.length>0&&(l.className=e),Object.keys(t).length>0&&(l.style=t),{props:l,internalRef:void 0}}const l=rr((0,o.A)({},a,r)),s=or(r),c=or(a),u=t(l),d=(0,$.A)(null==u?void 0:u.className,null==n?void 0:n.className,i,null==a?void 0:a.className,null==r?void 0:r.className),p=(0,o.A)({},null==u?void 0:u.style,null==n?void 0:n.style,null==a?void 0:a.style,null==r?void 0:r.style),f=(0,o.A)({},u,n,c,s);return d.length>0&&(f.className=d),Object.keys(p).length>0&&(f.style=p),{props:f,internalRef:u.ref}};const ir=function(e,t,n){return"function"===typeof e?e(t,n):e},lr=["elementType","externalSlotProps","ownerState","skipResolvingSlotProps"];const sr=function(e){var t;const{elementType:n,externalSlotProps:r,ownerState:i,skipResolvingSlotProps:l=!1}=e,s=(0,a.A)(e,lr),c=l?{}:ir(r,i),{props:u,internalRef:d}=ar((0,o.A)({},s,{externalSlotProps:c})),p=(0,Vt.A)(d,null==c?void 0:c.ref,null==(t=e.additionalProps)?void 0:t.ref);return At(n,(0,o.A)({},u,{ref:p}),i)};var cr=n(6564);const ur=e.forwardRef((function(t,n){const{children:r,container:o,disablePortal:a=!1}=t,[i,l]=e.useState(null),s=(0,Vt.A)(e.isValidElement(r)?r.ref:null,n);if((0,Ht.A)((()=>{a||l(function(e){return"function"===typeof e?e():e}(o)||document.body)}),[o,a]),(0,Ht.A)((()=>{if(i&&!a)return(0,cr.A)(n,i),()=>{(0,cr.A)(n,null)}}),[n,i,a]),a){if(e.isValidElement(r)){const t={ref:s};return e.cloneElement(r,t)}return(0,c.jsx)(e.Fragment,{children:r})}return(0,c.jsx)(e.Fragment,{children:i?St.createPortal(r,i):i})}));function dr(e){return(0,Ae.Ay)("MuiPopper",e)}(0,W.A)("MuiPopper",["root"]);const pr=["anchorEl","children","direction","disablePortal","modifiers","open","placement","popperOptions","popperRef","slotProps","slots","TransitionProps","ownerState"],fr=["anchorEl","children","container","direction","disablePortal","keepMounted","modifiers","open","placement","popperOptions","popperRef","style","transition","slotProps","slots"];function mr(e){return"function"===typeof e?e():e}function hr(e){return void 0!==e.nodeType}const gr={},vr=e.forwardRef((function(t,n){var r;const{anchorEl:i,children:l,direction:s,disablePortal:u,modifiers:d,open:p,placement:f,popperOptions:m,popperRef:h,slotProps:g={},slots:v={},TransitionProps:y}=t,b=(0,a.A)(t,pr),x=e.useRef(null),w=(0,Vt.A)(x,n),A=e.useRef(null),k=(0,Vt.A)(A,h),S=e.useRef(k);(0,Ht.A)((()=>{S.current=k}),[k]),e.useImperativeHandle(h,(()=>A.current),[]);const C=function(e,t){if("ltr"===t)return e;switch(e){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return e}}(f,s),[E,R]=e.useState(C),[P,M]=e.useState(mr(i));e.useEffect((()=>{A.current&&A.current.forceUpdate()})),e.useEffect((()=>{i&&M(mr(i))}),[i]),(0,Ht.A)((()=>{if(!P||!p)return;let e=[{name:"preventOverflow",options:{altBoundary:u}},{name:"flip",options:{altBoundary:u}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:e=>{let{state:t}=e;R(t.placement)}}];null!=d&&(e=e.concat(d)),m&&null!=m.modifiers&&(e=e.concat(m.modifiers));const t=nr(P,x.current,(0,o.A)({placement:C},m,{modifiers:e}));return S.current(t),()=>{t.destroy(),S.current(null)}}),[P,u,d,p,m,C]);const O={placement:E};null!==y&&(O.TransitionProps=y);const T=(e=>{const{classes:t}=e;return(0,U.A)({root:["root"]},dr,t)})(t),j=null!=(r=v.root)?r:"div",I=sr({elementType:j,externalSlotProps:g.root,externalForwardedProps:b,additionalProps:{role:"tooltip",ref:w},ownerState:t,className:T.root});return(0,c.jsx)(j,(0,o.A)({},I,{children:"function"===typeof l?l(O):l}))})),yr=e.forwardRef((function(t,n){const{anchorEl:r,children:i,container:l,direction:s="ltr",disablePortal:u=!1,keepMounted:d=!1,modifiers:p,open:f,placement:m="bottom",popperOptions:h=gr,popperRef:g,style:v,transition:y=!1,slotProps:b={},slots:x={}}=t,w=(0,a.A)(t,fr),[A,k]=e.useState(!0);if(!d&&!f&&(!y||A))return null;let S;if(l)S=l;else if(r){const e=mr(r);S=e&&hr(e)?(0,Ut.A)(e).body:(0,Ut.A)(null).body}const C=f||!d||y&&!A?void 0:"none",E=y?{in:f,onEnter:()=>{k(!1)},onExited:()=>{k(!0)}}:void 0;return(0,c.jsx)(ur,{disablePortal:u,container:S,children:(0,c.jsx)(vr,(0,o.A)({anchorEl:r,direction:s,disablePortal:u,modifiers:p,ref:n,open:y?!A:f,placement:m,popperOptions:h,popperRef:g,slotProps:b,slots:x},w,{style:(0,o.A)({position:"fixed",top:0,left:0,display:C},v),TransitionProps:E,children:i}))})})),br=["anchorEl","component","components","componentsProps","container","disablePortal","keepMounted","modifiers","open","placement","popperOptions","popperRef","transition","slots","slotProps"],xr=(0,K.Ay)(yr,{name:"MuiPopper",slot:"Root",overridesResolver:(e,t)=>t.root})({}),wr=e.forwardRef((function(e,t){var n;const r=(0,Bt.A)(),i=(0,S.b)({props:e,name:"MuiPopper"}),{anchorEl:l,component:s,components:u,componentsProps:d,container:p,disablePortal:f,keepMounted:m,modifiers:h,open:g,placement:v,popperOptions:y,popperRef:b,transition:x,slots:w,slotProps:A}=i,k=(0,a.A)(i,br),C=null!=(n=null==w?void 0:w.root)?n:null==u?void 0:u.Root,E=(0,o.A)({anchorEl:l,container:p,disablePortal:f,keepMounted:m,modifiers:h,open:g,placement:v,popperOptions:y,popperRef:b,transition:x},k);return(0,c.jsx)(xr,(0,o.A)({as:s,direction:null==r?void 0:r.direction,slots:{root:C},slotProps:null!=A?A:d},E,{ref:t}))}));var Ar=n(5879),kr=n(5420);function Sr(e){return(0,Ae.Ay)("MuiTooltip",e)}const Cr=(0,W.A)("MuiTooltip",["popper","popperInteractive","popperArrow","popperClose","tooltip","tooltipArrow","touch","tooltipPlacementLeft","tooltipPlacementRight","tooltipPlacementTop","tooltipPlacementBottom","arrow"]),Er=["arrow","children","classes","components","componentsProps","describeChild","disableFocusListener","disableHoverListener","disableInteractive","disableTouchListener","enterDelay","enterNextDelay","enterTouchDelay","followCursor","id","leaveDelay","leaveTouchDelay","onClose","onOpen","open","placement","PopperComponent","PopperProps","slotProps","slots","title","TransitionComponent","TransitionProps"];const Rr=(0,K.Ay)(wr,{name:"MuiTooltip",slot:"Popper",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.popper,!n.disableInteractive&&t.popperInteractive,n.arrow&&t.popperArrow,!n.open&&t.popperClose]}})((e=>{let{theme:t,ownerState:n,open:r}=e;return(0,o.A)({zIndex:(t.vars||t).zIndex.tooltip,pointerEvents:"none"},!n.disableInteractive&&{pointerEvents:"auto"},!r&&{pointerEvents:"none"},n.arrow&&{[`&[data-popper-placement*="bottom"] .${Cr.arrow}`]:{top:0,marginTop:"-0.71em","&::before":{transformOrigin:"0 100%"}},[`&[data-popper-placement*="top"] .${Cr.arrow}`]:{bottom:0,marginBottom:"-0.71em","&::before":{transformOrigin:"100% 0"}},[`&[data-popper-placement*="right"] .${Cr.arrow}`]:(0,o.A)({},n.isRtl?{right:0,marginRight:"-0.71em"}:{left:0,marginLeft:"-0.71em"},{height:"1em",width:"0.71em","&::before":{transformOrigin:"100% 100%"}}),[`&[data-popper-placement*="left"] .${Cr.arrow}`]:(0,o.A)({},n.isRtl?{left:0,marginLeft:"-0.71em"}:{right:0,marginRight:"-0.71em"},{height:"1em",width:"0.71em","&::before":{transformOrigin:"0 0"}})})})),Pr=(0,K.Ay)("div",{name:"MuiTooltip",slot:"Tooltip",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.tooltip,n.touch&&t.touch,n.arrow&&t.tooltipArrow,t[`tooltipPlacement${(0,Pe.A)(n.placement.split("-")[0])}`]]}})((e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({backgroundColor:t.vars?t.vars.palette.Tooltip.bg:(0,X.X4)(t.palette.grey[700],.92),borderRadius:(t.vars||t).shape.borderRadius,color:(t.vars||t).palette.common.white,fontFamily:t.typography.fontFamily,padding:"4px 8px",fontSize:t.typography.pxToRem(11),maxWidth:300,margin:2,wordWrap:"break-word",fontWeight:t.typography.fontWeightMedium},n.arrow&&{position:"relative",margin:0},n.touch&&{padding:"8px 16px",fontSize:t.typography.pxToRem(14),lineHeight:(r=16/14,Math.round(1e5*r)/1e5)+"em",fontWeight:t.typography.fontWeightRegular},{[`.${Cr.popper}[data-popper-placement*="left"] &`]:(0,o.A)({transformOrigin:"right center"},n.isRtl?(0,o.A)({marginLeft:"14px"},n.touch&&{marginLeft:"24px"}):(0,o.A)({marginRight:"14px"},n.touch&&{marginRight:"24px"})),[`.${Cr.popper}[data-popper-placement*="right"] &`]:(0,o.A)({transformOrigin:"left center"},n.isRtl?(0,o.A)({marginRight:"14px"},n.touch&&{marginRight:"24px"}):(0,o.A)({marginLeft:"14px"},n.touch&&{marginLeft:"24px"})),[`.${Cr.popper}[data-popper-placement*="top"] &`]:(0,o.A)({transformOrigin:"center bottom",marginBottom:"14px"},n.touch&&{marginBottom:"24px"}),[`.${Cr.popper}[data-popper-placement*="bottom"] &`]:(0,o.A)({transformOrigin:"center top",marginTop:"14px"},n.touch&&{marginTop:"24px"})});var r})),Mr=(0,K.Ay)("span",{name:"MuiTooltip",slot:"Arrow",overridesResolver:(e,t)=>t.arrow})((e=>{let{theme:t}=e;return{overflow:"hidden",position:"absolute",width:"1em",height:"0.71em",boxSizing:"border-box",color:t.vars?t.vars.palette.Tooltip.bg:(0,X.X4)(t.palette.grey[700],.9),"&::before":{content:'""',margin:"auto",display:"block",width:"100%",height:"100%",backgroundColor:"currentColor",transform:"rotate(45deg)"}}}));let Or=!1;const Tr=new le.E;let jr={x:0,y:0};function Ir(e,t){return function(n){for(var r=arguments.length,o=new Array(r>1?r-1:0),a=1;a<r;a++)o[a-1]=arguments[a];t&&t(n,...o),e(n,...o)}}const Nr=e.forwardRef((function(t,n){var r,i,l,s,u,d,p,f,m,g,v,y,b,x,w,A,k,C,E;const R=(0,S.b)({props:t,name:"MuiTooltip"}),{arrow:P=!1,children:M,components:O={},componentsProps:T={},describeChild:j=!1,disableFocusListener:I=!1,disableHoverListener:N=!1,disableInteractive:z=!1,disableTouchListener:L=!1,enterDelay:_=100,enterNextDelay:F=0,enterTouchDelay:D=700,followCursor:W=!1,id:B,leaveDelay:V=0,leaveTouchDelay:H=1500,onClose:X,onOpen:K,open:Y,placement:J="bottom",PopperComponent:Z,PopperProps:ee={},slotProps:te={},slots:ne={},title:re,TransitionComponent:oe=Wt,TransitionProps:ae}=R,ie=(0,a.A)(R,Er),se=e.isValidElement(M)?M:(0,c.jsx)("span",{children:M}),ce=kt(),ue=h(),[de,pe]=e.useState(),[fe,me]=e.useState(null),he=e.useRef(!1),ge=z||W,ve=(0,le.A)(),ye=(0,le.A)(),be=(0,le.A)(),xe=(0,le.A)(),[we,Ae]=(0,kr.A)({controlled:Y,default:!1,name:"Tooltip",state:"open"});let ke=we;const Se=(0,Ar.A)(B),Ce=e.useRef(),Ee=(0,G.A)((()=>{void 0!==Ce.current&&(document.body.style.WebkitUserSelect=Ce.current,Ce.current=void 0),xe.clear()}));e.useEffect((()=>Ee),[Ee]);const Re=e=>{Tr.clear(),Or=!0,Ae(!0),K&&!ke&&K(e)},Me=(0,G.A)((e=>{Tr.start(800+V,(()=>{Or=!1})),Ae(!1),X&&ke&&X(e),ve.start(ce.transitions.duration.shortest,(()=>{he.current=!1}))})),Oe=e=>{he.current&&"touchstart"!==e.type||(de&&de.removeAttribute("title"),ye.clear(),be.clear(),_||Or&&F?ye.start(Or?F:_,(()=>{Re(e)})):Re(e))},Te=e=>{ye.clear(),be.start(V,(()=>{Me(e)}))},{isFocusVisibleRef:je,onBlur:Ie,onFocus:Ne,ref:$e}=(0,Q.A)(),[,ze]=e.useState(!1),Le=e=>{Ie(e),!1===je.current&&(ze(!1),Te(e))},_e=e=>{de||pe(e.currentTarget),Ne(e),!0===je.current&&(ze(!0),Oe(e))},Fe=e=>{he.current=!0;const t=se.props;t.onTouchStart&&t.onTouchStart(e)},De=e=>{Fe(e),be.clear(),ve.clear(),Ee(),Ce.current=document.body.style.WebkitUserSelect,document.body.style.WebkitUserSelect="none",xe.start(D,(()=>{document.body.style.WebkitUserSelect=Ce.current,Oe(e)}))},We=e=>{se.props.onTouchEnd&&se.props.onTouchEnd(e),Ee(),be.start(H,(()=>{Me(e)}))};e.useEffect((()=>{if(ke)return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)};function e(e){"Escape"!==e.key&&"Esc"!==e.key||Me(e)}}),[Me,ke]);const Be=(0,q.A)(se.ref,$e,pe,n);re||0===re||(ke=!1);const Ve=e.useRef(),He={},Ue="string"===typeof re;j?(He.title=ke||!Ue||N?null:re,He["aria-describedby"]=ke?Se:null):(He["aria-label"]=Ue?re:null,He["aria-labelledby"]=ke&&!Ue?Se:null);const Xe=(0,o.A)({},He,ie,se.props,{className:(0,$.A)(ie.className,se.props.className),onTouchStart:Fe,ref:Be},W?{onMouseMove:e=>{const t=se.props;t.onMouseMove&&t.onMouseMove(e),jr={x:e.clientX,y:e.clientY},Ve.current&&Ve.current.update()}}:{});const Ke={};L||(Xe.onTouchStart=De,Xe.onTouchEnd=We),N||(Xe.onMouseOver=Ir(Oe,Xe.onMouseOver),Xe.onMouseLeave=Ir(Te,Xe.onMouseLeave),ge||(Ke.onMouseOver=Oe,Ke.onMouseLeave=Te)),I||(Xe.onFocus=Ir(_e,Xe.onFocus),Xe.onBlur=Ir(Le,Xe.onBlur),ge||(Ke.onFocus=_e,Ke.onBlur=Le));const qe=e.useMemo((()=>{var e;let t=[{name:"arrow",enabled:Boolean(fe),options:{element:fe,padding:4}}];return null!=(e=ee.popperOptions)&&e.modifiers&&(t=t.concat(ee.popperOptions.modifiers)),(0,o.A)({},ee.popperOptions,{modifiers:t})}),[fe,ee]),Ge=(0,o.A)({},R,{isRtl:ue,arrow:P,disableInteractive:ge,placement:J,PopperComponentProp:Z,touch:he.current}),Qe=(e=>{const{classes:t,disableInteractive:n,arrow:r,touch:o,placement:a}=e,i={popper:["popper",!n&&"popperInteractive",r&&"popperArrow"],tooltip:["tooltip",r&&"tooltipArrow",o&&"touch",`tooltipPlacement${(0,Pe.A)(a.split("-")[0])}`],arrow:["arrow"]};return(0,U.A)(i,Sr,t)})(Ge),Ye=null!=(r=null!=(i=ne.popper)?i:O.Popper)?r:Rr,Je=null!=(l=null!=(s=null!=(u=ne.transition)?u:O.Transition)?s:oe)?l:Wt,Ze=null!=(d=null!=(p=ne.tooltip)?p:O.Tooltip)?d:Pr,et=null!=(f=null!=(m=ne.arrow)?m:O.Arrow)?f:Mr,tt=At(Ye,(0,o.A)({},ee,null!=(g=te.popper)?g:T.popper,{className:(0,$.A)(Qe.popper,null==ee?void 0:ee.className,null==(v=null!=(y=te.popper)?y:T.popper)?void 0:v.className)}),Ge),nt=At(Je,(0,o.A)({},ae,null!=(b=te.transition)?b:T.transition),Ge),rt=At(Ze,(0,o.A)({},null!=(x=te.tooltip)?x:T.tooltip,{className:(0,$.A)(Qe.tooltip,null==(w=null!=(A=te.tooltip)?A:T.tooltip)?void 0:w.className)}),Ge),ot=At(et,(0,o.A)({},null!=(k=te.arrow)?k:T.arrow,{className:(0,$.A)(Qe.arrow,null==(C=null!=(E=te.arrow)?E:T.arrow)?void 0:C.className)}),Ge);return(0,c.jsxs)(e.Fragment,{children:[e.cloneElement(se,Xe),(0,c.jsx)(Ye,(0,o.A)({as:null!=Z?Z:wr,placement:J,anchorEl:W?{getBoundingClientRect:()=>({top:jr.y,left:jr.x,right:jr.x,bottom:jr.y,width:0,height:0})}:de,popperRef:Ve,open:!!de&&ke,id:Se,transition:!0},Ke,tt,{popperOptions:qe,children:e=>{let{TransitionProps:t}=e;return(0,c.jsx)(Je,(0,o.A)({timeout:ce.transitions.duration.shorter},t,nt,{children:(0,c.jsxs)(Ze,(0,o.A)({},rt,{children:[re,P?(0,c.jsx)(et,(0,o.A)({},ot,{ref:me})):null]}))}))}}))]})}));var $r=n(4536),zr=n(5896),Lr=n(1707),_r=n(5844),Fr=n(7868),Dr=n(3940),Wr=n(3468);const Br=["onChange","maxRows","minRows","style","value"];function Vr(e){return parseInt(e,10)||0}const Hr={visibility:"hidden",position:"absolute",overflow:"hidden",height:0,top:0,left:0,transform:"translateZ(0)"};const Ur=e.forwardRef((function(t,n){const{onChange:r,maxRows:i,minRows:l=1,style:s,value:u}=t,d=(0,a.A)(t,Br),{current:p}=e.useRef(null!=u),f=e.useRef(null),m=(0,Vt.A)(n,f),h=e.useRef(null),g=e.useRef(null),v=e.useCallback((()=>{const e=f.current,n=(0,Dr.A)(e).getComputedStyle(e);if("0px"===n.width)return{outerHeightStyle:0,overflowing:!1};const r=g.current;r.style.width=n.width,r.value=e.value||t.placeholder||"x","\n"===r.value.slice(-1)&&(r.value+=" ");const o=n.boxSizing,a=Vr(n.paddingBottom)+Vr(n.paddingTop),s=Vr(n.borderBottomWidth)+Vr(n.borderTopWidth),c=r.scrollHeight;r.value="x";const u=r.scrollHeight;let d=c;l&&(d=Math.max(Number(l)*u,d)),i&&(d=Math.min(Number(i)*u,d)),d=Math.max(d,u);return{outerHeightStyle:d+("border-box"===o?a+s:0),overflowing:Math.abs(d-c)<=1}}),[i,l,t.placeholder]),y=e.useCallback((()=>{const e=v();if(void 0===(t=e)||null===t||0===Object.keys(t).length||0===t.outerHeightStyle&&!t.overflowing)return;var t;const n=e.outerHeightStyle,r=f.current;h.current!==n&&(h.current=n,r.style.height=`${n}px`),r.style.overflow=e.overflowing?"hidden":""}),[v]);(0,Ht.A)((()=>{const e=()=>{y()};let t;const n=(0,Wr.A)(e),r=f.current,o=(0,Dr.A)(r);let a;return o.addEventListener("resize",n),"undefined"!==typeof ResizeObserver&&(a=new ResizeObserver(e),a.observe(r)),()=>{n.clear(),cancelAnimationFrame(t),o.removeEventListener("resize",n),a&&a.disconnect()}}),[v,y]),(0,Ht.A)((()=>{y()}));return(0,c.jsxs)(e.Fragment,{children:[(0,c.jsx)("textarea",(0,o.A)({value:u,onChange:e=>{p||y(),r&&r(e)},ref:m,rows:l,style:s},d)),(0,c.jsx)("textarea",{"aria-hidden":!0,className:t.className,readOnly:!0,ref:g,tabIndex:-1,style:(0,o.A)({},Hr,s,{paddingTop:0,paddingBottom:0})})]})}));function Xr(e){let{props:t,states:n,muiFormControl:r}=e;return n.reduce(((e,n)=>(e[n]=t[n],r&&"undefined"===typeof t[n]&&(e[n]=r[n]),e)),{})}const Kr=e.createContext(void 0);function qr(){return e.useContext(Kr)}var Gr=n(5013);function Qr(e){return null!=e&&!(Array.isArray(e)&&0===e.length)}function Yr(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e&&(Qr(e.value)&&""!==e.value||t&&Qr(e.defaultValue)&&""!==e.defaultValue)}function Jr(e){return(0,Ae.Ay)("MuiInputBase",e)}const Zr=(0,W.A)("MuiInputBase",["root","formControl","focused","disabled","adornedStart","adornedEnd","error","sizeSmall","multiline","colorSecondary","fullWidth","hiddenLabel","readOnly","input","inputSizeSmall","inputMultiline","inputTypeSearch","inputAdornedStart","inputAdornedEnd","inputHiddenLabel"]),eo=["aria-describedby","autoComplete","autoFocus","className","color","components","componentsProps","defaultValue","disabled","disableInjectingGlobalStyles","endAdornment","error","fullWidth","id","inputComponent","inputProps","inputRef","margin","maxRows","minRows","multiline","name","onBlur","onChange","onClick","onFocus","onKeyDown","onKeyUp","placeholder","readOnly","renderSuffix","rows","size","slotProps","slots","startAdornment","type","value"],to=(e,t)=>{const{ownerState:n}=e;return[t.root,n.formControl&&t.formControl,n.startAdornment&&t.adornedStart,n.endAdornment&&t.adornedEnd,n.error&&t.error,"small"===n.size&&t.sizeSmall,n.multiline&&t.multiline,n.color&&t[`color${(0,Pe.A)(n.color)}`],n.fullWidth&&t.fullWidth,n.hiddenLabel&&t.hiddenLabel]},no=(e,t)=>{const{ownerState:n}=e;return[t.input,"small"===n.size&&t.inputSizeSmall,n.multiline&&t.inputMultiline,"search"===n.type&&t.inputTypeSearch,n.startAdornment&&t.inputAdornedStart,n.endAdornment&&t.inputAdornedEnd,n.hiddenLabel&&t.inputHiddenLabel]},ro=(0,K.Ay)("div",{name:"MuiInputBase",slot:"Root",overridesResolver:to})((e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({},t.typography.body1,{color:(t.vars||t).palette.text.primary,lineHeight:"1.4375em",boxSizing:"border-box",position:"relative",cursor:"text",display:"inline-flex",alignItems:"center",[`&.${Zr.disabled}`]:{color:(t.vars||t).palette.text.disabled,cursor:"default"}},n.multiline&&(0,o.A)({padding:"4px 0 5px"},"small"===n.size&&{paddingTop:1}),n.fullWidth&&{width:"100%"})})),oo=(0,K.Ay)("input",{name:"MuiInputBase",slot:"Input",overridesResolver:no})((e=>{let{theme:t,ownerState:n}=e;const r="light"===t.palette.mode,a=(0,o.A)({color:"currentColor"},t.vars?{opacity:t.vars.opacity.inputPlaceholder}:{opacity:r?.42:.5},{transition:t.transitions.create("opacity",{duration:t.transitions.duration.shorter})}),i={opacity:"0 !important"},l=t.vars?{opacity:t.vars.opacity.inputPlaceholder}:{opacity:r?.42:.5};return(0,o.A)({font:"inherit",letterSpacing:"inherit",color:"currentColor",padding:"4px 0 5px",border:0,boxSizing:"content-box",background:"none",height:"1.4375em",margin:0,WebkitTapHighlightColor:"transparent",display:"block",minWidth:0,width:"100%",animationName:"mui-auto-fill-cancel",animationDuration:"10ms","&::-webkit-input-placeholder":a,"&::-moz-placeholder":a,"&:-ms-input-placeholder":a,"&::-ms-input-placeholder":a,"&:focus":{outline:0},"&:invalid":{boxShadow:"none"},"&::-webkit-search-decoration":{WebkitAppearance:"none"},[`label[data-shrink=false] + .${Zr.formControl} &`]:{"&::-webkit-input-placeholder":i,"&::-moz-placeholder":i,"&:-ms-input-placeholder":i,"&::-ms-input-placeholder":i,"&:focus::-webkit-input-placeholder":l,"&:focus::-moz-placeholder":l,"&:focus:-ms-input-placeholder":l,"&:focus::-ms-input-placeholder":l},[`&.${Zr.disabled}`]:{opacity:1,WebkitTextFillColor:(t.vars||t).palette.text.disabled},"&:-webkit-autofill":{animationDuration:"5000s",animationName:"mui-auto-fill"}},"small"===n.size&&{paddingTop:1},n.multiline&&{height:"auto",resize:"none",padding:0,paddingTop:0},"search"===n.type&&{MozAppearance:"textfield"})})),ao=(0,c.jsx)(T,{styles:{"@keyframes mui-auto-fill":{from:{display:"block"}},"@keyframes mui-auto-fill-cancel":{from:{display:"block"}}}}),io=e.forwardRef((function(t,n){var r;const i=(0,S.b)({props:t,name:"MuiInputBase"}),{"aria-describedby":l,autoComplete:s,autoFocus:u,className:d,components:p={},componentsProps:f={},defaultValue:m,disabled:h,disableInjectingGlobalStyles:g,endAdornment:v,fullWidth:y=!1,id:b,inputComponent:x="input",inputProps:w={},inputRef:A,maxRows:k,minRows:C,multiline:E=!1,name:R,onBlur:P,onChange:M,onClick:O,onFocus:T,onKeyDown:j,onKeyUp:I,placeholder:N,readOnly:z,renderSuffix:L,rows:_,slotProps:F={},slots:D={},startAdornment:W,type:B="text",value:V}=i,H=(0,a.A)(i,eo),X=null!=w.value?w.value:V,{current:K}=e.useRef(null!=X),G=e.useRef(),Q=e.useCallback((e=>{0}),[]),Y=(0,q.A)(G,A,w.ref,Q),[J,Z]=e.useState(!1),ee=qr();const te=Xr({props:i,muiFormControl:ee,states:["color","disabled","error","hiddenLabel","size","required","filled"]});te.focused=ee?ee.focused:J,e.useEffect((()=>{!ee&&h&&J&&(Z(!1),P&&P())}),[ee,h,J,P]);const ne=ee&&ee.onFilled,re=ee&&ee.onEmpty,oe=e.useCallback((e=>{Yr(e)?ne&&ne():re&&re()}),[ne,re]);(0,Gr.A)((()=>{K&&oe({value:X})}),[X,oe,K]);e.useEffect((()=>{oe(G.current)}),[]);let ae=x,ie=w;E&&"input"===ae&&(ie=_?(0,o.A)({type:void 0,minRows:_,maxRows:_},ie):(0,o.A)({type:void 0,maxRows:k,minRows:C},ie),ae=Ur);e.useEffect((()=>{ee&&ee.setAdornedStart(Boolean(W))}),[ee,W]);const le=(0,o.A)({},i,{color:te.color||"primary",disabled:te.disabled,endAdornment:v,error:te.error,focused:te.focused,formControl:ee,fullWidth:y,hiddenLabel:te.hiddenLabel,multiline:E,size:te.size,startAdornment:W,type:B}),se=(e=>{const{classes:t,color:n,disabled:r,error:o,endAdornment:a,focused:i,formControl:l,fullWidth:s,hiddenLabel:c,multiline:u,readOnly:d,size:p,startAdornment:f,type:m}=e,h={root:["root",`color${(0,Pe.A)(n)}`,r&&"disabled",o&&"error",s&&"fullWidth",i&&"focused",l&&"formControl",p&&"medium"!==p&&`size${(0,Pe.A)(p)}`,u&&"multiline",f&&"adornedStart",a&&"adornedEnd",c&&"hiddenLabel",d&&"readOnly"],input:["input",r&&"disabled","search"===m&&"inputTypeSearch",u&&"inputMultiline","small"===p&&"inputSizeSmall",c&&"inputHiddenLabel",f&&"inputAdornedStart",a&&"inputAdornedEnd",d&&"readOnly"]};return(0,U.A)(h,Jr,t)})(le),ce=D.root||p.Root||ro,ue=F.root||f.root||{},de=D.input||p.Input||oo;return ie=(0,o.A)({},ie,null!=(r=F.input)?r:f.input),(0,c.jsxs)(e.Fragment,{children:[!g&&ao,(0,c.jsxs)(ce,(0,o.A)({},ue,!wt(ce)&&{ownerState:(0,o.A)({},le,ue.ownerState)},{ref:n,onClick:e=>{G.current&&e.currentTarget===e.target&&G.current.focus(),O&&O(e)}},H,{className:(0,$.A)(se.root,ue.className,d,z&&"MuiInputBase-readOnly"),children:[W,(0,c.jsx)(Kr.Provider,{value:null,children:(0,c.jsx)(de,(0,o.A)({ownerState:le,"aria-invalid":te.error,"aria-describedby":l,autoComplete:s,autoFocus:u,defaultValue:m,disabled:te.disabled,id:b,onAnimationStart:e=>{oe("mui-auto-fill-cancel"===e.animationName?G.current:{value:"x"})},name:R,placeholder:N,readOnly:z,required:te.required,rows:_,value:X,onKeyDown:j,onKeyUp:I,type:B},ie,!wt(de)&&{as:ae,ownerState:(0,o.A)({},le,ie.ownerState)},{ref:Y,className:(0,$.A)(se.input,ie.className,z&&"MuiInputBase-readOnly"),onBlur:e=>{P&&P(e),w.onBlur&&w.onBlur(e),ee&&ee.onBlur?ee.onBlur(e):Z(!1)},onChange:function(e){if(!K){const t=e.target||G.current;if(null==t)throw new Error((0,Fr.A)(1));oe({value:t.value})}for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];w.onChange&&w.onChange(e,...n),M&&M(e,...n)},onFocus:e=>{te.disabled?e.stopPropagation():(T&&T(e),w.onFocus&&w.onFocus(e),ee&&ee.onFocus?ee.onFocus(e):Z(!0))}}))}),v,L?L((0,o.A)({},te,{startAdornment:W})):null]}))]})}));function lo(e){return(0,Ae.Ay)("MuiInput",e)}const so=(0,o.A)({},Zr,(0,W.A)("MuiInput",["root","underline","input"])),co=["disableUnderline","components","componentsProps","fullWidth","inputComponent","multiline","slotProps","slots","type"],uo=(0,K.Ay)(ro,{shouldForwardProp:e=>(0,$e.A)(e)||"classes"===e,name:"MuiInput",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[...to(e,t),!n.disableUnderline&&t.underline]}})((e=>{let{theme:t,ownerState:n}=e;let r="light"===t.palette.mode?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)";return t.vars&&(r=`rgba(${t.vars.palette.common.onBackgroundChannel} / ${t.vars.opacity.inputUnderline})`),(0,o.A)({position:"relative"},n.formControl&&{"label + &":{marginTop:16}},!n.disableUnderline&&{"&::after":{borderBottom:`2px solid ${(t.vars||t).palette[n.color].main}`,left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:t.transitions.create("transform",{duration:t.transitions.duration.shorter,easing:t.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${so.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${so.error}`]:{"&::before, &::after":{borderBottomColor:(t.vars||t).palette.error.main}},"&::before":{borderBottom:`1px solid ${r}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:t.transitions.create("border-bottom-color",{duration:t.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${so.disabled}, .${so.error}):before`]:{borderBottom:`2px solid ${(t.vars||t).palette.text.primary}`,"@media (hover: none)":{borderBottom:`1px solid ${r}`}},[`&.${so.disabled}:before`]:{borderBottomStyle:"dotted"}})})),po=(0,K.Ay)(oo,{name:"MuiInput",slot:"Input",overridesResolver:no})({}),fo=e.forwardRef((function(e,t){var n,r,i,l;const s=(0,S.b)({props:e,name:"MuiInput"}),{disableUnderline:u,components:d={},componentsProps:p,fullWidth:f=!1,inputComponent:m="input",multiline:h=!1,slotProps:g,slots:v={},type:y="text"}=s,b=(0,a.A)(s,co),x=(e=>{const{classes:t,disableUnderline:n}=e,r={root:["root",!n&&"underline"],input:["input"]},a=(0,U.A)(r,lo,t);return(0,o.A)({},t,a)})(s),w={root:{ownerState:{disableUnderline:u}}},A=(null!=g?g:p)?(0,qe.A)(null!=g?g:p,w):w,k=null!=(n=null!=(r=v.root)?r:d.Root)?n:uo,C=null!=(i=null!=(l=v.input)?l:d.Input)?i:po;return(0,c.jsx)(io,(0,o.A)({slots:{root:k,input:C},slotProps:A,fullWidth:f,inputComponent:m,multiline:h,ref:t,type:y},b,{classes:x}))}));fo.muiName="Input";const mo=fo;function ho(e){return(0,Ae.Ay)("MuiFilledInput",e)}const go=(0,o.A)({},Zr,(0,W.A)("MuiFilledInput",["root","underline","input"])),vo=["disableUnderline","components","componentsProps","fullWidth","hiddenLabel","inputComponent","multiline","slotProps","slots","type"],yo=(0,K.Ay)(ro,{shouldForwardProp:e=>(0,$e.A)(e)||"classes"===e,name:"MuiFilledInput",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[...to(e,t),!n.disableUnderline&&t.underline]}})((e=>{let{theme:t,ownerState:n}=e;var r;const a="light"===t.palette.mode,i=a?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)",l=a?"rgba(0, 0, 0, 0.06)":"rgba(255, 255, 255, 0.09)",s=a?"rgba(0, 0, 0, 0.09)":"rgba(255, 255, 255, 0.13)",c=a?"rgba(0, 0, 0, 0.12)":"rgba(255, 255, 255, 0.12)";return(0,o.A)({position:"relative",backgroundColor:t.vars?t.vars.palette.FilledInput.bg:l,borderTopLeftRadius:(t.vars||t).shape.borderRadius,borderTopRightRadius:(t.vars||t).shape.borderRadius,transition:t.transitions.create("background-color",{duration:t.transitions.duration.shorter,easing:t.transitions.easing.easeOut}),"&:hover":{backgroundColor:t.vars?t.vars.palette.FilledInput.hoverBg:s,"@media (hover: none)":{backgroundColor:t.vars?t.vars.palette.FilledInput.bg:l}},[`&.${go.focused}`]:{backgroundColor:t.vars?t.vars.palette.FilledInput.bg:l},[`&.${go.disabled}`]:{backgroundColor:t.vars?t.vars.palette.FilledInput.disabledBg:c}},!n.disableUnderline&&{"&::after":{borderBottom:`2px solid ${null==(r=(t.vars||t).palette[n.color||"primary"])?void 0:r.main}`,left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:t.transitions.create("transform",{duration:t.transitions.duration.shorter,easing:t.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${go.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${go.error}`]:{"&::before, &::after":{borderBottomColor:(t.vars||t).palette.error.main}},"&::before":{borderBottom:`1px solid ${t.vars?`rgba(${t.vars.palette.common.onBackgroundChannel} / ${t.vars.opacity.inputUnderline})`:i}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:t.transitions.create("border-bottom-color",{duration:t.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${go.disabled}, .${go.error}):before`]:{borderBottom:`1px solid ${(t.vars||t).palette.text.primary}`},[`&.${go.disabled}:before`]:{borderBottomStyle:"dotted"}},n.startAdornment&&{paddingLeft:12},n.endAdornment&&{paddingRight:12},n.multiline&&(0,o.A)({padding:"25px 12px 8px"},"small"===n.size&&{paddingTop:21,paddingBottom:4},n.hiddenLabel&&{paddingTop:16,paddingBottom:17},n.hiddenLabel&&"small"===n.size&&{paddingTop:8,paddingBottom:9}))})),bo=(0,K.Ay)(oo,{name:"MuiFilledInput",slot:"Input",overridesResolver:no})((e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({paddingTop:25,paddingRight:12,paddingBottom:8,paddingLeft:12},!t.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:"light"===t.palette.mode?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:"light"===t.palette.mode?null:"#fff",caretColor:"light"===t.palette.mode?null:"#fff",borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"}},t.vars&&{"&:-webkit-autofill":{borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"},[t.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},"small"===n.size&&{paddingTop:21,paddingBottom:4},n.hiddenLabel&&{paddingTop:16,paddingBottom:17},n.startAdornment&&{paddingLeft:0},n.endAdornment&&{paddingRight:0},n.hiddenLabel&&"small"===n.size&&{paddingTop:8,paddingBottom:9},n.multiline&&{paddingTop:0,paddingBottom:0,paddingLeft:0,paddingRight:0})})),xo=e.forwardRef((function(e,t){var n,r,i,l;const s=(0,S.b)({props:e,name:"MuiFilledInput"}),{components:u={},componentsProps:d,fullWidth:p=!1,inputComponent:f="input",multiline:m=!1,slotProps:h,slots:g={},type:v="text"}=s,y=(0,a.A)(s,vo),b=(0,o.A)({},s,{fullWidth:p,inputComponent:f,multiline:m,type:v}),x=(e=>{const{classes:t,disableUnderline:n}=e,r={root:["root",!n&&"underline"],input:["input"]},a=(0,U.A)(r,ho,t);return(0,o.A)({},t,a)})(s),w={root:{ownerState:b},input:{ownerState:b}},A=(null!=h?h:d)?(0,qe.A)(w,null!=h?h:d):w,k=null!=(n=null!=(r=g.root)?r:u.Root)?n:yo,C=null!=(i=null!=(l=g.input)?l:u.Input)?i:bo;return(0,c.jsx)(io,(0,o.A)({slots:{root:k,input:C},componentsProps:A,fullWidth:p,inputComponent:f,multiline:m,ref:t,type:v},y,{classes:x}))}));xo.muiName="Input";const wo=xo;var Ao;const ko=["children","classes","className","label","notched"],So=(0,K.Ay)("fieldset",{shouldForwardProp:$e.A})({textAlign:"left",position:"absolute",bottom:0,right:0,top:-5,left:0,margin:0,padding:"0 8px",pointerEvents:"none",borderRadius:"inherit",borderStyle:"solid",borderWidth:1,overflow:"hidden",minWidth:"0%"}),Co=(0,K.Ay)("legend",{shouldForwardProp:$e.A})((e=>{let{ownerState:t,theme:n}=e;return(0,o.A)({float:"unset",width:"auto",overflow:"hidden"},!t.withLabel&&{padding:0,lineHeight:"11px",transition:n.transitions.create("width",{duration:150,easing:n.transitions.easing.easeOut})},t.withLabel&&(0,o.A)({display:"block",padding:0,height:11,fontSize:"0.75em",visibility:"hidden",maxWidth:.01,transition:n.transitions.create("max-width",{duration:50,easing:n.transitions.easing.easeOut}),whiteSpace:"nowrap","& > span":{paddingLeft:5,paddingRight:5,display:"inline-block",opacity:0,visibility:"visible"}},t.notched&&{maxWidth:"100%",transition:n.transitions.create("max-width",{duration:100,easing:n.transitions.easing.easeOut,delay:50})}))}));function Eo(e){return(0,Ae.Ay)("MuiOutlinedInput",e)}const Ro=(0,o.A)({},Zr,(0,W.A)("MuiOutlinedInput",["root","notchedOutline","input"])),Po=["components","fullWidth","inputComponent","label","multiline","notched","slots","type"],Mo=(0,K.Ay)(ro,{shouldForwardProp:e=>(0,$e.A)(e)||"classes"===e,name:"MuiOutlinedInput",slot:"Root",overridesResolver:to})((e=>{let{theme:t,ownerState:n}=e;const r="light"===t.palette.mode?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return(0,o.A)({position:"relative",borderRadius:(t.vars||t).shape.borderRadius,[`&:hover .${Ro.notchedOutline}`]:{borderColor:(t.vars||t).palette.text.primary},"@media (hover: none)":{[`&:hover .${Ro.notchedOutline}`]:{borderColor:t.vars?`rgba(${t.vars.palette.common.onBackgroundChannel} / 0.23)`:r}},[`&.${Ro.focused} .${Ro.notchedOutline}`]:{borderColor:(t.vars||t).palette[n.color].main,borderWidth:2},[`&.${Ro.error} .${Ro.notchedOutline}`]:{borderColor:(t.vars||t).palette.error.main},[`&.${Ro.disabled} .${Ro.notchedOutline}`]:{borderColor:(t.vars||t).palette.action.disabled}},n.startAdornment&&{paddingLeft:14},n.endAdornment&&{paddingRight:14},n.multiline&&(0,o.A)({padding:"16.5px 14px"},"small"===n.size&&{padding:"8.5px 14px"}))})),Oo=(0,K.Ay)((function(e){const{className:t,label:n,notched:r}=e,i=(0,a.A)(e,ko),l=null!=n&&""!==n,s=(0,o.A)({},e,{notched:r,withLabel:l});return(0,c.jsx)(So,(0,o.A)({"aria-hidden":!0,className:t,ownerState:s},i,{children:(0,c.jsx)(Co,{ownerState:s,children:l?(0,c.jsx)("span",{children:n}):Ao||(Ao=(0,c.jsx)("span",{className:"notranslate",children:"\u200b"}))})}))}),{name:"MuiOutlinedInput",slot:"NotchedOutline",overridesResolver:(e,t)=>t.notchedOutline})((e=>{let{theme:t}=e;const n="light"===t.palette.mode?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{borderColor:t.vars?`rgba(${t.vars.palette.common.onBackgroundChannel} / 0.23)`:n}})),To=(0,K.Ay)(oo,{name:"MuiOutlinedInput",slot:"Input",overridesResolver:no})((e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({padding:"16.5px 14px"},!t.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:"light"===t.palette.mode?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:"light"===t.palette.mode?null:"#fff",caretColor:"light"===t.palette.mode?null:"#fff",borderRadius:"inherit"}},t.vars&&{"&:-webkit-autofill":{borderRadius:"inherit"},[t.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},"small"===n.size&&{padding:"8.5px 14px"},n.multiline&&{padding:0},n.startAdornment&&{paddingLeft:0},n.endAdornment&&{paddingRight:0})})),jo=e.forwardRef((function(t,n){var r,i,l,s,u;const d=(0,S.b)({props:t,name:"MuiOutlinedInput"}),{components:p={},fullWidth:f=!1,inputComponent:m="input",label:h,multiline:g=!1,notched:v,slots:y={},type:b="text"}=d,x=(0,a.A)(d,Po),w=(e=>{const{classes:t}=e,n=(0,U.A)({root:["root"],notchedOutline:["notchedOutline"],input:["input"]},Eo,t);return(0,o.A)({},t,n)})(d),A=qr(),k=Xr({props:d,muiFormControl:A,states:["color","disabled","error","focused","hiddenLabel","size","required"]}),C=(0,o.A)({},d,{color:k.color||"primary",disabled:k.disabled,error:k.error,focused:k.focused,formControl:A,fullWidth:f,hiddenLabel:k.hiddenLabel,multiline:g,size:k.size,type:b}),E=null!=(r=null!=(i=y.root)?i:p.Root)?r:Mo,R=null!=(l=null!=(s=y.input)?s:p.Input)?l:To;return(0,c.jsx)(io,(0,o.A)({slots:{root:E,input:R},renderSuffix:t=>(0,c.jsx)(Oo,{ownerState:C,className:w.notchedOutline,label:null!=h&&""!==h&&k.required?u||(u=(0,c.jsxs)(e.Fragment,{children:[h,"\u2009","*"]})):h,notched:"undefined"!==typeof v?v:Boolean(t.startAdornment||t.filled||t.focused)}),fullWidth:f,inputComponent:m,multiline:g,ref:n,type:b},x,{classes:(0,o.A)({},w,{notchedOutline:null})}))}));jo.muiName="Input";const Io=jo;function No(e){return(0,Ae.Ay)("MuiFormLabel",e)}const $o=(0,W.A)("MuiFormLabel",["root","colorSecondary","focused","disabled","error","filled","required","asterisk"]),zo=["children","className","color","component","disabled","error","filled","focused","required"],Lo=(0,K.Ay)("label",{name:"MuiFormLabel",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:n}=e;return(0,o.A)({},t.root,"secondary"===n.color&&t.colorSecondary,n.filled&&t.filled)}})((e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({color:(t.vars||t).palette.text.secondary},t.typography.body1,{lineHeight:"1.4375em",padding:0,position:"relative",[`&.${$o.focused}`]:{color:(t.vars||t).palette[n.color].main},[`&.${$o.disabled}`]:{color:(t.vars||t).palette.text.disabled},[`&.${$o.error}`]:{color:(t.vars||t).palette.error.main}})})),_o=(0,K.Ay)("span",{name:"MuiFormLabel",slot:"Asterisk",overridesResolver:(e,t)=>t.asterisk})((e=>{let{theme:t}=e;return{[`&.${$o.error}`]:{color:(t.vars||t).palette.error.main}}})),Fo=e.forwardRef((function(e,t){const n=(0,S.b)({props:e,name:"MuiFormLabel"}),{children:r,className:i,component:l="label"}=n,s=(0,a.A)(n,zo),u=Xr({props:n,muiFormControl:qr(),states:["color","required","focused","disabled","error","filled"]}),d=(0,o.A)({},n,{color:u.color||"primary",component:l,disabled:u.disabled,error:u.error,filled:u.filled,focused:u.focused,required:u.required}),p=(e=>{const{classes:t,color:n,focused:r,disabled:o,error:a,filled:i,required:l}=e,s={root:["root",`color${(0,Pe.A)(n)}`,o&&"disabled",a&&"error",i&&"filled",r&&"focused",l&&"required"],asterisk:["asterisk",a&&"error"]};return(0,U.A)(s,No,t)})(d);return(0,c.jsxs)(Lo,(0,o.A)({as:l,ownerState:d,className:(0,$.A)(p.root,i),ref:t},s,{children:[r,u.required&&(0,c.jsxs)(_o,{ownerState:d,"aria-hidden":!0,className:p.asterisk,children:["\u2009","*"]})]}))}));function Do(e){return(0,Ae.Ay)("MuiInputLabel",e)}(0,W.A)("MuiInputLabel",["root","focused","disabled","error","required","asterisk","formControl","sizeSmall","shrink","animated","standard","filled","outlined"]);const Wo=["disableAnimation","margin","shrink","variant","className"],Bo=(0,K.Ay)(Fo,{shouldForwardProp:e=>(0,$e.A)(e)||"classes"===e,name:"MuiInputLabel",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[{[`& .${$o.asterisk}`]:t.asterisk},t.root,n.formControl&&t.formControl,"small"===n.size&&t.sizeSmall,n.shrink&&t.shrink,!n.disableAnimation&&t.animated,n.focused&&t.focused,t[n.variant]]}})((e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({display:"block",transformOrigin:"top left",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},n.formControl&&{position:"absolute",left:0,top:0,transform:"translate(0, 20px) scale(1)"},"small"===n.size&&{transform:"translate(0, 17px) scale(1)"},n.shrink&&{transform:"translate(0, -1.5px) scale(0.75)",transformOrigin:"top left",maxWidth:"133%"},!n.disableAnimation&&{transition:t.transitions.create(["color","transform","max-width"],{duration:t.transitions.duration.shorter,easing:t.transitions.easing.easeOut})},"filled"===n.variant&&(0,o.A)({zIndex:1,pointerEvents:"none",transform:"translate(12px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"},"small"===n.size&&{transform:"translate(12px, 13px) scale(1)"},n.shrink&&(0,o.A)({userSelect:"none",pointerEvents:"auto",transform:"translate(12px, 7px) scale(0.75)",maxWidth:"calc(133% - 24px)"},"small"===n.size&&{transform:"translate(12px, 4px) scale(0.75)"})),"outlined"===n.variant&&(0,o.A)({zIndex:1,pointerEvents:"none",transform:"translate(14px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"},"small"===n.size&&{transform:"translate(14px, 9px) scale(1)"},n.shrink&&{userSelect:"none",pointerEvents:"auto",maxWidth:"calc(133% - 32px)",transform:"translate(14px, -9px) scale(0.75)"}))})),Vo=e.forwardRef((function(e,t){const n=(0,S.b)({name:"MuiInputLabel",props:e}),{disableAnimation:r=!1,shrink:i,className:l}=n,s=(0,a.A)(n,Wo),u=qr();let d=i;"undefined"===typeof d&&u&&(d=u.filled||u.focused||u.adornedStart);const p=Xr({props:n,muiFormControl:u,states:["size","variant","required","focused"]}),f=(0,o.A)({},n,{disableAnimation:r,formControl:u,shrink:d,size:p.size,variant:p.variant,required:p.required,focused:p.focused}),m=(e=>{const{classes:t,formControl:n,size:r,shrink:a,disableAnimation:i,variant:l,required:s}=e,c={root:["root",n&&"formControl",!i&&"animated",a&&"shrink",r&&"normal"!==r&&`size${(0,Pe.A)(r)}`,l],asterisk:[s&&"asterisk"]},u=(0,U.A)(c,Do,t);return(0,o.A)({},t,u)})(f);return(0,c.jsx)(Bo,(0,o.A)({"data-shrink":d,ownerState:f,ref:t,className:(0,$.A)(m.root,l)},s,{classes:m}))}));var Ho=n(7328);function Uo(e){return(0,Ae.Ay)("MuiFormControl",e)}(0,W.A)("MuiFormControl",["root","marginNone","marginNormal","marginDense","fullWidth","disabled"]);const Xo=["children","className","color","component","disabled","error","focused","fullWidth","hiddenLabel","margin","required","size","variant"],Ko=(0,K.Ay)("div",{name:"MuiFormControl",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:n}=e;return(0,o.A)({},t.root,t[`margin${(0,Pe.A)(n.margin)}`],n.fullWidth&&t.fullWidth)}})((e=>{let{ownerState:t}=e;return(0,o.A)({display:"inline-flex",flexDirection:"column",position:"relative",minWidth:0,padding:0,margin:0,border:0,verticalAlign:"top"},"normal"===t.margin&&{marginTop:16,marginBottom:8},"dense"===t.margin&&{marginTop:8,marginBottom:4},t.fullWidth&&{width:"100%"})})),qo=e.forwardRef((function(t,n){const r=(0,S.b)({props:t,name:"MuiFormControl"}),{children:i,className:l,color:s="primary",component:u="div",disabled:d=!1,error:p=!1,focused:f,fullWidth:m=!1,hiddenLabel:h=!1,margin:g="none",required:v=!1,size:y="medium",variant:b="outlined"}=r,x=(0,a.A)(r,Xo),w=(0,o.A)({},r,{color:s,component:u,disabled:d,error:p,fullWidth:m,hiddenLabel:h,margin:g,required:v,size:y,variant:b}),A=(e=>{const{classes:t,margin:n,fullWidth:r}=e,o={root:["root","none"!==n&&`margin${(0,Pe.A)(n)}`,r&&"fullWidth"]};return(0,U.A)(o,Uo,t)})(w),[k,C]=e.useState((()=>{let t=!1;return i&&e.Children.forEach(i,(e=>{if(!(0,Ho.A)(e,["Input","Select"]))return;const n=(0,Ho.A)(e,["Select"])?e.props.input:e;n&&n.props.startAdornment&&(t=!0)})),t})),[E,R]=e.useState((()=>{let t=!1;return i&&e.Children.forEach(i,(e=>{(0,Ho.A)(e,["Input","Select"])&&(Yr(e.props,!0)||Yr(e.props.inputProps,!0))&&(t=!0)})),t})),[P,M]=e.useState(!1);d&&P&&M(!1);const O=void 0===f||d?P:f;let T;const j=e.useMemo((()=>({adornedStart:k,setAdornedStart:C,color:s,disabled:d,error:p,filled:E,focused:O,fullWidth:m,hiddenLabel:h,size:y,onBlur:()=>{M(!1)},onEmpty:()=>{R(!1)},onFilled:()=>{R(!0)},onFocus:()=>{M(!0)},registerEffect:T,required:v,variant:b})),[k,s,d,p,E,O,m,h,T,v,y,b]);return(0,c.jsx)(Kr.Provider,{value:j,children:(0,c.jsx)(Ko,(0,o.A)({as:u,ownerState:w,className:(0,$.A)(A.root,l),ref:n},x,{children:i}))})}));function Go(e){return(0,Ae.Ay)("MuiFormHelperText",e)}const Qo=(0,W.A)("MuiFormHelperText",["root","error","disabled","sizeSmall","sizeMedium","contained","focused","filled","required"]);var Yo;const Jo=["children","className","component","disabled","error","filled","focused","margin","required","variant"],Zo=(0,K.Ay)("p",{name:"MuiFormHelperText",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.size&&t[`size${(0,Pe.A)(n.size)}`],n.contained&&t.contained,n.filled&&t.filled]}})((e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({color:(t.vars||t).palette.text.secondary},t.typography.caption,{textAlign:"left",marginTop:3,marginRight:0,marginBottom:0,marginLeft:0,[`&.${Qo.disabled}`]:{color:(t.vars||t).palette.text.disabled},[`&.${Qo.error}`]:{color:(t.vars||t).palette.error.main}},"small"===n.size&&{marginTop:4},n.contained&&{marginLeft:14,marginRight:14})})),ea=e.forwardRef((function(e,t){const n=(0,S.b)({props:e,name:"MuiFormHelperText"}),{children:r,className:i,component:l="p"}=n,s=(0,a.A)(n,Jo),u=Xr({props:n,muiFormControl:qr(),states:["variant","size","disabled","error","filled","focused","required"]}),d=(0,o.A)({},n,{component:l,contained:"filled"===u.variant||"outlined"===u.variant,variant:u.variant,size:u.size,disabled:u.disabled,error:u.error,filled:u.filled,focused:u.focused,required:u.required}),p=(e=>{const{classes:t,contained:n,size:r,disabled:o,error:a,filled:i,focused:l,required:s}=e,c={root:["root",o&&"disabled",a&&"error",r&&`size${(0,Pe.A)(r)}`,n&&"contained",l&&"focused",i&&"filled",s&&"required"]};return(0,U.A)(c,Go,t)})(d);return(0,c.jsx)(Zo,(0,o.A)({as:l,ownerState:d,className:(0,$.A)(p.root,i),ref:t},s,{children:" "===r?Yo||(Yo=(0,c.jsx)("span",{className:"notranslate",children:"\u200b"})):r}))}));n(2086);var ta=n(2427);const na=e.createContext({});function ra(e){return(0,Ae.Ay)("MuiList",e)}(0,W.A)("MuiList",["root","padding","dense","subheader"]);const oa=["children","className","component","dense","disablePadding","subheader"],aa=(0,K.Ay)("ul",{name:"MuiList",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,!n.disablePadding&&t.padding,n.dense&&t.dense,n.subheader&&t.subheader]}})((e=>{let{ownerState:t}=e;return(0,o.A)({listStyle:"none",margin:0,padding:0,position:"relative"},!t.disablePadding&&{paddingTop:8,paddingBottom:8},t.subheader&&{paddingTop:0})})),ia=e.forwardRef((function(t,n){const r=(0,S.b)({props:t,name:"MuiList"}),{children:i,className:l,component:s="ul",dense:u=!1,disablePadding:d=!1,subheader:p}=r,f=(0,a.A)(r,oa),m=e.useMemo((()=>({dense:u})),[u]),h=(0,o.A)({},r,{component:s,dense:u,disablePadding:d}),g=(e=>{const{classes:t,disablePadding:n,dense:r,subheader:o}=e,a={root:["root",!n&&"padding",r&&"dense",o&&"subheader"]};return(0,U.A)(a,ra,t)})(h);return(0,c.jsx)(na.Provider,{value:m,children:(0,c.jsxs)(aa,(0,o.A)({as:s,className:(0,$.A)(g.root,l),ref:n,ownerState:h},f,{children:[p,i]}))})}));function la(e){const t=e.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}const sa=la,ca=["actions","autoFocus","autoFocusItem","children","className","disabledItemsFocusable","disableListWrap","onKeyDown","variant"];function ua(e,t,n){return e===t?e.firstChild:t&&t.nextElementSibling?t.nextElementSibling:n?null:e.firstChild}function da(e,t,n){return e===t?n?e.firstChild:e.lastChild:t&&t.previousElementSibling?t.previousElementSibling:n?null:e.lastChild}function pa(e,t){if(void 0===t)return!0;let n=e.innerText;return void 0===n&&(n=e.textContent),n=n.trim().toLowerCase(),0!==n.length&&(t.repeating?n[0]===t.keys[0]:0===n.indexOf(t.keys.join("")))}function fa(e,t,n,r,o,a){let i=!1,l=o(e,t,!!t&&n);for(;l;){if(l===e.firstChild){if(i)return!1;i=!0}const t=!r&&(l.disabled||"true"===l.getAttribute("aria-disabled"));if(l.hasAttribute("tabindex")&&pa(l,a)&&!t)return l.focus(),!0;l=o(e,l,n)}return!1}const ma=e.forwardRef((function(t,n){const{actions:r,autoFocus:i=!1,autoFocusItem:l=!1,children:s,className:u,disabledItemsFocusable:d=!1,disableListWrap:p=!1,onKeyDown:f,variant:m="selectedMenu"}=t,h=(0,a.A)(t,ca),g=e.useRef(null),v=e.useRef({keys:[],repeating:!0,previousKeyMatched:!0,lastTime:null});(0,Gr.A)((()=>{i&&g.current.focus()}),[i]),e.useImperativeHandle(r,(()=>({adjustStyleForScrollbar:(e,t)=>{let{direction:n}=t;const r=!g.current.style.width;if(e.clientHeight<g.current.clientHeight&&r){const t=`${sa((0,ta.A)(e))}px`;g.current.style["rtl"===n?"paddingLeft":"paddingRight"]=t,g.current.style.width=`calc(100% + ${t})`}return g.current}})),[]);const y=(0,q.A)(g,n);let b=-1;e.Children.forEach(s,((t,n)=>{e.isValidElement(t)?(t.props.disabled||("selectedMenu"===m&&t.props.selected||-1===b)&&(b=n),b===n&&(t.props.disabled||t.props.muiSkipListHighlight||t.type.muiSkipListHighlight)&&(b+=1,b>=s.length&&(b=-1))):b===n&&(b+=1,b>=s.length&&(b=-1))}));const x=e.Children.map(s,((t,n)=>{if(n===b){const n={};return l&&(n.autoFocus=!0),void 0===t.props.tabIndex&&"selectedMenu"===m&&(n.tabIndex=0),e.cloneElement(t,n)}return t}));return(0,c.jsx)(ia,(0,o.A)({role:"menu",ref:y,className:u,onKeyDown:e=>{const t=g.current,n=e.key,r=(0,ta.A)(t).activeElement;if("ArrowDown"===n)e.preventDefault(),fa(t,r,p,d,ua);else if("ArrowUp"===n)e.preventDefault(),fa(t,r,p,d,da);else if("Home"===n)e.preventDefault(),fa(t,null,p,d,ua);else if("End"===n)e.preventDefault(),fa(t,null,p,d,da);else if(1===n.length){const o=v.current,a=n.toLowerCase(),i=performance.now();o.keys.length>0&&(i-o.lastTime>500?(o.keys=[],o.repeating=!0,o.previousKeyMatched=!0):o.repeating&&a!==o.keys[0]&&(o.repeating=!1)),o.lastTime=i,o.keys.push(a);const l=r&&!o.repeating&&pa(r,o);o.previousKeyMatched&&(l||fa(t,r,!1,d,ua,o))?e.preventDefault():o.previousKeyMatched=!1}f&&f(e)},tabIndex:i?0:-1},h,{children:x}))}));var ha=n(950),ga=n(6078);const va=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'].join(",");function ya(e){const t=[],n=[];return Array.from(e.querySelectorAll(va)).forEach(((e,r)=>{const o=function(e){const t=parseInt(e.getAttribute("tabindex")||"",10);return Number.isNaN(t)?"true"===e.contentEditable||("AUDIO"===e.nodeName||"VIDEO"===e.nodeName||"DETAILS"===e.nodeName)&&null===e.getAttribute("tabindex")?0:e.tabIndex:t}(e);-1!==o&&function(e){return!(e.disabled||"INPUT"===e.tagName&&"hidden"===e.type||function(e){if("INPUT"!==e.tagName||"radio"!==e.type)return!1;if(!e.name)return!1;const t=t=>e.ownerDocument.querySelector(`input[type="radio"]${t}`);let n=t(`[name="${e.name}"]:checked`);return n||(n=t(`[name="${e.name}"]`)),n!==e}(e))}(e)&&(0===o?t.push(e):n.push({documentOrder:r,tabIndex:o,node:e}))})),n.sort(((e,t)=>e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex)).map((e=>e.node)).concat(t)}function ba(){return!0}const xa=function(t){const{children:n,disableAutoFocus:r=!1,disableEnforceFocus:o=!1,disableRestoreFocus:a=!1,getTabbable:i=ya,isEnabled:l=ba,open:s}=t,u=e.useRef(!1),d=e.useRef(null),p=e.useRef(null),f=e.useRef(null),m=e.useRef(null),h=e.useRef(!1),g=e.useRef(null),v=(0,Vt.A)(n.ref,g),y=e.useRef(null);e.useEffect((()=>{s&&g.current&&(h.current=!r)}),[r,s]),e.useEffect((()=>{if(!s||!g.current)return;const e=(0,Ut.A)(g.current);return g.current.contains(e.activeElement)||(g.current.hasAttribute("tabIndex")||g.current.setAttribute("tabIndex","-1"),h.current&&g.current.focus()),()=>{a||(f.current&&f.current.focus&&(u.current=!0,f.current.focus()),f.current=null)}}),[s]),e.useEffect((()=>{if(!s||!g.current)return;const e=(0,Ut.A)(g.current),t=t=>{y.current=t,!o&&l()&&"Tab"===t.key&&e.activeElement===g.current&&t.shiftKey&&(u.current=!0,p.current&&p.current.focus())},n=()=>{const t=g.current;if(null===t)return;if(!e.hasFocus()||!l()||u.current)return void(u.current=!1);if(t.contains(e.activeElement))return;if(o&&e.activeElement!==d.current&&e.activeElement!==p.current)return;if(e.activeElement!==m.current)m.current=null;else if(null!==m.current)return;if(!h.current)return;let n=[];if(e.activeElement!==d.current&&e.activeElement!==p.current||(n=i(g.current)),n.length>0){var r,a;const e=Boolean((null==(r=y.current)?void 0:r.shiftKey)&&"Tab"===(null==(a=y.current)?void 0:a.key)),t=n[0],o=n[n.length-1];"string"!==typeof t&&"string"!==typeof o&&(e?o.focus():t.focus())}else t.focus()};e.addEventListener("focusin",n),e.addEventListener("keydown",t,!0);const r=setInterval((()=>{e.activeElement&&"BODY"===e.activeElement.tagName&&n()}),50);return()=>{clearInterval(r),e.removeEventListener("focusin",n),e.removeEventListener("keydown",t,!0)}}),[r,o,a,l,s,i]);const b=e=>{null===f.current&&(f.current=e.relatedTarget),h.current=!0};return(0,c.jsxs)(e.Fragment,{children:[(0,c.jsx)("div",{tabIndex:s?0:-1,onFocus:b,ref:d,"data-testid":"sentinelStart"}),e.cloneElement(n,{ref:v,onFocus:e=>{null===f.current&&(f.current=e.relatedTarget),h.current=!0,m.current=e.target;const t=n.props.onFocus;t&&t(e)}}),(0,c.jsx)("div",{tabIndex:s?0:-1,onFocus:b,ref:p,"data-testid":"sentinelEnd"})]})},wa=["addEndListener","appear","children","easing","in","onEnter","onEntered","onEntering","onExit","onExited","onExiting","style","timeout","TransitionComponent"],Aa={entering:{opacity:1},entered:{opacity:1}},ka=e.forwardRef((function(t,n){const r=kt(),i={enter:r.transitions.duration.enteringScreen,exit:r.transitions.duration.leavingScreen},{addEndListener:l,appear:s=!0,children:u,easing:d,in:p,onEnter:f,onEntered:m,onEntering:h,onExit:g,onExited:v,onExiting:y,style:b,timeout:x=i,TransitionComponent:w=It}=t,A=(0,a.A)(t,wa),k=e.useRef(null),S=(0,q.A)(k,u.ref,n),C=e=>t=>{if(e){const n=k.current;void 0===t?e(n):e(n,t)}},E=C(h),R=C(((e,t)=>{Nt(e);const n=$t({style:b,timeout:x,easing:d},{mode:"enter"});e.style.webkitTransition=r.transitions.create("opacity",n),e.style.transition=r.transitions.create("opacity",n),f&&f(e,t)})),P=C(m),M=C(y),O=C((e=>{const t=$t({style:b,timeout:x,easing:d},{mode:"exit"});e.style.webkitTransition=r.transitions.create("opacity",t),e.style.transition=r.transitions.create("opacity",t),g&&g(e)})),T=C(v);return(0,c.jsx)(w,(0,o.A)({appear:s,in:p,nodeRef:k,onEnter:R,onEntered:P,onEntering:E,onExit:O,onExited:T,onExiting:M,addEndListener:e=>{l&&l(k.current,e)},timeout:x},A,{children:(t,n)=>e.cloneElement(u,(0,o.A)({style:(0,o.A)({opacity:0,visibility:"exited"!==t||p?void 0:"hidden"},Aa[t],b,u.props.style),ref:S},n))}))}));function Sa(e){return(0,Ae.Ay)("MuiBackdrop",e)}(0,W.A)("MuiBackdrop",["root","invisible"]);const Ca=["children","className","component","components","componentsProps","invisible","open","slotProps","slots","TransitionComponent","transitionDuration"],Ea=(0,K.Ay)("div",{name:"MuiBackdrop",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.invisible&&t.invisible]}})((e=>{let{ownerState:t}=e;return(0,o.A)({position:"fixed",display:"flex",alignItems:"center",justifyContent:"center",right:0,bottom:0,top:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",WebkitTapHighlightColor:"transparent"},t.invisible&&{backgroundColor:"transparent"})})),Ra=e.forwardRef((function(e,t){var n,r,i;const l=(0,S.b)({props:e,name:"MuiBackdrop"}),{children:s,className:u,component:d="div",components:p={},componentsProps:f={},invisible:m=!1,open:h,slotProps:g={},slots:v={},TransitionComponent:y=ka,transitionDuration:b}=l,x=(0,a.A)(l,Ca),w=(0,o.A)({},l,{component:d,invisible:m}),A=(e=>{const{classes:t,invisible:n}=e,r={root:["root",n&&"invisible"]};return(0,U.A)(r,Sa,t)})(w),k=null!=(n=g.root)?n:f.root;return(0,c.jsx)(y,(0,o.A)({in:h,timeout:b},x,{children:(0,c.jsx)(Ea,(0,o.A)({"aria-hidden":!0},k,{as:null!=(r=null!=(i=v.root)?i:p.Root)?r:d,className:(0,$.A)(A.root,u,null==k?void 0:k.className),ownerState:(0,o.A)({},w,null==k?void 0:k.ownerState),classes:A,ref:t,children:s}))}))}));var Pa=n(1782),Ma=n(2456);function Oa(e,t){t?e.setAttribute("aria-hidden","true"):e.removeAttribute("aria-hidden")}function Ta(e){return parseInt((0,Dr.A)(e).getComputedStyle(e).paddingRight,10)||0}function ja(e,t,n,r,o){const a=[t,n,...r];[].forEach.call(e.children,(e=>{const t=-1===a.indexOf(e),n=!function(e){const t=-1!==["TEMPLATE","SCRIPT","STYLE","LINK","MAP","META","NOSCRIPT","PICTURE","COL","COLGROUP","PARAM","SLOT","SOURCE","TRACK"].indexOf(e.tagName),n="INPUT"===e.tagName&&"hidden"===e.getAttribute("type");return t||n}(e);t&&n&&Oa(e,o)}))}function Ia(e,t){let n=-1;return e.some(((e,r)=>!!t(e)&&(n=r,!0))),n}function Na(e,t){const n=[],r=e.container;if(!t.disableScrollLock){if(function(e){const t=(0,Ut.A)(e);return t.body===e?(0,Dr.A)(e).innerWidth>t.documentElement.clientWidth:e.scrollHeight>e.clientHeight}(r)){const e=la((0,Ut.A)(r));n.push({value:r.style.paddingRight,property:"padding-right",el:r}),r.style.paddingRight=`${Ta(r)+e}px`;const t=(0,Ut.A)(r).querySelectorAll(".mui-fixed");[].forEach.call(t,(t=>{n.push({value:t.style.paddingRight,property:"padding-right",el:t}),t.style.paddingRight=`${Ta(t)+e}px`}))}let e;if(r.parentNode instanceof DocumentFragment)e=(0,Ut.A)(r).body;else{const t=r.parentElement,n=(0,Dr.A)(r);e="HTML"===(null==t?void 0:t.nodeName)&&"scroll"===n.getComputedStyle(t).overflowY?t:r}n.push({value:e.style.overflow,property:"overflow",el:e},{value:e.style.overflowX,property:"overflow-x",el:e},{value:e.style.overflowY,property:"overflow-y",el:e}),e.style.overflow="hidden"}return()=>{n.forEach((e=>{let{value:t,el:n,property:r}=e;t?n.style.setProperty(r,t):n.style.removeProperty(r)}))}}const $a=new class{constructor(){this.containers=void 0,this.modals=void 0,this.modals=[],this.containers=[]}add(e,t){let n=this.modals.indexOf(e);if(-1!==n)return n;n=this.modals.length,this.modals.push(e),e.modalRef&&Oa(e.modalRef,!1);const r=function(e){const t=[];return[].forEach.call(e.children,(e=>{"true"===e.getAttribute("aria-hidden")&&t.push(e)})),t}(t);ja(t,e.mount,e.modalRef,r,!0);const o=Ia(this.containers,(e=>e.container===t));return-1!==o?(this.containers[o].modals.push(e),n):(this.containers.push({modals:[e],container:t,restore:null,hiddenSiblings:r}),n)}mount(e,t){const n=Ia(this.containers,(t=>-1!==t.modals.indexOf(e))),r=this.containers[n];r.restore||(r.restore=Na(r,t))}remove(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const n=this.modals.indexOf(e);if(-1===n)return n;const r=Ia(this.containers,(t=>-1!==t.modals.indexOf(e))),o=this.containers[r];if(o.modals.splice(o.modals.indexOf(e),1),this.modals.splice(n,1),0===o.modals.length)o.restore&&o.restore(),e.modalRef&&Oa(e.modalRef,t),ja(o.container,e.mount,e.modalRef,o.hiddenSiblings,!1),this.containers.splice(r,1);else{const e=o.modals[o.modals.length-1];e.modalRef&&Oa(e.modalRef,!1)}return n}isTopModal(e){return this.modals.length>0&&this.modals[this.modals.length-1]===e}};const za=function(t){const{container:n,disableEscapeKeyDown:r=!1,disableScrollLock:a=!1,manager:i=$a,closeAfterTransition:l=!1,onTransitionEnter:s,onTransitionExited:c,children:u,onClose:d,open:p,rootRef:f}=t,m=e.useRef({}),h=e.useRef(null),g=e.useRef(null),v=(0,Vt.A)(g,f),[y,b]=e.useState(!p),x=function(e){return!!e&&e.props.hasOwnProperty("in")}(u);let w=!0;"false"!==t["aria-hidden"]&&!1!==t["aria-hidden"]||(w=!1);const A=()=>(m.current.modalRef=g.current,m.current.mount=h.current,m.current),k=()=>{i.mount(A(),{disableScrollLock:a}),g.current&&(g.current.scrollTop=0)},S=(0,Pa.A)((()=>{const e=function(e){return"function"===typeof e?e():e}(n)||(0,Ut.A)(h.current).body;i.add(A(),e),g.current&&k()})),C=e.useCallback((()=>i.isTopModal(A())),[i]),E=(0,Pa.A)((e=>{h.current=e,e&&(p&&C()?k():g.current&&Oa(g.current,w))})),R=e.useCallback((()=>{i.remove(A(),w)}),[w,i]);e.useEffect((()=>()=>{R()}),[R]),e.useEffect((()=>{p?S():x&&l||R()}),[p,R,x,l,S]);const P=e=>t=>{var n;null==(n=e.onKeyDown)||n.call(e,t),"Escape"===t.key&&229!==t.which&&C()&&(r||(t.stopPropagation(),d&&d(t,"escapeKeyDown")))},M=e=>t=>{var n;null==(n=e.onClick)||n.call(e,t),t.target===t.currentTarget&&d&&d(t,"backdropClick")};return{getRootProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const n=rr(t);delete n.onTransitionEnter,delete n.onTransitionExited;const r=(0,o.A)({},n,e);return(0,o.A)({role:"presentation"},r,{onKeyDown:P(r),ref:v})},getBackdropProps:function(){const e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,o.A)({"aria-hidden":!0},e,{onClick:M(e),open:p})},getTransitionProps:()=>({onEnter:(0,Ma.A)((()=>{b(!1),s&&s()}),null==u?void 0:u.props.onEnter),onExited:(0,Ma.A)((()=>{b(!0),c&&c(),l&&R()}),null==u?void 0:u.props.onExited)}),rootRef:v,portalRef:E,isTopModal:C,exited:y,hasTransition:x}};function La(e){return(0,Ae.Ay)("MuiModal",e)}(0,W.A)("MuiModal",["root","hidden","backdrop"]);const _a=["BackdropComponent","BackdropProps","classes","className","closeAfterTransition","children","container","component","components","componentsProps","disableAutoFocus","disableEnforceFocus","disableEscapeKeyDown","disablePortal","disableRestoreFocus","disableScrollLock","hideBackdrop","keepMounted","onBackdropClick","onClose","onTransitionEnter","onTransitionExited","open","slotProps","slots","theme"],Fa=(0,K.Ay)("div",{name:"MuiModal",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,!n.open&&n.exited&&t.hidden]}})((e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({position:"fixed",zIndex:(t.vars||t).zIndex.modal,right:0,bottom:0,top:0,left:0},!n.open&&n.exited&&{visibility:"hidden"})})),Da=(0,K.Ay)(Ra,{name:"MuiModal",slot:"Backdrop",overridesResolver:(e,t)=>t.backdrop})({zIndex:-1}),Wa=e.forwardRef((function(t,n){var r,i,l,s,u,d;const p=(0,S.b)({name:"MuiModal",props:t}),{BackdropComponent:f=Da,BackdropProps:m,className:h,closeAfterTransition:g=!1,children:v,container:y,component:b,components:x={},componentsProps:w={},disableAutoFocus:A=!1,disableEnforceFocus:k=!1,disableEscapeKeyDown:C=!1,disablePortal:E=!1,disableRestoreFocus:R=!1,disableScrollLock:P=!1,hideBackdrop:M=!1,keepMounted:O=!1,onBackdropClick:T,open:j,slotProps:I,slots:N}=p,z=(0,a.A)(p,_a),L=(0,o.A)({},p,{closeAfterTransition:g,disableAutoFocus:A,disableEnforceFocus:k,disableEscapeKeyDown:C,disablePortal:E,disableRestoreFocus:R,disableScrollLock:P,hideBackdrop:M,keepMounted:O}),{getRootProps:_,getBackdropProps:F,getTransitionProps:D,portalRef:W,isTopModal:B,exited:V,hasTransition:H}=za((0,o.A)({},L,{rootRef:n})),X=(0,o.A)({},L,{exited:V}),K=(e=>{const{open:t,exited:n,classes:r}=e,o={root:["root",!t&&n&&"hidden"],backdrop:["backdrop"]};return(0,U.A)(o,La,r)})(X),q={};if(void 0===v.props.tabIndex&&(q.tabIndex="-1"),H){const{onEnter:e,onExited:t}=D();q.onEnter=e,q.onExited=t}const G=null!=(r=null!=(i=null==N?void 0:N.root)?i:x.Root)?r:Fa,Q=null!=(l=null!=(s=null==N?void 0:N.backdrop)?s:x.Backdrop)?l:f,Y=null!=(u=null==I?void 0:I.root)?u:w.root,J=null!=(d=null==I?void 0:I.backdrop)?d:w.backdrop,Z=sr({elementType:G,externalSlotProps:Y,externalForwardedProps:z,getSlotProps:_,additionalProps:{ref:n,as:b},ownerState:X,className:(0,$.A)(h,null==Y?void 0:Y.className,null==K?void 0:K.root,!X.open&&X.exited&&(null==K?void 0:K.hidden))}),ee=sr({elementType:Q,externalSlotProps:J,additionalProps:m,getSlotProps:e=>F((0,o.A)({},e,{onClick:t=>{T&&T(t),null!=e&&e.onClick&&e.onClick(t)}})),className:(0,$.A)(null==J?void 0:J.className,null==m?void 0:m.className,null==K?void 0:K.backdrop),ownerState:X});return O||j||H&&!V?(0,c.jsx)(ur,{ref:W,container:y,disablePortal:E,children:(0,c.jsxs)(G,(0,o.A)({},Z,{children:[!M&&f?(0,c.jsx)(Q,(0,o.A)({},ee)):null,(0,c.jsx)(xa,{disableEnforceFocus:k,disableAutoFocus:A,disableRestoreFocus:R,isEnabled:B,open:j,children:e.cloneElement(v,q)})]}))}):null}));function Ba(e){return(0,Ae.Ay)("MuiPopover",e)}(0,W.A)("MuiPopover",["root","paper"]);const Va=["onEntering"],Ha=["action","anchorEl","anchorOrigin","anchorPosition","anchorReference","children","className","container","elevation","marginThreshold","open","PaperProps","slots","slotProps","transformOrigin","TransitionComponent","transitionDuration","TransitionProps","disableScrollLock"],Ua=["slotProps"];function Xa(e,t){let n=0;return"number"===typeof t?n=t:"center"===t?n=e.height/2:"bottom"===t&&(n=e.height),n}function Ka(e,t){let n=0;return"number"===typeof t?n=t:"center"===t?n=e.width/2:"right"===t&&(n=e.width),n}function qa(e){return[e.horizontal,e.vertical].map((e=>"number"===typeof e?`${e}px`:e)).join(" ")}function Ga(e){return"function"===typeof e?e():e}const Qa=(0,K.Ay)(Wa,{name:"MuiPopover",slot:"Root",overridesResolver:(e,t)=>t.root})({}),Ya=(0,K.Ay)(mt,{name:"MuiPopover",slot:"Paper",overridesResolver:(e,t)=>t.paper})({position:"absolute",overflowY:"auto",overflowX:"hidden",minWidth:16,minHeight:16,maxWidth:"calc(100% - 32px)",maxHeight:"calc(100% - 32px)",outline:0}),Ja=e.forwardRef((function(t,n){var r,i,l;const s=(0,S.b)({props:t,name:"MuiPopover"}),{action:u,anchorEl:d,anchorOrigin:p={vertical:"top",horizontal:"left"},anchorPosition:f,anchorReference:m="anchorEl",children:h,className:g,container:v,elevation:y=8,marginThreshold:b=16,open:x,PaperProps:w={},slots:A,slotProps:k,transformOrigin:C={vertical:"top",horizontal:"left"},TransitionComponent:E=Wt,transitionDuration:R="auto",TransitionProps:{onEntering:P}={},disableScrollLock:M=!1}=s,O=(0,a.A)(s.TransitionProps,Va),T=(0,a.A)(s,Ha),j=null!=(r=null==k?void 0:k.paper)?r:w,I=e.useRef(),N=(0,q.A)(I,j.ref),z=(0,o.A)({},s,{anchorOrigin:p,anchorReference:m,elevation:y,marginThreshold:b,externalPaperSlotProps:j,transformOrigin:C,TransitionComponent:E,transitionDuration:R,TransitionProps:O}),L=(e=>{const{classes:t}=e;return(0,U.A)({root:["root"],paper:["paper"]},Ba,t)})(z),_=e.useCallback((()=>{if("anchorPosition"===m)return f;const e=Ga(d),t=(e&&1===e.nodeType?e:(0,ta.A)(I.current).body).getBoundingClientRect();return{top:t.top+Xa(t,p.vertical),left:t.left+Ka(t,p.horizontal)}}),[d,p.horizontal,p.vertical,f,m]),F=e.useCallback((e=>({vertical:Xa(e,C.vertical),horizontal:Ka(e,C.horizontal)})),[C.horizontal,C.vertical]),D=e.useCallback((e=>{const t={width:e.offsetWidth,height:e.offsetHeight},n=F(t);if("none"===m)return{top:null,left:null,transformOrigin:qa(n)};const r=_();let o=r.top-n.vertical,a=r.left-n.horizontal;const i=o+t.height,l=a+t.width,s=(0,ga.A)(Ga(d)),c=s.innerHeight-b,u=s.innerWidth-b;if(null!==b&&o<b){const e=o-b;o-=e,n.vertical+=e}else if(null!==b&&i>c){const e=i-c;o-=e,n.vertical+=e}if(null!==b&&a<b){const e=a-b;a-=e,n.horizontal+=e}else if(l>u){const e=l-u;a-=e,n.horizontal+=e}return{top:`${Math.round(o)}px`,left:`${Math.round(a)}px`,transformOrigin:qa(n)}}),[d,m,_,F,b]),[W,B]=e.useState(x),V=e.useCallback((()=>{const e=I.current;if(!e)return;const t=D(e);null!==t.top&&(e.style.top=t.top),null!==t.left&&(e.style.left=t.left),e.style.transformOrigin=t.transformOrigin,B(!0)}),[D]);e.useEffect((()=>(M&&window.addEventListener("scroll",V),()=>window.removeEventListener("scroll",V))),[d,M,V]);e.useEffect((()=>{x&&V()})),e.useImperativeHandle(u,(()=>x?{updatePosition:()=>{V()}}:null),[x,V]),e.useEffect((()=>{if(!x)return;const e=(0,ha.A)((()=>{V()})),t=(0,ga.A)(d);return t.addEventListener("resize",e),()=>{e.clear(),t.removeEventListener("resize",e)}}),[d,x,V]);let H=R;"auto"!==R||E.muiSupportAuto||(H=void 0);const X=v||(d?(0,ta.A)(Ga(d)).body:void 0),K=null!=(i=null==A?void 0:A.root)?i:Qa,G=null!=(l=null==A?void 0:A.paper)?l:Ya,Q=sr({elementType:G,externalSlotProps:(0,o.A)({},j,{style:W?j.style:(0,o.A)({},j.style,{opacity:0})}),additionalProps:{elevation:y,ref:N},ownerState:z,className:(0,$.A)(L.paper,null==j?void 0:j.className)}),Y=sr({elementType:K,externalSlotProps:(null==k?void 0:k.root)||{},externalForwardedProps:T,additionalProps:{ref:n,slotProps:{backdrop:{invisible:!0}},container:X,open:x},ownerState:z,className:(0,$.A)(L.root,g)}),{slotProps:J}=Y,Z=(0,a.A)(Y,Ua);return(0,c.jsx)(K,(0,o.A)({},Z,!wt(K)&&{slotProps:J,disableScrollLock:M},{children:(0,c.jsx)(E,(0,o.A)({appear:!0,in:x,onEntering:(e,t)=>{P&&P(e,t),V()},onExited:()=>{B(!1)},timeout:H},O,{children:(0,c.jsx)(G,(0,o.A)({},Q,{children:h}))}))}))}));function Za(e){return(0,Ae.Ay)("MuiMenu",e)}(0,W.A)("MuiMenu",["root","paper","list"]);const ei=["onEntering"],ti=["autoFocus","children","className","disableAutoFocusItem","MenuListProps","onClose","open","PaperProps","PopoverClasses","transitionDuration","TransitionProps","variant","slots","slotProps"],ni={vertical:"top",horizontal:"right"},ri={vertical:"top",horizontal:"left"},oi=(0,K.Ay)(Ja,{shouldForwardProp:e=>(0,$e.A)(e)||"classes"===e,name:"MuiMenu",slot:"Root",overridesResolver:(e,t)=>t.root})({}),ai=(0,K.Ay)(Ya,{name:"MuiMenu",slot:"Paper",overridesResolver:(e,t)=>t.paper})({maxHeight:"calc(100% - 96px)",WebkitOverflowScrolling:"touch"}),ii=(0,K.Ay)(ma,{name:"MuiMenu",slot:"List",overridesResolver:(e,t)=>t.list})({outline:0}),li=e.forwardRef((function(t,n){var r,i;const l=(0,S.b)({props:t,name:"MuiMenu"}),{autoFocus:s=!0,children:u,className:d,disableAutoFocusItem:p=!1,MenuListProps:f={},onClose:m,open:g,PaperProps:v={},PopoverClasses:y,transitionDuration:b="auto",TransitionProps:{onEntering:x}={},variant:w="selectedMenu",slots:A={},slotProps:k={}}=l,C=(0,a.A)(l.TransitionProps,ei),E=(0,a.A)(l,ti),R=h(),P=(0,o.A)({},l,{autoFocus:s,disableAutoFocusItem:p,MenuListProps:f,onEntering:x,PaperProps:v,transitionDuration:b,TransitionProps:C,variant:w}),M=(e=>{const{classes:t}=e;return(0,U.A)({root:["root"],paper:["paper"],list:["list"]},Za,t)})(P),O=s&&!p&&g,T=e.useRef(null);let j=-1;e.Children.map(u,((t,n)=>{e.isValidElement(t)&&(t.props.disabled||("selectedMenu"===w&&t.props.selected||-1===j)&&(j=n))}));const I=null!=(r=A.paper)?r:ai,N=null!=(i=k.paper)?i:v,z=sr({elementType:A.root,externalSlotProps:k.root,ownerState:P,className:[M.root,d]}),L=sr({elementType:I,externalSlotProps:N,ownerState:P,className:M.paper});return(0,c.jsx)(oi,(0,o.A)({onClose:m,anchorOrigin:{vertical:"bottom",horizontal:R?"right":"left"},transformOrigin:R?ni:ri,slots:{paper:I,root:A.root},slotProps:{root:z,paper:L},open:g,ref:n,transitionDuration:b,TransitionProps:(0,o.A)({onEntering:(e,t)=>{T.current&&T.current.adjustStyleForScrollbar(e,{direction:R?"rtl":"ltr"}),x&&x(e,t)}},C),ownerState:P},E,{classes:y,children:(0,c.jsx)(ii,(0,o.A)({onKeyDown:e=>{"Tab"===e.key&&(e.preventDefault(),m&&m(e,"tabKeyDown"))},actions:T,autoFocus:s&&(-1===j||p),autoFocusItem:O,variant:w},f,{className:(0,$.A)(M.list,f.className),children:u}))}))}));function si(e){return(0,Ae.Ay)("MuiNativeSelect",e)}const ci=(0,W.A)("MuiNativeSelect",["root","select","multiple","filled","outlined","standard","disabled","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]),ui=["className","disabled","error","IconComponent","inputRef","variant"],di=e=>{let{ownerState:t,theme:n}=e;return(0,o.A)({MozAppearance:"none",WebkitAppearance:"none",userSelect:"none",borderRadius:0,cursor:"pointer","&:focus":(0,o.A)({},n.vars?{backgroundColor:`rgba(${n.vars.palette.common.onBackgroundChannel} / 0.05)`}:{backgroundColor:"light"===n.palette.mode?"rgba(0, 0, 0, 0.05)":"rgba(255, 255, 255, 0.05)"},{borderRadius:0}),"&::-ms-expand":{display:"none"},[`&.${ci.disabled}`]:{cursor:"default"},"&[multiple]":{height:"auto"},"&:not([multiple]) option, &:not([multiple]) optgroup":{backgroundColor:(n.vars||n).palette.background.paper},"&&&":{paddingRight:24,minWidth:16}},"filled"===t.variant&&{"&&&":{paddingRight:32}},"outlined"===t.variant&&{borderRadius:(n.vars||n).shape.borderRadius,"&:focus":{borderRadius:(n.vars||n).shape.borderRadius},"&&&":{paddingRight:32}})},pi=(0,K.Ay)("select",{name:"MuiNativeSelect",slot:"Select",shouldForwardProp:$e.A,overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.select,t[n.variant],n.error&&t.error,{[`&.${ci.multiple}`]:t.multiple}]}})(di),fi=e=>{let{ownerState:t,theme:n}=e;return(0,o.A)({position:"absolute",right:0,top:"calc(50% - .5em)",pointerEvents:"none",color:(n.vars||n).palette.action.active,[`&.${ci.disabled}`]:{color:(n.vars||n).palette.action.disabled}},t.open&&{transform:"rotate(180deg)"},"filled"===t.variant&&{right:7},"outlined"===t.variant&&{right:7})},mi=(0,K.Ay)("svg",{name:"MuiNativeSelect",slot:"Icon",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.icon,n.variant&&t[`icon${(0,Pe.A)(n.variant)}`],n.open&&t.iconOpen]}})(fi),hi=e.forwardRef((function(t,n){const{className:r,disabled:i,error:l,IconComponent:s,inputRef:u,variant:d="standard"}=t,p=(0,a.A)(t,ui),f=(0,o.A)({},t,{disabled:i,variant:d,error:l}),m=(e=>{const{classes:t,variant:n,disabled:r,multiple:o,open:a,error:i}=e,l={select:["select",n,r&&"disabled",o&&"multiple",i&&"error"],icon:["icon",`icon${(0,Pe.A)(n)}`,a&&"iconOpen",r&&"disabled"]};return(0,U.A)(l,si,t)})(f);return(0,c.jsxs)(e.Fragment,{children:[(0,c.jsx)(pi,(0,o.A)({ownerState:f,className:(0,$.A)(m.select,r),disabled:i,ref:u||n},p)),t.multiple?null:(0,c.jsx)(mi,{as:s,ownerState:f,className:m.icon})]})}));var gi=n(7123);function vi(e){return(0,Ae.Ay)("MuiSelect",e)}const yi=(0,W.A)("MuiSelect",["root","select","multiple","filled","outlined","standard","disabled","focused","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]);var bi;const xi=["aria-describedby","aria-label","autoFocus","autoWidth","children","className","defaultOpen","defaultValue","disabled","displayEmpty","error","IconComponent","inputRef","labelId","MenuProps","multiple","name","onBlur","onChange","onClose","onFocus","onOpen","open","readOnly","renderValue","SelectDisplayProps","tabIndex","type","value","variant"],wi=(0,K.Ay)("div",{name:"MuiSelect",slot:"Select",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[{[`&.${yi.select}`]:t.select},{[`&.${yi.select}`]:t[n.variant]},{[`&.${yi.error}`]:t.error},{[`&.${yi.multiple}`]:t.multiple}]}})(di,{[`&.${yi.select}`]:{height:"auto",minHeight:"1.4375em",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"}}),Ai=(0,K.Ay)("svg",{name:"MuiSelect",slot:"Icon",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.icon,n.variant&&t[`icon${(0,Pe.A)(n.variant)}`],n.open&&t.iconOpen]}})(fi),ki=(0,K.Ay)("input",{shouldForwardProp:e=>(0,gi.A)(e)&&"classes"!==e,name:"MuiSelect",slot:"NativeInput",overridesResolver:(e,t)=>t.nativeInput})({bottom:0,left:0,position:"absolute",opacity:0,pointerEvents:"none",width:"100%",boxSizing:"border-box"});function Si(e,t){return"object"===typeof t&&null!==t?e===t:String(e)===String(t)}function Ci(e){return null==e||"string"===typeof e&&!e.trim()}const Ei=e.forwardRef((function(t,n){var r;const{"aria-describedby":i,"aria-label":l,autoFocus:s,autoWidth:u,children:d,className:p,defaultOpen:f,defaultValue:m,disabled:h,displayEmpty:g,error:v=!1,IconComponent:y,inputRef:b,labelId:x,MenuProps:w={},multiple:A,name:k,onBlur:S,onChange:C,onClose:E,onFocus:R,onOpen:P,open:M,readOnly:O,renderValue:T,SelectDisplayProps:j={},tabIndex:I,value:N,variant:z="standard"}=t,L=(0,a.A)(t,xi),[_,F]=(0,kr.A)({controlled:N,default:m,name:"Select"}),[D,W]=(0,kr.A)({controlled:M,default:f,name:"Select"}),B=e.useRef(null),V=e.useRef(null),[H,X]=e.useState(null),{current:K}=e.useRef(null!=M),[G,Q]=e.useState(),Y=(0,q.A)(n,b),J=e.useCallback((e=>{V.current=e,e&&X(e)}),[]),Z=null==H?void 0:H.parentNode;e.useImperativeHandle(Y,(()=>({focus:()=>{V.current.focus()},node:B.current,value:_})),[_]),e.useEffect((()=>{f&&D&&H&&!K&&(Q(u?null:Z.clientWidth),V.current.focus())}),[H,u]),e.useEffect((()=>{s&&V.current.focus()}),[s]),e.useEffect((()=>{if(!x)return;const e=(0,ta.A)(V.current).getElementById(x);if(e){const t=()=>{getSelection().isCollapsed&&V.current.focus()};return e.addEventListener("click",t),()=>{e.removeEventListener("click",t)}}}),[x]);const ee=(e,t)=>{e?P&&P(t):E&&E(t),K||(Q(u?null:Z.clientWidth),W(e))},te=e.Children.toArray(d),ne=e=>t=>{let n;if(t.currentTarget.hasAttribute("tabindex")){if(A){n=Array.isArray(_)?_.slice():[];const t=_.indexOf(e.props.value);-1===t?n.push(e.props.value):n.splice(t,1)}else n=e.props.value;if(e.props.onClick&&e.props.onClick(t),_!==n&&(F(n),C)){const r=t.nativeEvent||t,o=new r.constructor(r.type,r);Object.defineProperty(o,"target",{writable:!0,value:{value:n,name:k}}),C(o,e)}A||ee(!1,t)}},re=null!==H&&D;let oe,ae;delete L["aria-invalid"];const ie=[];let le=!1,se=!1;(Yr({value:_})||g)&&(T?oe=T(_):le=!0);const ce=te.map((t=>{if(!e.isValidElement(t))return null;let n;if(A){if(!Array.isArray(_))throw new Error((0,Fr.A)(2));n=_.some((e=>Si(e,t.props.value))),n&&le&&ie.push(t.props.children)}else n=Si(_,t.props.value),n&&le&&(ae=t.props.children);return n&&(se=!0),e.cloneElement(t,{"aria-selected":n?"true":"false",onClick:ne(t),onKeyUp:e=>{" "===e.key&&e.preventDefault(),t.props.onKeyUp&&t.props.onKeyUp(e)},role:"option",selected:n,value:void 0,"data-value":t.props.value})}));le&&(oe=A?0===ie.length?null:ie.reduce(((e,t,n)=>(e.push(t),n<ie.length-1&&e.push(", "),e)),[]):ae);let ue,de=G;!u&&K&&H&&(de=Z.clientWidth),ue="undefined"!==typeof I?I:h?null:0;const pe=j.id||(k?`mui-component-select-${k}`:void 0),fe=(0,o.A)({},t,{variant:z,value:_,open:re,error:v}),me=(e=>{const{classes:t,variant:n,disabled:r,multiple:o,open:a,error:i}=e,l={select:["select",n,r&&"disabled",o&&"multiple",i&&"error"],icon:["icon",`icon${(0,Pe.A)(n)}`,a&&"iconOpen",r&&"disabled"],nativeInput:["nativeInput"]};return(0,U.A)(l,vi,t)})(fe),he=(0,o.A)({},w.PaperProps,null==(r=w.slotProps)?void 0:r.paper),ge=(0,_r.A)();return(0,c.jsxs)(e.Fragment,{children:[(0,c.jsx)(wi,(0,o.A)({ref:J,tabIndex:ue,role:"combobox","aria-controls":ge,"aria-disabled":h?"true":void 0,"aria-expanded":re?"true":"false","aria-haspopup":"listbox","aria-label":l,"aria-labelledby":[x,pe].filter(Boolean).join(" ")||void 0,"aria-describedby":i,onKeyDown:e=>{if(!O){-1!==[" ","ArrowUp","ArrowDown","Enter"].indexOf(e.key)&&(e.preventDefault(),ee(!0,e))}},onMouseDown:h||O?null:e=>{0===e.button&&(e.preventDefault(),V.current.focus(),ee(!0,e))},onBlur:e=>{!re&&S&&(Object.defineProperty(e,"target",{writable:!0,value:{value:_,name:k}}),S(e))},onFocus:R},j,{ownerState:fe,className:(0,$.A)(j.className,me.select,p),id:pe,children:Ci(oe)?bi||(bi=(0,c.jsx)("span",{className:"notranslate",children:"\u200b"})):oe})),(0,c.jsx)(ki,(0,o.A)({"aria-invalid":v,value:Array.isArray(_)?_.join(","):_,name:k,ref:B,"aria-hidden":!0,onChange:e=>{const t=te.find((t=>t.props.value===e.target.value));void 0!==t&&(F(t.props.value),C&&C(e,t))},tabIndex:-1,disabled:h,className:me.nativeInput,autoFocus:s,ownerState:fe},L)),(0,c.jsx)(Ai,{as:y,className:me.icon,ownerState:fe}),(0,c.jsx)(li,(0,o.A)({id:`menu-${k||""}`,anchorEl:Z,open:re,onClose:e=>{ee(!1,e)},anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"}},w,{MenuListProps:(0,o.A)({"aria-labelledby":x,role:"listbox","aria-multiselectable":A?"true":void 0,disableListWrap:!0,id:ge},w.MenuListProps),slotProps:(0,o.A)({},w.slotProps,{paper:(0,o.A)({},he,{style:(0,o.A)({minWidth:de},null!=he?he.style:null)})}),children:ce}))]})}));var Ri=n(9662);const Pi=(0,Ri.A)((0,c.jsx)("path",{d:"M7 10l5 5 5-5z"}),"ArrowDropDown"),Mi=["autoWidth","children","classes","className","defaultOpen","displayEmpty","IconComponent","id","input","inputProps","label","labelId","MenuProps","multiple","native","onClose","onOpen","open","renderValue","SelectDisplayProps","variant"],Oi=["root"],Ti={name:"MuiSelect",overridesResolver:(e,t)=>t.root,shouldForwardProp:e=>(0,$e.A)(e)&&"variant"!==e,slot:"Root"},ji=(0,K.Ay)(mo,Ti)(""),Ii=(0,K.Ay)(Io,Ti)(""),Ni=(0,K.Ay)(wo,Ti)(""),$i=e.forwardRef((function(t,n){const r=(0,S.b)({name:"MuiSelect",props:t}),{autoWidth:i=!1,children:l,classes:s={},className:u,defaultOpen:d=!1,displayEmpty:p=!1,IconComponent:f=Pi,id:m,input:h,inputProps:g,label:v,labelId:y,MenuProps:b,multiple:x=!1,native:w=!1,onClose:A,onOpen:k,open:C,renderValue:E,SelectDisplayProps:R,variant:P="outlined"}=r,M=(0,a.A)(r,Mi),O=w?hi:Ei,T=Xr({props:r,muiFormControl:qr(),states:["variant","error"]}),j=T.variant||P,I=(0,o.A)({},r,{variant:j,classes:s}),N=(e=>{const{classes:t}=e;return t})(I),z=(0,a.A)(N,Oi),L=h||{standard:(0,c.jsx)(ji,{ownerState:I}),outlined:(0,c.jsx)(Ii,{label:v,ownerState:I}),filled:(0,c.jsx)(Ni,{ownerState:I})}[j],_=(0,q.A)(n,L.ref);return(0,c.jsx)(e.Fragment,{children:e.cloneElement(L,(0,o.A)({inputComponent:O,inputProps:(0,o.A)({children:l,error:T.error,IconComponent:f,variant:j,type:void 0,multiple:x},w?{id:m}:{autoWidth:i,defaultOpen:d,displayEmpty:p,labelId:y,MenuProps:b,onClose:A,onOpen:k,open:C,renderValue:E,SelectDisplayProps:(0,o.A)({id:m},R)},g,{classes:g?(0,qe.A)(z,g.classes):z},h?h.props.inputProps:{})},(x&&w||p)&&"outlined"===j?{notched:!0}:{},{ref:_,className:(0,$.A)(L.props.className,u,N.root)},!h&&{variant:j},M))})}));$i.muiName="Select";const zi=$i;function Li(e){return(0,Ae.Ay)("MuiTextField",e)}(0,W.A)("MuiTextField",["root"]);const _i=["autoComplete","autoFocus","children","className","color","defaultValue","disabled","error","FormHelperTextProps","fullWidth","helperText","id","InputLabelProps","inputProps","InputProps","inputRef","label","maxRows","minRows","multiline","name","onBlur","onChange","onFocus","placeholder","required","rows","select","SelectProps","type","value","variant"],Fi={standard:mo,filled:wo,outlined:Io},Di=(0,K.Ay)(qo,{name:"MuiTextField",slot:"Root",overridesResolver:(e,t)=>t.root})({}),Wi=e.forwardRef((function(e,t){const n=(0,S.b)({props:e,name:"MuiTextField"}),{autoComplete:r,autoFocus:i=!1,children:l,className:s,color:u="primary",defaultValue:d,disabled:p=!1,error:f=!1,FormHelperTextProps:m,fullWidth:h=!1,helperText:g,id:v,InputLabelProps:y,inputProps:b,InputProps:x,inputRef:w,label:A,maxRows:k,minRows:C,multiline:E=!1,name:R,onBlur:P,onChange:M,onFocus:O,placeholder:T,required:j=!1,rows:I,select:N=!1,SelectProps:z,type:L,value:_,variant:F="outlined"}=n,D=(0,a.A)(n,_i),W=(0,o.A)({},n,{autoFocus:i,color:u,disabled:p,error:f,fullWidth:h,multiline:E,required:j,select:N,variant:F}),B=(e=>{const{classes:t}=e;return(0,U.A)({root:["root"]},Li,t)})(W);const V={};"outlined"===F&&(y&&"undefined"!==typeof y.shrink&&(V.notched=y.shrink),V.label=A),N&&(z&&z.native||(V.id=void 0),V["aria-describedby"]=void 0);const H=(0,_r.A)(v),X=g&&H?`${H}-helper-text`:void 0,K=A&&H?`${H}-label`:void 0,q=Fi[F],G=(0,c.jsx)(q,(0,o.A)({"aria-describedby":X,autoComplete:r,autoFocus:i,defaultValue:d,fullWidth:h,multiline:E,name:R,rows:I,maxRows:k,minRows:C,type:L,value:_,id:H,inputRef:w,onBlur:P,onChange:M,onFocus:O,placeholder:T,inputProps:b},V,x));return(0,c.jsxs)(Di,(0,o.A)({className:(0,$.A)(B.root,s),disabled:p,error:f,fullWidth:h,ref:t,required:j,color:u,variant:F,ownerState:W},D,{children:[null!=A&&""!==A&&(0,c.jsx)(Vo,(0,o.A)({htmlFor:H,id:K},y,{children:A})),N?(0,c.jsx)(zi,(0,o.A)({"aria-describedby":X,id:H,labelId:K,value:_,input:G},z,{children:l})):G,g&&(0,c.jsx)(ea,(0,o.A)({id:X},m,{children:g}))]}))}));function Bi(e){return(0,Ae.Ay)("MuiListItem",e)}const Vi=(0,W.A)("MuiListItem",["root","container","focusVisible","dense","alignItemsFlexStart","disabled","divider","gutters","padding","button","secondaryAction","selected"]);const Hi=(0,W.A)("MuiListItemButton",["root","focusVisible","dense","alignItemsFlexStart","disabled","divider","gutters","selected"]);function Ui(e){return(0,Ae.Ay)("MuiListItemSecondaryAction",e)}(0,W.A)("MuiListItemSecondaryAction",["root","disableGutters"]);const Xi=["className"],Ki=(0,K.Ay)("div",{name:"MuiListItemSecondaryAction",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.disableGutters&&t.disableGutters]}})((e=>{let{ownerState:t}=e;return(0,o.A)({position:"absolute",right:16,top:"50%",transform:"translateY(-50%)"},t.disableGutters&&{right:0})})),qi=e.forwardRef((function(t,n){const r=(0,S.b)({props:t,name:"MuiListItemSecondaryAction"}),{className:i}=r,l=(0,a.A)(r,Xi),s=e.useContext(na),u=(0,o.A)({},r,{disableGutters:s.disableGutters}),d=(e=>{const{disableGutters:t,classes:n}=e,r={root:["root",t&&"disableGutters"]};return(0,U.A)(r,Ui,n)})(u);return(0,c.jsx)(Ki,(0,o.A)({className:(0,$.A)(d.root,i),ownerState:u,ref:n},l))}));qi.muiName="ListItemSecondaryAction";const Gi=qi,Qi=["className"],Yi=["alignItems","autoFocus","button","children","className","component","components","componentsProps","ContainerComponent","ContainerProps","dense","disabled","disableGutters","disablePadding","divider","focusVisibleClassName","secondaryAction","selected","slotProps","slots"],Ji=(0,K.Ay)("div",{name:"MuiListItem",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.dense&&t.dense,"flex-start"===n.alignItems&&t.alignItemsFlexStart,n.divider&&t.divider,!n.disableGutters&&t.gutters,!n.disablePadding&&t.padding,n.button&&t.button,n.hasSecondaryAction&&t.secondaryAction]}})((e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",width:"100%",boxSizing:"border-box",textAlign:"left"},!n.disablePadding&&(0,o.A)({paddingTop:8,paddingBottom:8},n.dense&&{paddingTop:4,paddingBottom:4},!n.disableGutters&&{paddingLeft:16,paddingRight:16},!!n.secondaryAction&&{paddingRight:48}),!!n.secondaryAction&&{[`& > .${Hi.root}`]:{paddingRight:48}},{[`&.${Vi.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},[`&.${Vi.selected}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,X.X4)(t.palette.primary.main,t.palette.action.selectedOpacity),[`&.${Vi.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.focusOpacity}))`:(0,X.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},[`&.${Vi.disabled}`]:{opacity:(t.vars||t).palette.action.disabledOpacity}},"flex-start"===n.alignItems&&{alignItems:"flex-start"},n.divider&&{borderBottom:`1px solid ${(t.vars||t).palette.divider}`,backgroundClip:"padding-box"},n.button&&{transition:t.transitions.create("background-color",{duration:t.transitions.duration.shortest}),"&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${Vi.selected}:hover`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:(0,X.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,X.X4)(t.palette.primary.main,t.palette.action.selectedOpacity)}}},n.hasSecondaryAction&&{paddingRight:48})})),Zi=(0,K.Ay)("li",{name:"MuiListItem",slot:"Container",overridesResolver:(e,t)=>t.container})({position:"relative"}),el=e.forwardRef((function(t,n){const r=(0,S.b)({props:t,name:"MuiListItem"}),{alignItems:i="center",autoFocus:l=!1,button:s=!1,children:u,className:d,component:p,components:f={},componentsProps:m={},ContainerComponent:h="li",ContainerProps:{className:g}={},dense:v=!1,disabled:y=!1,disableGutters:b=!1,disablePadding:x=!1,divider:w=!1,focusVisibleClassName:A,secondaryAction:k,selected:C=!1,slotProps:E={},slots:R={}}=r,P=(0,a.A)(r.ContainerProps,Qi),M=(0,a.A)(r,Yi),O=e.useContext(na),T=e.useMemo((()=>({dense:v||O.dense||!1,alignItems:i,disableGutters:b})),[i,O.dense,v,b]),j=e.useRef(null);(0,Gr.A)((()=>{l&&j.current&&j.current.focus()}),[l]);const I=e.Children.toArray(u),N=I.length&&(0,Ho.A)(I[I.length-1],["ListItemSecondaryAction"]),z=(0,o.A)({},r,{alignItems:i,autoFocus:l,button:s,dense:T.dense,disabled:y,disableGutters:b,disablePadding:x,divider:w,hasSecondaryAction:N,selected:C}),L=(e=>{const{alignItems:t,button:n,classes:r,dense:o,disabled:a,disableGutters:i,disablePadding:l,divider:s,hasSecondaryAction:c,selected:u}=e,d={root:["root",o&&"dense",!i&&"gutters",!l&&"padding",s&&"divider",a&&"disabled",n&&"button","flex-start"===t&&"alignItemsFlexStart",c&&"secondaryAction",u&&"selected"],container:["container"]};return(0,U.A)(d,Bi,r)})(z),_=(0,q.A)(j,n),F=R.root||f.Root||Ji,D=E.root||m.root||{},W=(0,o.A)({className:(0,$.A)(L.root,D.className,d),disabled:y},M);let B=p||"li";return s&&(W.component=p||"div",W.focusVisibleClassName=(0,$.A)(Vi.focusVisible,A),B=Re),N?(B=W.component||p?B:"div","li"===h&&("li"===B?B="div":"li"===W.component&&(W.component="div")),(0,c.jsx)(na.Provider,{value:T,children:(0,c.jsxs)(Zi,(0,o.A)({as:h,className:(0,$.A)(L.container,g),ref:_,ownerState:z},P,{children:[(0,c.jsx)(F,(0,o.A)({},D,!wt(F)&&{as:B,ownerState:(0,o.A)({},z,D.ownerState)},W,{children:I})),I.pop()]}))})):(0,c.jsx)(na.Provider,{value:T,children:(0,c.jsxs)(F,(0,o.A)({},D,{as:B,ref:_},!wt(F)&&{ownerState:(0,o.A)({},z,D.ownerState)},W,{children:[I,k&&(0,c.jsx)(Gi,{children:k})]}))})}));function tl(e){return(0,Ae.Ay)("MuiListItemAvatar",e)}(0,W.A)("MuiListItemAvatar",["root","alignItemsFlexStart"]);const nl=["className"],rl=(0,K.Ay)("div",{name:"MuiListItemAvatar",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,"flex-start"===n.alignItems&&t.alignItemsFlexStart]}})((e=>{let{ownerState:t}=e;return(0,o.A)({minWidth:56,flexShrink:0},"flex-start"===t.alignItems&&{marginTop:8})})),ol=e.forwardRef((function(t,n){const r=(0,S.b)({props:t,name:"MuiListItemAvatar"}),{className:i}=r,l=(0,a.A)(r,nl),s=e.useContext(na),u=(0,o.A)({},r,{alignItems:s.alignItems}),d=(e=>{const{alignItems:t,classes:n}=e,r={root:["root","flex-start"===t&&"alignItemsFlexStart"]};return(0,U.A)(r,tl,n)})(u);return(0,c.jsx)(rl,(0,o.A)({className:(0,$.A)(d.root,i),ownerState:u,ref:n},l))})),al=(0,Ri.A)((0,c.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person");function il(e){return(0,Ae.Ay)("MuiAvatar",e)}(0,W.A)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);const ll=["className","elementType","ownerState","externalForwardedProps","getSlotOwnerState","internalForwardedProps"],sl=["component","slots","slotProps"],cl=["component"];function ul(e,t){const{className:n,elementType:r,ownerState:i,externalForwardedProps:l,getSlotOwnerState:s,internalForwardedProps:c}=t,u=(0,a.A)(t,ll),{component:d,slots:p={[e]:void 0},slotProps:f={[e]:void 0}}=l,m=(0,a.A)(l,sl),h=p[e]||r,g=ir(f[e],i),v=ar((0,o.A)({className:n},u,{externalForwardedProps:"root"===e?m:void 0,externalSlotProps:g})),{props:{component:y},internalRef:b}=v,x=(0,a.A)(v.props,cl),w=(0,Vt.A)(b,null==g?void 0:g.ref,t.ref),A=s?s(x):{},k=(0,o.A)({},i,A),S="root"===e?y||d:y,C=At(h,(0,o.A)({},"root"===e&&!d&&!p[e]&&c,"root"!==e&&!p[e]&&c,x,S&&{as:S},{ref:w}),k);return Object.keys(A).forEach((e=>{delete C[e]})),[h,C]}const dl=["alt","children","className","component","slots","slotProps","imgProps","sizes","src","srcSet","variant"],pl=(0,K.Ay)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[n.variant],n.colorDefault&&t.colorDefault]}})((e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:t.typography.fontFamily,fontSize:t.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(t.vars||t).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:(0,o.A)({color:(t.vars||t).palette.background.default},t.vars?{backgroundColor:t.vars.palette.Avatar.defaultBg}:(0,o.A)({backgroundColor:t.palette.grey[400]},t.applyStyles("dark",{backgroundColor:t.palette.grey[600]})))}]}})),fl=(0,K.Ay)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),ml=(0,K.Ay)(al,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"});const hl=e.forwardRef((function(t,n){const r=(0,S.b)({props:t,name:"MuiAvatar"}),{alt:i,children:l,className:s,component:u="div",slots:d={},slotProps:p={},imgProps:f,sizes:m,src:h,srcSet:g,variant:v="circular"}=r,y=(0,a.A)(r,dl);let b=null;const x=function(t){let{crossOrigin:n,referrerPolicy:r,src:o,srcSet:a}=t;const[i,l]=e.useState(!1);return e.useEffect((()=>{if(!o&&!a)return;l(!1);let e=!0;const t=new Image;return t.onload=()=>{e&&l("loaded")},t.onerror=()=>{e&&l("error")},t.crossOrigin=n,t.referrerPolicy=r,t.src=o,a&&(t.srcset=a),()=>{e=!1}}),[n,r,o,a]),i}((0,o.A)({},f,{src:h,srcSet:g})),w=h||g,A=w&&"error"!==x,k=(0,o.A)({},r,{colorDefault:!A,component:u,variant:v}),C=(e=>{const{classes:t,variant:n,colorDefault:r}=e,o={root:["root",n,r&&"colorDefault"],img:["img"],fallback:["fallback"]};return(0,U.A)(o,il,t)})(k),[E,R]=ul("img",{className:C.img,elementType:fl,externalForwardedProps:{slots:d,slotProps:{img:(0,o.A)({},f,p.img)}},additionalProps:{alt:i,src:h,srcSet:g,sizes:m},ownerState:k});return b=A?(0,c.jsx)(E,(0,o.A)({},R)):l||0===l?l:w&&i?i[0]:(0,c.jsx)(ml,{ownerState:k,className:C.fallback}),(0,c.jsx)(pl,(0,o.A)({as:u,ownerState:k,className:(0,$.A)(C.root,s),ref:n},y,{children:b}))}));function gl(e){return(0,Ae.Ay)("MuiListItemText",e)}const vl=(0,W.A)("MuiListItemText",["root","multiline","dense","inset","primary","secondary"]),yl=["children","className","disableTypography","inset","primary","primaryTypographyProps","secondary","secondaryTypographyProps"],bl=(0,K.Ay)("div",{name:"MuiListItemText",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[{[`& .${vl.primary}`]:t.primary},{[`& .${vl.secondary}`]:t.secondary},t.root,n.inset&&t.inset,n.primary&&n.secondary&&t.multiline,n.dense&&t.dense]}})((e=>{let{ownerState:t}=e;return(0,o.A)({flex:"1 1 auto",minWidth:0,marginTop:4,marginBottom:4},t.primary&&t.secondary&&{marginTop:6,marginBottom:6},t.inset&&{paddingLeft:56})})),xl=e.forwardRef((function(t,n){const r=(0,S.b)({props:t,name:"MuiListItemText"}),{children:i,className:l,disableTypography:s=!1,inset:u=!1,primary:d,primaryTypographyProps:p,secondary:f,secondaryTypographyProps:m}=r,h=(0,a.A)(r,yl),{dense:g}=e.useContext(na);let v=null!=d?d:i,y=f;const b=(0,o.A)({},r,{disableTypography:s,inset:u,primary:!!v,secondary:!!y,dense:g}),x=(e=>{const{classes:t,inset:n,primary:r,secondary:o,dense:a}=e,i={root:["root",n&&"inset",a&&"dense",r&&o&&"multiline"],primary:["primary"],secondary:["secondary"]};return(0,U.A)(i,gl,t)})(b);return null==v||v.type===xt||s||(v=(0,c.jsx)(xt,(0,o.A)({variant:g?"body2":"body1",className:x.primary,component:null!=p&&p.variant?void 0:"span",display:"block"},p,{children:v}))),null==y||y.type===xt||s||(y=(0,c.jsx)(xt,(0,o.A)({variant:"body2",className:x.secondary,color:"text.secondary",display:"block"},m,{children:y}))),(0,c.jsxs)(bl,(0,o.A)({className:(0,$.A)(x.root,l),ownerState:b,ref:n},h,{children:[v,y]}))}));var wl=n(9751),Al=n(8604);const kl=["component","direction","spacing","divider","children","className","useFlexGap"],Sl=(0,E.A)(),Cl=ot("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,t)=>t.root});function El(e){return Ke({props:e,name:"MuiStack",defaultTheme:Sl})}function Rl(t,n){const r=e.Children.toArray(t).filter(Boolean);return r.reduce(((t,o,a)=>(t.push(o),a<r.length-1&&t.push(e.cloneElement(n,{key:`separator-${a}`})),t)),[])}const Pl=e=>{let{ownerState:t,theme:n}=e,r=(0,o.A)({display:"flex",flexDirection:"column"},(0,wl.NI)({theme:n},(0,wl.kW)({values:t.direction,breakpoints:n.breakpoints.values}),(e=>({flexDirection:e}))));if(t.spacing){const e=(0,Al.LX)(n),o=Object.keys(n.breakpoints.values).reduce(((e,n)=>(("object"===typeof t.spacing&&null!=t.spacing[n]||"object"===typeof t.direction&&null!=t.direction[n])&&(e[n]=!0),e)),{}),a=(0,wl.kW)({values:t.direction,base:o}),i=(0,wl.kW)({values:t.spacing,base:o});"object"===typeof a&&Object.keys(a).forEach(((e,t,n)=>{if(!a[e]){const r=t>0?a[n[t-1]]:"column";a[e]=r}}));const l=(n,r)=>{return t.useFlexGap?{gap:(0,Al._W)(e,n)}:{"& > :not(style):not(style)":{margin:0},"& > :not(style) ~ :not(style)":{[`margin${o=r?a[r]:t.direction,{row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"}[o]}`]:(0,Al._W)(e,n)}};var o};r=(0,qe.A)(r,(0,wl.NI)({theme:n},i,l))}return r=(0,wl.iZ)(n.breakpoints,r),r};const Ml=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{createStyledComponent:n=Cl,useThemeProps:r=El,componentName:i="MuiStack"}=t,l=n(Pl);return e.forwardRef((function(e,t){const n=r(e),s=(0,_.A)(n),{component:u="div",direction:d="column",spacing:p=0,divider:f,children:m,className:h,useFlexGap:g=!1}=s,v=(0,a.A)(s,kl),y={direction:d,spacing:p,useFlexGap:g},b=(0,U.A)({root:["root"]},(e=>(0,Ae.Ay)(i,e)),{});return(0,c.jsx)(l,(0,o.A)({as:u,ownerState:y,ref:t,className:(0,$.A)(b.root,h)},v,{children:f?Rl(m,f):m}))}))}({createStyledComponent:(0,K.Ay)("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,t)=>t.root}),useThemeProps:e=>(0,S.b)({props:e,name:"MuiStack"})});const Ol=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{autoHideDuration:n=null,disableWindowBlurListener:r=!1,onClose:a,open:i,resumeHideDuration:l}=t,s=(0,le.A)();e.useEffect((()=>{if(i)return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)};function e(e){e.defaultPrevented||"Escape"!==e.key&&"Esc"!==e.key||null==a||a(e,"escapeKeyDown")}}),[i,a]);const c=(0,Pa.A)(((e,t)=>{null==a||a(e,t)})),u=(0,Pa.A)((e=>{a&&null!=e&&s.start(e,(()=>{c(null,"timeout")}))}));e.useEffect((()=>(i&&u(n),s.clear)),[i,n,u,s]);const d=s.clear,p=e.useCallback((()=>{null!=n&&u(null!=l?l:.5*n)}),[n,l,u]),f=e=>t=>{const n=e.onFocus;null==n||n(t),d()},m=e=>t=>{const n=e.onMouseEnter;null==n||n(t),d()},h=e=>t=>{const n=e.onMouseLeave;null==n||n(t),p()};return e.useEffect((()=>{if(!r&&i)return window.addEventListener("focus",p),window.addEventListener("blur",d),()=>{window.removeEventListener("focus",p),window.removeEventListener("blur",d)}}),[r,i,p,d]),{getRootProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const n=(0,o.A)({},rr(t),rr(e));return(0,o.A)({role:"presentation"},e,n,{onBlur:(r=n,e=>{const t=r.onBlur;null==t||t(e),p()}),onFocus:f(n),onMouseEnter:m(n),onMouseLeave:h(n)});var r},onClickAway:e=>{null==a||a(e,"clickaway")}}};function Tl(e){return e.substring(2).toLowerCase()}function jl(t){const{children:n,disableReactTree:r=!1,mouseEvent:o="onClick",onClickAway:a,touchEvent:i="onTouchEnd"}=t,l=e.useRef(!1),s=e.useRef(null),u=e.useRef(!1),d=e.useRef(!1);e.useEffect((()=>(setTimeout((()=>{u.current=!0}),0),()=>{u.current=!1})),[]);const p=(0,Vt.A)(n.ref,s),f=(0,Pa.A)((e=>{const t=d.current;d.current=!1;const n=(0,Ut.A)(s.current);if(!u.current||!s.current||"clientX"in e&&function(e,t){return t.documentElement.clientWidth<e.clientX||t.documentElement.clientHeight<e.clientY}(e,n))return;if(l.current)return void(l.current=!1);let o;o=e.composedPath?e.composedPath().indexOf(s.current)>-1:!n.documentElement.contains(e.target)||s.current.contains(e.target),o||!r&&t||a(e)})),m=e=>t=>{d.current=!0;const r=n.props[e];r&&r(t)},h={ref:p};return!1!==i&&(h[i]=m(i)),e.useEffect((()=>{if(!1!==i){const e=Tl(i),t=(0,Ut.A)(s.current),n=()=>{l.current=!0};return t.addEventListener(e,f),t.addEventListener("touchmove",n),()=>{t.removeEventListener(e,f),t.removeEventListener("touchmove",n)}}}),[f,i]),!1!==o&&(h[o]=m(o)),e.useEffect((()=>{if(!1!==o){const e=Tl(o),t=(0,Ut.A)(s.current);return t.addEventListener(e,f),()=>{t.removeEventListener(e,f)}}}),[f,o]),(0,c.jsx)(e.Fragment,{children:e.cloneElement(n,h)})}function Il(e){return(0,Ae.Ay)("MuiSnackbarContent",e)}(0,W.A)("MuiSnackbarContent",["root","message","action"]);const Nl=["action","className","message","role"],$l=(0,K.Ay)(mt,{name:"MuiSnackbarContent",slot:"Root",overridesResolver:(e,t)=>t.root})((e=>{let{theme:t}=e;const n="light"===t.palette.mode?.8:.98,r=(0,X.tL)(t.palette.background.default,n);return(0,o.A)({},t.typography.body2,{color:t.vars?t.vars.palette.SnackbarContent.color:t.palette.getContrastText(r),backgroundColor:t.vars?t.vars.palette.SnackbarContent.bg:r,display:"flex",alignItems:"center",flexWrap:"wrap",padding:"6px 16px",borderRadius:(t.vars||t).shape.borderRadius,flexGrow:1,[t.breakpoints.up("sm")]:{flexGrow:"initial",minWidth:288}})})),zl=(0,K.Ay)("div",{name:"MuiSnackbarContent",slot:"Message",overridesResolver:(e,t)=>t.message})({padding:"8px 0"}),Ll=(0,K.Ay)("div",{name:"MuiSnackbarContent",slot:"Action",overridesResolver:(e,t)=>t.action})({display:"flex",alignItems:"center",marginLeft:"auto",paddingLeft:16,marginRight:-8}),_l=e.forwardRef((function(e,t){const n=(0,S.b)({props:e,name:"MuiSnackbarContent"}),{action:r,className:i,message:l,role:s="alert"}=n,u=(0,a.A)(n,Nl),d=n,p=(e=>{const{classes:t}=e;return(0,U.A)({root:["root"],action:["action"],message:["message"]},Il,t)})(d);return(0,c.jsxs)($l,(0,o.A)({role:s,square:!0,elevation:6,className:(0,$.A)(p.root,i),ownerState:d,ref:t},u,{children:[(0,c.jsx)(zl,{className:p.message,ownerState:d,children:l}),r?(0,c.jsx)(Ll,{className:p.action,ownerState:d,children:r}):null]}))}));function Fl(e){return(0,Ae.Ay)("MuiSnackbar",e)}(0,W.A)("MuiSnackbar",["root","anchorOriginTopCenter","anchorOriginBottomCenter","anchorOriginTopRight","anchorOriginBottomRight","anchorOriginTopLeft","anchorOriginBottomLeft"]);const Dl=["onEnter","onExited"],Wl=["action","anchorOrigin","autoHideDuration","children","className","ClickAwayListenerProps","ContentProps","disableWindowBlurListener","message","onBlur","onClose","onFocus","onMouseEnter","onMouseLeave","open","resumeHideDuration","TransitionComponent","transitionDuration","TransitionProps"],Bl=(0,K.Ay)("div",{name:"MuiSnackbar",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[`anchorOrigin${(0,Pe.A)(n.anchorOrigin.vertical)}${(0,Pe.A)(n.anchorOrigin.horizontal)}`]]}})((e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({zIndex:(t.vars||t).zIndex.snackbar,position:"fixed",display:"flex",left:8,right:8,justifyContent:"center",alignItems:"center"},"top"===n.anchorOrigin.vertical?{top:8}:{bottom:8},"left"===n.anchorOrigin.horizontal&&{justifyContent:"flex-start"},"right"===n.anchorOrigin.horizontal&&{justifyContent:"flex-end"},{[t.breakpoints.up("sm")]:(0,o.A)({},"top"===n.anchorOrigin.vertical?{top:24}:{bottom:24},"center"===n.anchorOrigin.horizontal&&{left:"50%",right:"auto",transform:"translateX(-50%)"},"left"===n.anchorOrigin.horizontal&&{left:24,right:"auto"},"right"===n.anchorOrigin.horizontal&&{right:24,left:"auto"})})})),Vl=e.forwardRef((function(t,n){const r=(0,S.b)({props:t,name:"MuiSnackbar"}),i=kt(),l={enter:i.transitions.duration.enteringScreen,exit:i.transitions.duration.leavingScreen},{action:s,anchorOrigin:{vertical:u,horizontal:d}={vertical:"bottom",horizontal:"left"},autoHideDuration:p=null,children:f,className:m,ClickAwayListenerProps:h,ContentProps:g,disableWindowBlurListener:v=!1,message:y,open:b,TransitionComponent:x=Wt,transitionDuration:w=l,TransitionProps:{onEnter:A,onExited:k}={}}=r,C=(0,a.A)(r.TransitionProps,Dl),E=(0,a.A)(r,Wl),R=(0,o.A)({},r,{anchorOrigin:{vertical:u,horizontal:d},autoHideDuration:p,disableWindowBlurListener:v,TransitionComponent:x,transitionDuration:w}),P=(e=>{const{classes:t,anchorOrigin:n}=e,r={root:["root",`anchorOrigin${(0,Pe.A)(n.vertical)}${(0,Pe.A)(n.horizontal)}`]};return(0,U.A)(r,Fl,t)})(R),{getRootProps:M,onClickAway:O}=Ol((0,o.A)({},R)),[T,j]=e.useState(!0),I=sr({elementType:Bl,getSlotProps:M,externalForwardedProps:E,ownerState:R,additionalProps:{ref:n},className:[P.root,m]});return!b&&T?null:(0,c.jsx)(jl,(0,o.A)({onClickAway:O},h,{children:(0,c.jsx)(Bl,(0,o.A)({},I,{children:(0,c.jsx)(x,(0,o.A)({appear:!0,in:b,timeout:w,direction:"top"===u?"down":"up",onEnter:(e,t)=>{j(!1),A&&A(e,t)},onExited:e=>{j(!0),k&&k(e)}},C,{children:f||(0,c.jsx)(_l,(0,o.A)({message:y,action:s},g))}))}))}))}));function Hl(e){return(0,Ae.Ay)("MuiAlert",e)}const Ul=(0,W.A)("MuiAlert",["root","action","icon","message","filled","colorSuccess","colorInfo","colorWarning","colorError","filledSuccess","filledInfo","filledWarning","filledError","outlined","outlinedSuccess","outlinedInfo","outlinedWarning","outlinedError","standard","standardSuccess","standardInfo","standardWarning","standardError"]),Xl=(0,Ri.A)((0,c.jsx)("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"}),"SuccessOutlined"),Kl=(0,Ri.A)((0,c.jsx)("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"}),"ReportProblemOutlined"),ql=(0,Ri.A)((0,c.jsx)("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"}),"ErrorOutline"),Gl=(0,Ri.A)((0,c.jsx)("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"}),"InfoOutlined"),Ql=(0,Ri.A)((0,c.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close"),Yl=["action","children","className","closeText","color","components","componentsProps","icon","iconMapping","onClose","role","severity","slotProps","slots","variant"],Jl=(0,K.Ay)(mt,{name:"MuiAlert",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[n.variant],t[`${n.variant}${(0,Pe.A)(n.color||n.severity)}`]]}})((e=>{let{theme:t}=e;const n="light"===t.palette.mode?X.e$:X.a,r="light"===t.palette.mode?X.a:X.e$;return(0,o.A)({},t.typography.body2,{backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(t.palette).filter((e=>{let[,t]=e;return t.main&&t.light})).map((e=>{let[o]=e;return{props:{colorSeverity:o,variant:"standard"},style:{color:t.vars?t.vars.palette.Alert[`${o}Color`]:n(t.palette[o].light,.6),backgroundColor:t.vars?t.vars.palette.Alert[`${o}StandardBg`]:r(t.palette[o].light,.9),[`& .${Ul.icon}`]:t.vars?{color:t.vars.palette.Alert[`${o}IconColor`]}:{color:t.palette[o].main}}}})),...Object.entries(t.palette).filter((e=>{let[,t]=e;return t.main&&t.light})).map((e=>{let[r]=e;return{props:{colorSeverity:r,variant:"outlined"},style:{color:t.vars?t.vars.palette.Alert[`${r}Color`]:n(t.palette[r].light,.6),border:`1px solid ${(t.vars||t).palette[r].light}`,[`& .${Ul.icon}`]:t.vars?{color:t.vars.palette.Alert[`${r}IconColor`]}:{color:t.palette[r].main}}}})),...Object.entries(t.palette).filter((e=>{let[,t]=e;return t.main&&t.dark})).map((e=>{let[n]=e;return{props:{colorSeverity:n,variant:"filled"},style:(0,o.A)({fontWeight:t.typography.fontWeightMedium},t.vars?{color:t.vars.palette.Alert[`${n}FilledColor`],backgroundColor:t.vars.palette.Alert[`${n}FilledBg`]}:{backgroundColor:"dark"===t.palette.mode?t.palette[n].dark:t.palette[n].main,color:t.palette.getContrastText(t.palette[n].main)})}}))]})})),Zl=(0,K.Ay)("div",{name:"MuiAlert",slot:"Icon",overridesResolver:(e,t)=>t.icon})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),es=(0,K.Ay)("div",{name:"MuiAlert",slot:"Message",overridesResolver:(e,t)=>t.message})({padding:"8px 0",minWidth:0,overflow:"auto"}),ts=(0,K.Ay)("div",{name:"MuiAlert",slot:"Action",overridesResolver:(e,t)=>t.action})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),ns={success:(0,c.jsx)(Xl,{fontSize:"inherit"}),warning:(0,c.jsx)(Kl,{fontSize:"inherit"}),error:(0,c.jsx)(ql,{fontSize:"inherit"}),info:(0,c.jsx)(Gl,{fontSize:"inherit"})},rs=e.forwardRef((function(e,t){const n=(0,S.b)({props:e,name:"MuiAlert"}),{action:r,children:i,className:l,closeText:s="Close",color:u,components:d={},componentsProps:p={},icon:f,iconMapping:m=ns,onClose:h,role:g="alert",severity:v="success",slotProps:y={},slots:b={},variant:x="standard"}=n,w=(0,a.A)(n,Yl),A=(0,o.A)({},n,{color:u,severity:v,variant:x,colorSeverity:u||v}),k=(e=>{const{variant:t,color:n,severity:r,classes:o}=e,a={root:["root",`color${(0,Pe.A)(n||r)}`,`${t}${(0,Pe.A)(n||r)}`,`${t}`],icon:["icon"],message:["message"],action:["action"]};return(0,U.A)(a,Hl,o)})(A),C={slots:(0,o.A)({closeButton:d.CloseButton,closeIcon:d.CloseIcon},b),slotProps:(0,o.A)({},p,y)},[E,R]=ul("closeButton",{elementType:Ie,externalForwardedProps:C,ownerState:A}),[P,M]=ul("closeIcon",{elementType:Ql,externalForwardedProps:C,ownerState:A});return(0,c.jsxs)(Jl,(0,o.A)({role:g,elevation:0,ownerState:A,className:(0,$.A)(k.root,l),ref:t},w,{children:[!1!==f?(0,c.jsx)(Zl,{ownerState:A,className:k.icon,children:f||m[v]||ns[v]}):null,(0,c.jsx)(es,{ownerState:A,className:k.message,children:i}),null!=r?(0,c.jsx)(ts,{ownerState:A,className:k.action,children:r}):null,null==r&&h?(0,c.jsx)(ts,{ownerState:A,className:k.action,children:(0,c.jsx)(E,(0,o.A)({size:"small","aria-label":s,title:s,color:"inherit",onClick:h},R,{children:(0,c.jsx)(P,(0,o.A)({fontSize:"small"},M))}))}):null]}))}));function os(e){return(0,Ae.Ay)("MuiDialog",e)}const as=(0,W.A)("MuiDialog",["root","scrollPaper","scrollBody","container","paper","paperScrollPaper","paperScrollBody","paperWidthFalse","paperWidthXs","paperWidthSm","paperWidthMd","paperWidthLg","paperWidthXl","paperFullWidth","paperFullScreen"]);const is=e.createContext({}),ls=["aria-describedby","aria-labelledby","BackdropComponent","BackdropProps","children","className","disableEscapeKeyDown","fullScreen","fullWidth","maxWidth","onBackdropClick","onClick","onClose","open","PaperComponent","PaperProps","scroll","TransitionComponent","transitionDuration","TransitionProps"],ss=(0,K.Ay)(Ra,{name:"MuiDialog",slot:"Backdrop",overrides:(e,t)=>t.backdrop})({zIndex:-1}),cs=(0,K.Ay)(Wa,{name:"MuiDialog",slot:"Root",overridesResolver:(e,t)=>t.root})({"@media print":{position:"absolute !important"}}),us=(0,K.Ay)("div",{name:"MuiDialog",slot:"Container",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.container,t[`scroll${(0,Pe.A)(n.scroll)}`]]}})((e=>{let{ownerState:t}=e;return(0,o.A)({height:"100%","@media print":{height:"auto"},outline:0},"paper"===t.scroll&&{display:"flex",justifyContent:"center",alignItems:"center"},"body"===t.scroll&&{overflowY:"auto",overflowX:"hidden",textAlign:"center","&::after":{content:'""',display:"inline-block",verticalAlign:"middle",height:"100%",width:"0"}})})),ds=(0,K.Ay)(mt,{name:"MuiDialog",slot:"Paper",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.paper,t[`scrollPaper${(0,Pe.A)(n.scroll)}`],t[`paperWidth${(0,Pe.A)(String(n.maxWidth))}`],n.fullWidth&&t.paperFullWidth,n.fullScreen&&t.paperFullScreen]}})((e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({margin:32,position:"relative",overflowY:"auto","@media print":{overflowY:"visible",boxShadow:"none"}},"paper"===n.scroll&&{display:"flex",flexDirection:"column",maxHeight:"calc(100% - 64px)"},"body"===n.scroll&&{display:"inline-block",verticalAlign:"middle",textAlign:"left"},!n.maxWidth&&{maxWidth:"calc(100% - 64px)"},"xs"===n.maxWidth&&{maxWidth:"px"===t.breakpoints.unit?Math.max(t.breakpoints.values.xs,444):`max(${t.breakpoints.values.xs}${t.breakpoints.unit}, 444px)`,[`&.${as.paperScrollBody}`]:{[t.breakpoints.down(Math.max(t.breakpoints.values.xs,444)+64)]:{maxWidth:"calc(100% - 64px)"}}},n.maxWidth&&"xs"!==n.maxWidth&&{maxWidth:`${t.breakpoints.values[n.maxWidth]}${t.breakpoints.unit}`,[`&.${as.paperScrollBody}`]:{[t.breakpoints.down(t.breakpoints.values[n.maxWidth]+64)]:{maxWidth:"calc(100% - 64px)"}}},n.fullWidth&&{width:"calc(100% - 64px)"},n.fullScreen&&{margin:0,width:"100%",maxWidth:"100%",height:"100%",maxHeight:"none",borderRadius:0,[`&.${as.paperScrollBody}`]:{margin:0,maxWidth:"100%"}})})),ps=e.forwardRef((function(t,n){const r=(0,S.b)({props:t,name:"MuiDialog"}),i=kt(),l={enter:i.transitions.duration.enteringScreen,exit:i.transitions.duration.leavingScreen},{"aria-describedby":s,"aria-labelledby":u,BackdropComponent:d,BackdropProps:p,children:f,className:m,disableEscapeKeyDown:h=!1,fullScreen:g=!1,fullWidth:v=!1,maxWidth:y="sm",onBackdropClick:b,onClick:x,onClose:w,open:A,PaperComponent:k=mt,PaperProps:C={},scroll:E="paper",TransitionComponent:R=ka,transitionDuration:P=l,TransitionProps:M}=r,O=(0,a.A)(r,ls),T=(0,o.A)({},r,{disableEscapeKeyDown:h,fullScreen:g,fullWidth:v,maxWidth:y,scroll:E}),j=(e=>{const{classes:t,scroll:n,maxWidth:r,fullWidth:o,fullScreen:a}=e,i={root:["root"],container:["container",`scroll${(0,Pe.A)(n)}`],paper:["paper",`paperScroll${(0,Pe.A)(n)}`,`paperWidth${(0,Pe.A)(String(r))}`,o&&"paperFullWidth",a&&"paperFullScreen"]};return(0,U.A)(i,os,t)})(T),I=e.useRef(),N=(0,_r.A)(u),z=e.useMemo((()=>({titleId:N})),[N]);return(0,c.jsx)(cs,(0,o.A)({className:(0,$.A)(j.root,m),closeAfterTransition:!0,components:{Backdrop:ss},componentsProps:{backdrop:(0,o.A)({transitionDuration:P,as:d},p)},disableEscapeKeyDown:h,onClose:w,open:A,ref:n,onClick:e=>{x&&x(e),I.current&&(I.current=null,b&&b(e),w&&w(e,"backdropClick"))},ownerState:T},O,{children:(0,c.jsx)(R,(0,o.A)({appear:!0,in:A,timeout:P,role:"presentation"},M,{children:(0,c.jsx)(us,{className:(0,$.A)(j.container),onMouseDown:e=>{I.current=e.target===e.currentTarget},ownerState:T,children:(0,c.jsx)(ds,(0,o.A)({as:k,elevation:24,role:"dialog","aria-describedby":s,"aria-labelledby":N},C,{className:(0,$.A)(j.paper,C.className),ownerState:T,children:(0,c.jsx)(is.Provider,{value:z,children:f})}))})}))}))}));function fs(e){return(0,Ae.Ay)("MuiDialogTitle",e)}const ms=(0,W.A)("MuiDialogTitle",["root"]),hs=["className","id"],gs=(0,K.Ay)(xt,{name:"MuiDialogTitle",slot:"Root",overridesResolver:(e,t)=>t.root})({padding:"16px 24px",flex:"0 0 auto"}),vs=e.forwardRef((function(t,n){const r=(0,S.b)({props:t,name:"MuiDialogTitle"}),{className:i,id:l}=r,s=(0,a.A)(r,hs),u=r,d=(e=>{const{classes:t}=e;return(0,U.A)({root:["root"]},fs,t)})(u),{titleId:p=l}=e.useContext(is);return(0,c.jsx)(gs,(0,o.A)({component:"h2",className:(0,$.A)(d.root,i),ownerState:u,ref:n,variant:"h6",id:null!=l?l:p},s))}));function ys(e){return(0,Ae.Ay)("MuiDialogContent",e)}(0,W.A)("MuiDialogContent",["root","dividers"]);const bs=["className","dividers"],xs=(0,K.Ay)("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.dividers&&t.dividers]}})((e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px"},n.dividers?{padding:"16px 24px",borderTop:`1px solid ${(t.vars||t).palette.divider}`,borderBottom:`1px solid ${(t.vars||t).palette.divider}`}:{[`.${ms.root} + &`]:{paddingTop:0}})})),ws=e.forwardRef((function(e,t){const n=(0,S.b)({props:e,name:"MuiDialogContent"}),{className:r,dividers:i=!1}=n,l=(0,a.A)(n,bs),s=(0,o.A)({},n,{dividers:i}),u=(e=>{const{classes:t,dividers:n}=e,r={root:["root",n&&"dividers"]};return(0,U.A)(r,ys,t)})(s);return(0,c.jsx)(xs,(0,o.A)({className:(0,$.A)(u.root,r),ownerState:s,ref:t},l))}));function As(e){return(0,Ae.Ay)("MuiDialogActions",e)}(0,W.A)("MuiDialogActions",["root","spacing"]);const ks=["className","disableSpacing"],Ss=(0,K.Ay)("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,!n.disableSpacing&&t.spacing]}})((e=>{let{ownerState:t}=e;return(0,o.A)({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto"},!t.disableSpacing&&{"& > :not(style) ~ :not(style)":{marginLeft:8}})})),Cs=e.forwardRef((function(e,t){const n=(0,S.b)({props:e,name:"MuiDialogActions"}),{className:r,disableSpacing:i=!1}=n,l=(0,a.A)(n,ks),s=(0,o.A)({},n,{disableSpacing:i}),u=(e=>{const{classes:t,disableSpacing:n}=e,r={root:["root",!n&&"spacing"]};return(0,U.A)(r,As,t)})(s);return(0,c.jsx)(Ss,(0,o.A)({className:(0,$.A)(u.root,r),ownerState:s,ref:t},l))}));var Es=n(9810),Rs=n(1958),Ps=n(7201),Ms=n(3471);window.location.hostname;const Os="http://*************:5000";const Ts=function(t){let{books:n,onSelectBook:r,selectedBooks:o=[],onDeleteBook:a}=t;const[i,l]=(0,e.useState)(""),[s,u]=(0,e.useState)(!1),[d,p]=(0,e.useState)(""),[f,m]=(0,e.useState)(!1),[h,g]=(0,e.useState)([]),[v,y]=(0,e.useState)(!1),b=(e,t)=>{"clickaway"!==t&&u(!1)},x=n.filter((e=>{var t,n;return((null===(t=e.title)||void 0===t?void 0:t.toLowerCase())||"").includes(i.toLowerCase())||((null===(n=e.author)||void 0===n?void 0:n.toLowerCase())||"").includes(i.toLowerCase())}));return n.length?(0,c.jsxs)(H,{children:[(0,c.jsx)(Wi,{fullWidth:!0,variant:"outlined",placeholder:"Search books...",value:i,onChange:e=>l(e.target.value),sx:{mb:2}}),(0,c.jsx)(ia,{children:x.map((e=>(0,c.jsxs)(el,{sx:{mb:1,bgcolor:"background.paper",borderRadius:1},children:[(0,c.jsx)(ol,{children:(0,c.jsx)(hl,{children:(0,c.jsx)(Es.A,{})})}),(0,c.jsx)(xl,{primary:e.title,secondary:e.author}),(0,c.jsxs)(Ml,{direction:"row",spacing:1,children:[(0,c.jsx)(Ie,{onClick:()=>(async e=>{y(!0);try{const t=await fetch(`${Os}/api/chat/history?book_id=${e}`,{headers:{"X-User-ID":"1"}});if(!t.ok)throw new Error("Failed to fetch chat history");const n=await t.json();g(n.messages||[]),m(!0)}catch(t){console.error("Error fetching chat history:",t),u(!0),p("Error loading chat history")}finally{y(!1)}})(e.id),disabled:v,title:"View chat history",children:(0,c.jsx)(Rs.A,{})}),(0,c.jsx)(Ie,{onClick:()=>(async e=>{try{const t=n.find((t=>t.id===e));if(t){console.log("Adding book to chat:",t),await r(t),console.log("Successfully added book to chat"),p(t.title),u(!0);const e=document.querySelector('[data-section="chat"]');e&&(e.click(),setTimeout((()=>{const e=document.querySelector('input[placeholder*="Chat with"]');e&&e.focus()}),100))}}catch(t){console.error("Error adding book to chat:",t),u(!0),p("Error adding book to chat")}})(e.id),color:"primary",title:"Add to chat",children:(0,c.jsx)(Ps.A,{})}),(0,c.jsx)(Ie,{onClick:()=>(async e=>{try{await a(e)}catch(t){console.error("Error deleting book:",t)}})(e.id),color:"error",title:"Delete book",children:(0,c.jsx)(Ms.A,{})})]})]},e.id)))}),(0,c.jsx)(Vl,{open:s,autoHideDuration:3e3,onClose:b,children:(0,c.jsx)(rs,{onClose:b,severity:"info",children:d})}),(0,c.jsxs)(ps,{open:f,onClose:()=>m(!1),maxWidth:"md",fullWidth:!0,children:[(0,c.jsx)(vs,{children:"Chat History"}),(0,c.jsx)(ws,{children:0===h.length?(0,c.jsx)(xt,{color:"text.secondary",children:"No chat history available"}):(0,c.jsx)(ia,{children:h.map(((e,t)=>(0,c.jsx)(el,{children:(0,c.jsx)(xl,{primary:e.is_user?"You":e.character||"Assistant",secondary:e.message,sx:{"& .MuiListItemText-primary":{fontWeight:"bold",color:e.is_user?"primary.main":"secondary.main"}}})},t)))})}),(0,c.jsx)(Cs,{children:(0,c.jsx)(Ue,{onClick:()=>m(!1),children:"Close"})})]})]}):(0,c.jsx)(H,{sx:{p:2,textAlign:"center"},children:(0,c.jsx)(xt,{variant:"body1",color:"text.secondary",children:"Your library is empty. Add some books to get started!"})})};var js=n(2505),Is=n(5382),Ns=n(5540);const $s=function(t){var n,r;let{onAddBook:o,userId:a}=t;const[i,l]=(0,e.useState)({title:"",author:"",description:""}),[s,u]=(0,e.useState)(null),[d,p]=(0,e.useState)(!1),[f,m]=(0,e.useState)(!1),[h,g]=(0,e.useState)({title:"",author:""}),v=e=>{const{name:t,value:n}=e.target;g((e=>({...e,[t]:n})))},y=()=>{l({title:"",author:"",description:""}),u(null),g({title:"",author:""}),m(!1)},b=e=>{const{name:t,value:n}=e.target;l((e=>({...e,[t]:n})))};return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(H,{component:"form",onSubmit:async e=>{if(e.preventDefault(),a)try{const e=await fetch(`${Os}/api/books`,{method:"POST",headers:{"Content-Type":"application/json","X-User-ID":a},body:JSON.stringify({...i,use_ai_suggestions:!0})});if(e.ok){const t=await e.json();t.ai_changes&&(t.ai_changes.original_title||t.ai_changes.generated_author)?(u({...t,originalData:{...i}}),g({title:t.ai_changes.corrected_title||i.title,author:t.ai_changes.generated_author||i.author}),p(!0),m(!1)):(o(t),y())}else{const t=await e.json();console.error("Error adding book:",t.error)}}catch(t){console.error("Error adding book:",t)}else console.error("No user selected")},children:(0,c.jsxs)(Ml,{spacing:2,children:[(0,c.jsx)(Wi,{required:!0,fullWidth:!0,label:"Book Title",name:"title",value:i.title,onChange:b}),(0,c.jsx)(Wi,{fullWidth:!0,label:"Author",name:"author",value:i.author,onChange:b}),(0,c.jsx)(Ue,{type:"submit",variant:"contained",color:"secondary",startIcon:(0,c.jsx)(js.A,{}),sx:{mt:2},children:"Add Book"})]})}),(0,c.jsxs)(ps,{open:d,onClose:()=>p(!1),maxWidth:"sm",fullWidth:!0,children:[(0,c.jsx)(vs,{children:"Review AI-Assisted Changes"}),(0,c.jsx)(ws,{children:f?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(xt,{variant:"body1",sx:{mb:3},children:"Edit the book details manually:"}),(0,c.jsxs)(Ml,{spacing:3,children:[(0,c.jsx)(Wi,{fullWidth:!0,label:"Book Title",name:"title",value:h.title,onChange:v,variant:"outlined"}),(0,c.jsx)(Wi,{fullWidth:!0,label:"Author",name:"author",value:h.author,onChange:v,variant:"outlined"})]})]}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(xt,{variant:"body1",sx:{mb:2},children:"Our AI assistant has suggested the following changes to ensure accuracy:"}),(null===s||void 0===s||null===(n=s.ai_changes)||void 0===n?void 0:n.original_title)&&(0,c.jsxs)(rs,{severity:"info",sx:{mb:2},children:[(0,c.jsx)(xt,{variant:"subtitle1",sx:{mb:1,fontWeight:"bold"},children:"Book Title Correction"}),(0,c.jsx)(xt,{variant:"body1",component:"div",children:(0,c.jsxs)(H,{sx:{display:"flex",flexDirection:"column",gap:1},children:[(0,c.jsxs)(H,{children:["You entered: ",(0,c.jsx)("strong",{children:s.ai_changes.original_title})]}),(0,c.jsxs)(H,{children:["Suggested correction: ",(0,c.jsx)("strong",{children:s.ai_changes.corrected_title})]})]})})]}),(null===s||void 0===s||null===(r=s.ai_changes)||void 0===r?void 0:r.generated_author)&&(0,c.jsxs)(rs,{severity:"info",children:[(0,c.jsx)(xt,{variant:"subtitle1",sx:{mb:1,fontWeight:"bold"},children:"Author Generation"}),(0,c.jsx)(xt,{variant:"body1",component:"div",children:(0,c.jsx)(H,{sx:{display:"flex",flexDirection:"column",gap:1},children:null===s.ai_changes.original_author?(0,c.jsxs)(H,{children:["Since no author was provided, our AI suggests: ",(0,c.jsx)("strong",{children:s.ai_changes.generated_author})]}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsxs)(H,{children:["You entered: ",(0,c.jsx)("strong",{children:s.ai_changes.original_author})]}),(0,c.jsxs)(H,{children:["AI suggests: ",(0,c.jsx)("strong",{children:s.ai_changes.generated_author})]})]})})})]}),(0,c.jsx)(xt,{sx:{mt:3,mb:2},children:"Would you like to use these suggestions, keep your original input, or make manual edits?"})]})}),(0,c.jsx)(Cs,{sx:{p:3,pt:2},children:f?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(Ue,{onClick:()=>m(!1),color:"secondary",variant:"outlined",children:"Back to Suggestions"}),(0,c.jsx)(Ue,{onClick:()=>{s&&fetch(`${Os}/api/books`,{method:"POST",headers:{"Content-Type":"application/json","X-User-ID":a},body:JSON.stringify({title:h.title,author:h.author,use_ai_suggestions:!1,original_id:s.id})}).then((e=>e.json())).then((e=>{o(e),p(!1),m(!1),y()})).catch((e=>{console.error("Error adding book with manual edits:",e)}))},color:"primary",variant:"contained",startIcon:(0,c.jsx)(Is.A,{}),children:"Save Changes"})]}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(Ue,{onClick:()=>{null!==s&&void 0!==s&&s.originalData&&fetch(`${Os}/api/books`,{method:"POST",headers:{"Content-Type":"application/json","X-User-ID":a},body:JSON.stringify({...s.originalData,use_ai_suggestions:!1,original_id:s.id})}).then((e=>e.json())).then((e=>{o(e),p(!1),y()})).catch((e=>{console.error("Error adding book with original data:",e)}))},color:"secondary",variant:"outlined",children:"Keep My Original Input"}),(0,c.jsx)(Ue,{onClick:()=>{m(!0)},color:"info",variant:"outlined",startIcon:(0,c.jsx)(Ns.A,{}),children:"Edit Manually"}),(0,c.jsx)(Ue,{onClick:()=>{if(s){console.log("Original confirmationData:",s);const e={title:s.ai_changes.corrected_title||s.originalData.title,author:s.ai_changes.generated_author||s.originalData.author,use_ai_suggestions:!0};console.log("Sending finalData to backend:",e),o(e),p(!1),y()}},color:"primary",variant:"contained",startIcon:(0,c.jsx)(Is.A,{}),children:"Accept Suggestions"})]})})]})]})};var zs=n(5678);const Ls=function(t){let{selectedBook:n,selectedCharacter:r,userId:o}=t;const[a,i]=(0,e.useState)([]),[l,s]=(0,e.useState)(""),[u,d]=(0,e.useState)(null),[p,f]=(0,e.useState)(!1),m=(Boolean(u),(0,e.useRef)(null)),[h,g]=(0,e.useState)(!1),[v,y]=(0,e.useState)(null);(0,e.useEffect)((()=>{let e=!0;return r&&o?(async()=>{if(o&&r){g(!0),y(null);try{const t=await fetch(`${Os}/api/chat/history?book_id=${(null===n||void 0===n?void 0:n.id)||""}`,{headers:{"X-User-ID":o}});if(!t.ok)throw new Error("Failed to fetch chat history");const r=await t.json();e&&i(r.messages.map((e=>({text:e.message,sender:e.is_user?"user":"assistant",timestamp:e.timestamp,character:e.character}))))}catch(v){console.error("Error fetching chat history:",v),e&&y("Failed to load chat history. Please try again.")}finally{e&&g(!1)}}})():i([]),()=>{e=!1}}),[r,o,n]),(0,e.useEffect)((()=>{m.current&&m.current.focus()}),[r]);const b=async e=>{if(e.preventDefault(),!l.trim()||!r||!o)return;const t=l.trim();s(""),i((e=>[...e,{text:t,sender:"user",timestamp:(new Date).toISOString(),character:r.name.toLowerCase()}]));try{const e=await fetch(`${Os}/api/chat`,{method:"POST",headers:{"Content-Type":"application/json","X-User-ID":o},body:JSON.stringify({message:t,character:r.name.toLowerCase(),book_id:null===n||void 0===n?void 0:n.id,bookContext:n?`The user is currently reading "${n.title}" by ${n.author}. Book description: ${n.description||"No description available."}`:null})});if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const a=await e.json();i((e=>[...e,{text:a.response,sender:"assistant",timestamp:(new Date).toISOString(),character:r.name.toLowerCase()}]))}catch(v){console.error("Error:",v),i((e=>[...e,{text:"Sorry, I encountered an error while processing your message.",sender:"system",timestamp:(new Date).toISOString()}]))}},x=()=>{f(!0)},w=()=>{f(!1)},A=()=>(0,c.jsxs)(mt,{elevation:1,sx:{p:2,mb:2,backgroundColor:"#f5f5f5",borderRadius:"8px",display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[(0,c.jsxs)(Ml,{spacing:1,children:[(0,c.jsx)(xt,{variant:"subtitle2",color:"text.secondary",children:"Current Book"}),n?(0,c.jsxs)(xt,{variant:"body2",children:[n.title," by ",n.author]}):(0,c.jsx)(xt,{variant:"body2",color:"text.disabled",children:"No book selected"}),(null===n||void 0===n?void 0:n.currentPage)&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(xt,{variant:"subtitle2",color:"text.secondary",sx:{mt:1},children:"Current Page Context"}),(0,c.jsxs)(xt,{variant:"body2",children:["Page ",n.currentPage]})]})]}),(0,c.jsx)(Ue,{variant:"outlined",size:"small",onClick:x,sx:{ml:2,minWidth:"auto",height:"fit-content",color:"text.secondary",borderColor:"divider","&:hover":{borderColor:"primary.main",color:"primary.main"}},children:"Clear History"})]});return(0,c.jsxs)(H,{sx:{height:"100%",display:"flex",flexDirection:"column"},children:[(0,c.jsx)(A,{}),(0,c.jsxs)(mt,{elevation:3,sx:{height:"70vh",display:"flex",flexDirection:"column",p:2,overflow:"hidden",backgroundColor:"#ffffff"},children:[(0,c.jsxs)(H,{sx:{flexGrow:1,overflowY:"auto",mb:2,"&::-webkit-scrollbar":{width:"8px"},"&::-webkit-scrollbar-track":{background:"#f1f1f1",borderRadius:"4px"},"&::-webkit-scrollbar-thumb":{background:"#C4A68A",borderRadius:"4px"},"&::-webkit-scrollbar-thumb:hover":{background:"#AB8B6E"}},children:[0===a.length&&r&&!h&&!v&&(0,c.jsxs)(H,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%",p:3},children:[(0,c.jsx)(hl,{src:r.avatar,alt:r.name,sx:{width:150,height:150,mb:2,boxShadow:"0px 4px 8px rgba(0, 0, 0, 0.1)"}}),(0,c.jsx)(xt,{variant:"h5",sx:{color:"text.primary",mb:1},children:r.name}),(0,c.jsx)(xt,{variant:"body1",sx:{color:"text.secondary",textAlign:"center"},children:"Start a conversation with your reading companion"})]}),h&&(0,c.jsx)(H,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,c.jsx)(xt,{variant:"body1",sx:{color:"text.secondary"},children:"Loading chat history..."})}),v&&(0,c.jsx)(H,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,c.jsx)(xt,{variant:"body1",sx:{color:"error.main"},children:v})}),a.map(((e,t)=>(0,c.jsxs)(H,{sx:{display:"flex",flexDirection:"user"===e.sender?"row-reverse":"row",alignItems:"flex-start",mb:3,pb:2,borderRadius:"8px",backgroundColor:"system"===e.sender?"rgba(0, 0, 0, 0.04)":"transparent",p:"system"===e.sender?2:0},children:["assistant"===e.sender&&(0,c.jsx)(hl,{src:(null===r||void 0===r?void 0:r.avatar)||`/avatars/${e.character}.png`,alt:(null===r||void 0===r?void 0:r.name)||e.character,sx:{mr:1,width:40,height:40}}),(0,c.jsxs)(mt,{sx:{p:2,maxWidth:"80%",backgroundColor:"assistant"===e.sender?"primary.light":"system"===e.sender?"primary.main":"secondary.light",color:"text.primary",boxShadow:"0px 2px 4px rgba(0, 0, 0, 0.05)",borderRadius:2,position:"relative"},children:["assistant"===e.sender&&(0,c.jsx)(xt,{variant:"subtitle2",sx:{mb:1,color:"text.secondary"},children:(null===r||void 0===r?void 0:r.name)||e.character}),(0,c.jsx)(xt,{variant:"body1",children:e.text})]})]},t)))]}),(0,c.jsx)(H,{component:"form",onSubmit:b,children:(0,c.jsx)(Ml,{direction:"row",spacing:2,children:(0,c.jsx)(Wi,{inputRef:m,autoFocus:!0,fullWidth:!0,variant:"outlined",placeholder:"Type your message...",value:l,onChange:e=>s(e.target.value),onKeyPress:e=>"Enter"===e.key&&b(e),InputProps:{endAdornment:(0,c.jsx)(Ie,{color:"primary",onClick:b,disabled:!l.trim()||!r||!o,children:(0,c.jsx)(zs.A,{})})},sx:{"& .MuiOutlinedInput-root":{borderRadius:"20px",backgroundColor:"#f5f5f5"}}})})})]}),(0,c.jsxs)(ps,{open:p,onClose:w,"aria-labelledby":"clear-history-dialog",children:[(0,c.jsx)(vs,{id:"clear-history-dialog",children:"Clear Chat History?"}),(0,c.jsx)(ws,{children:(0,c.jsx)(xt,{children:"Are you sure you want to clear all chat history? This action cannot be undone."})}),(0,c.jsxs)(Cs,{children:[(0,c.jsx)(Ue,{onClick:w,color:"primary",children:"Cancel"}),(0,c.jsx)(Ue,{onClick:async()=>{try{const e=await fetch(`${Os}/api/chat/clear`,{method:"DELETE",headers:{"Content-Type":"application/json","X-User-ID":o},body:JSON.stringify({character:r.name.toLowerCase()})});if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);i([]),f(!1)}catch(v){console.error("Error clearing chat history:",v)}},color:"primary",variant:"contained",sx:{backgroundColor:"primary.main","&:hover":{backgroundColor:"primary.dark"}},children:"Clear History"})]})]})]})};const _s=e.createContext();function Fs(e){return(0,Ae.Ay)("MuiGrid",e)}const Ds=["auto",!0,1,2,3,4,5,6,7,8,9,10,11,12],Ws=(0,W.A)("MuiGrid",["root","container","item","zeroMinWidth",...[0,1,2,3,4,5,6,7,8,9,10].map((e=>`spacing-xs-${e}`)),...["column-reverse","column","row-reverse","row"].map((e=>`direction-xs-${e}`)),...["nowrap","wrap-reverse","wrap"].map((e=>`wrap-xs-${e}`)),...Ds.map((e=>`grid-xs-${e}`)),...Ds.map((e=>`grid-sm-${e}`)),...Ds.map((e=>`grid-md-${e}`)),...Ds.map((e=>`grid-lg-${e}`)),...Ds.map((e=>`grid-xl-${e}`))]),Bs=["className","columns","columnSpacing","component","container","direction","item","rowSpacing","spacing","wrap","zeroMinWidth"];function Vs(e){const t=parseFloat(e);return`${t}${String(e).replace(String(t),"")||"px"}`}function Hs(e){let{breakpoints:t,values:n}=e,r="";Object.keys(n).forEach((e=>{""===r&&0!==n[e]&&(r=e)}));const o=Object.keys(t).sort(((e,n)=>t[e]-t[n]));return o.slice(0,o.indexOf(r))}const Us=(0,K.Ay)("div",{name:"MuiGrid",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e,{container:r,direction:o,item:a,spacing:i,wrap:l,zeroMinWidth:s,breakpoints:c}=n;let u=[];r&&(u=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!e||e<=0)return[];if("string"===typeof e&&!Number.isNaN(Number(e))||"number"===typeof e)return[n[`spacing-xs-${String(e)}`]];const r=[];return t.forEach((t=>{const o=e[t];Number(o)>0&&r.push(n[`spacing-${t}-${String(o)}`])})),r}(i,c,t));const d=[];return c.forEach((e=>{const r=n[e];r&&d.push(t[`grid-${e}-${String(r)}`])})),[t.root,r&&t.container,a&&t.item,s&&t.zeroMinWidth,...u,"row"!==o&&t[`direction-xs-${String(o)}`],"wrap"!==l&&t[`wrap-xs-${String(l)}`],...d]}})((e=>{let{ownerState:t}=e;return(0,o.A)({boxSizing:"border-box"},t.container&&{display:"flex",flexWrap:"wrap",width:"100%"},t.item&&{margin:0},t.zeroMinWidth&&{minWidth:0},"wrap"!==t.wrap&&{flexWrap:t.wrap})}),(function(e){let{theme:t,ownerState:n}=e;const r=(0,wl.kW)({values:n.direction,breakpoints:t.breakpoints.values});return(0,wl.NI)({theme:t},r,(e=>{const t={flexDirection:e};return 0===e.indexOf("column")&&(t[`& > .${Ws.item}`]={maxWidth:"none"}),t}))}),(function(e){let{theme:t,ownerState:n}=e;const{container:r,rowSpacing:o}=n;let a={};if(r&&0!==o){const e=(0,wl.kW)({values:o,breakpoints:t.breakpoints.values});let n;"object"===typeof e&&(n=Hs({breakpoints:t.breakpoints.values,values:e})),a=(0,wl.NI)({theme:t},e,((e,r)=>{var o;const a=t.spacing(e);return"0px"!==a?{marginTop:`-${Vs(a)}`,[`& > .${Ws.item}`]:{paddingTop:Vs(a)}}:null!=(o=n)&&o.includes(r)?{}:{marginTop:0,[`& > .${Ws.item}`]:{paddingTop:0}}}))}return a}),(function(e){let{theme:t,ownerState:n}=e;const{container:r,columnSpacing:o}=n;let a={};if(r&&0!==o){const e=(0,wl.kW)({values:o,breakpoints:t.breakpoints.values});let n;"object"===typeof e&&(n=Hs({breakpoints:t.breakpoints.values,values:e})),a=(0,wl.NI)({theme:t},e,((e,r)=>{var o;const a=t.spacing(e);return"0px"!==a?{width:`calc(100% + ${Vs(a)})`,marginLeft:`-${Vs(a)}`,[`& > .${Ws.item}`]:{paddingLeft:Vs(a)}}:null!=(o=n)&&o.includes(r)?{}:{width:"100%",marginLeft:0,[`& > .${Ws.item}`]:{paddingLeft:0}}}))}return a}),(function(e){let t,{theme:n,ownerState:r}=e;return n.breakpoints.keys.reduce(((e,a)=>{let i={};if(r[a]&&(t=r[a]),!t)return e;if(!0===t)i={flexBasis:0,flexGrow:1,maxWidth:"100%"};else if("auto"===t)i={flexBasis:"auto",flexGrow:0,flexShrink:0,maxWidth:"none",width:"auto"};else{const l=(0,wl.kW)({values:r.columns,breakpoints:n.breakpoints.values}),s="object"===typeof l?l[a]:l;if(void 0===s||null===s)return e;const c=Math.round(t/s*1e8)/1e6+"%";let u={};if(r.container&&r.item&&0!==r.columnSpacing){const e=n.spacing(r.columnSpacing);if("0px"!==e){const t=`calc(${c} + ${Vs(e)})`;u={flexBasis:t,maxWidth:t}}}i=(0,o.A)({flexBasis:c,flexGrow:0,maxWidth:c},u)}return 0===n.breakpoints.values[a]?Object.assign(e,i):e[n.breakpoints.up(a)]=i,e}),{})}));const Xs=e=>{const{classes:t,container:n,direction:r,item:o,spacing:a,wrap:i,zeroMinWidth:l,breakpoints:s}=e;let c=[];n&&(c=function(e,t){if(!e||e<=0)return[];if("string"===typeof e&&!Number.isNaN(Number(e))||"number"===typeof e)return[`spacing-xs-${String(e)}`];const n=[];return t.forEach((t=>{const r=e[t];if(Number(r)>0){const e=`spacing-${t}-${String(r)}`;n.push(e)}})),n}(a,s));const u=[];s.forEach((t=>{const n=e[t];n&&u.push(`grid-${t}-${String(n)}`)}));const d={root:["root",n&&"container",o&&"item",l&&"zeroMinWidth",...c,"row"!==r&&`direction-xs-${String(r)}`,"wrap"!==i&&`wrap-xs-${String(i)}`,...u]};return(0,U.A)(d,Fs,t)};const Ks=e.forwardRef((function(t,n){const r=(0,S.b)({props:t,name:"MuiGrid"}),{breakpoints:i}=kt(),l=(0,_.A)(r),{className:s,columns:u,columnSpacing:d,component:p="div",container:f=!1,direction:m="row",item:h=!1,rowSpacing:g,spacing:v=0,wrap:y="wrap",zeroMinWidth:b=!1}=l,x=(0,a.A)(l,Bs),w=g||v,A=d||v,k=e.useContext(_s),C=f?u||12:k,E={},R=(0,o.A)({},x);i.keys.forEach((e=>{null!=x[e]&&(E[e]=x[e],delete R[e])}));const P=(0,o.A)({},l,{columns:C,container:f,direction:m,item:h,rowSpacing:w,columnSpacing:A,wrap:y,zeroMinWidth:b,spacing:v},E,{breakpoints:i.keys}),M=Xs(P);return(0,c.jsx)(_s.Provider,{value:C,children:(0,c.jsx)(Us,(0,o.A)({ownerState:P,className:(0,$.A)(M.root,s),as:p,ref:n},R))})}));function qs(e){return(0,Ae.Ay)("MuiCard",e)}(0,W.A)("MuiCard",["root"]);const Gs=["className","raised"],Qs=(0,K.Ay)(mt,{name:"MuiCard",slot:"Root",overridesResolver:(e,t)=>t.root})((()=>({overflow:"hidden"}))),Ys=e.forwardRef((function(e,t){const n=(0,S.b)({props:e,name:"MuiCard"}),{className:r,raised:i=!1}=n,l=(0,a.A)(n,Gs),s=(0,o.A)({},n,{raised:i}),u=(e=>{const{classes:t}=e;return(0,U.A)({root:["root"]},qs,t)})(s);return(0,c.jsx)(Qs,(0,o.A)({className:(0,$.A)(u.root,r),elevation:i?8:void 0,ref:t,ownerState:s},l))}));function Js(e){return(0,Ae.Ay)("MuiCardActionArea",e)}const Zs=(0,W.A)("MuiCardActionArea",["root","focusVisible","focusHighlight"]),ec=["children","className","focusVisibleClassName"],tc=(0,K.Ay)(Re,{name:"MuiCardActionArea",slot:"Root",overridesResolver:(e,t)=>t.root})((e=>{let{theme:t}=e;return{display:"block",textAlign:"inherit",borderRadius:"inherit",width:"100%",[`&:hover .${Zs.focusHighlight}`]:{opacity:(t.vars||t).palette.action.hoverOpacity,"@media (hover: none)":{opacity:0}},[`&.${Zs.focusVisible} .${Zs.focusHighlight}`]:{opacity:(t.vars||t).palette.action.focusOpacity}}})),nc=(0,K.Ay)("span",{name:"MuiCardActionArea",slot:"FocusHighlight",overridesResolver:(e,t)=>t.focusHighlight})((e=>{let{theme:t}=e;return{overflow:"hidden",pointerEvents:"none",position:"absolute",top:0,right:0,bottom:0,left:0,borderRadius:"inherit",opacity:0,backgroundColor:"currentcolor",transition:t.transitions.create("opacity",{duration:t.transitions.duration.short})}})),rc=e.forwardRef((function(e,t){const n=(0,S.b)({props:e,name:"MuiCardActionArea"}),{children:r,className:i,focusVisibleClassName:l}=n,s=(0,a.A)(n,ec),u=n,d=(e=>{const{classes:t}=e;return(0,U.A)({root:["root"],focusHighlight:["focusHighlight"]},Js,t)})(u);return(0,c.jsxs)(tc,(0,o.A)({className:(0,$.A)(d.root,i),focusVisibleClassName:(0,$.A)(l,d.focusVisible),ref:t,ownerState:u},s,{children:[r,(0,c.jsx)(nc,{className:d.focusHighlight,ownerState:u})]}))}));function oc(e){return(0,Ae.Ay)("MuiCardMedia",e)}(0,W.A)("MuiCardMedia",["root","media","img"]);const ac=["children","className","component","image","src","style"],ic=(0,K.Ay)("div",{name:"MuiCardMedia",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e,{isMediaComponent:r,isImageComponent:o}=n;return[t.root,r&&t.media,o&&t.img]}})((e=>{let{ownerState:t}=e;return(0,o.A)({display:"block",backgroundSize:"cover",backgroundRepeat:"no-repeat",backgroundPosition:"center"},t.isMediaComponent&&{width:"100%"},t.isImageComponent&&{objectFit:"cover"})})),lc=["video","audio","picture","iframe","img"],sc=["picture","img"],cc=e.forwardRef((function(e,t){const n=(0,S.b)({props:e,name:"MuiCardMedia"}),{children:r,className:i,component:l="div",image:s,src:u,style:d}=n,p=(0,a.A)(n,ac),f=-1!==lc.indexOf(l),m=!f&&s?(0,o.A)({backgroundImage:`url("${s}")`},d):d,h=(0,o.A)({},n,{component:l,isMediaComponent:f,isImageComponent:-1!==sc.indexOf(l)}),g=(e=>{const{classes:t,isMediaComponent:n,isImageComponent:r}=e,o={root:["root",n&&"media",r&&"img"]};return(0,U.A)(o,oc,t)})(h);return(0,c.jsx)(ic,(0,o.A)({className:(0,$.A)(g.root,i),as:l,role:!f&&s?"img":void 0,ref:t,style:m,ownerState:h,src:f?s||u:void 0},p,{children:r}))}));function uc(e){return(0,Ae.Ay)("MuiCardContent",e)}(0,W.A)("MuiCardContent",["root"]);const dc=["className","component"],pc=(0,K.Ay)("div",{name:"MuiCardContent",slot:"Root",overridesResolver:(e,t)=>t.root})((()=>({padding:16,"&:last-child":{paddingBottom:24}}))),fc=e.forwardRef((function(e,t){const n=(0,S.b)({props:e,name:"MuiCardContent"}),{className:r,component:i="div"}=n,l=(0,a.A)(n,dc),s=(0,o.A)({},n,{component:i}),u=(e=>{const{classes:t}=e;return(0,U.A)({root:["root"]},uc,t)})(s);return(0,c.jsx)(pc,(0,o.A)({as:i,className:(0,$.A)(u.root,r),ownerState:s,ref:t},l))})),mc=(0,Ri.A)((0,c.jsx)("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"}),"Cancel");function hc(e){return(0,Ae.Ay)("MuiChip",e)}const gc=(0,W.A)("MuiChip",["root","sizeSmall","sizeMedium","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","disabled","clickable","clickableColorPrimary","clickableColorSecondary","deletable","deletableColorPrimary","deletableColorSecondary","outlined","filled","outlinedPrimary","outlinedSecondary","filledPrimary","filledSecondary","avatar","avatarSmall","avatarMedium","avatarColorPrimary","avatarColorSecondary","icon","iconSmall","iconMedium","iconColorPrimary","iconColorSecondary","label","labelSmall","labelMedium","deleteIcon","deleteIconSmall","deleteIconMedium","deleteIconColorPrimary","deleteIconColorSecondary","deleteIconOutlinedColorPrimary","deleteIconOutlinedColorSecondary","deleteIconFilledColorPrimary","deleteIconFilledColorSecondary","focusVisible"]),vc=["avatar","className","clickable","color","component","deleteIcon","disabled","icon","label","onClick","onDelete","onKeyDown","onKeyUp","size","variant","tabIndex","skipFocusWhenDisabled"],yc=(0,K.Ay)("div",{name:"MuiChip",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e,{color:r,iconColor:o,clickable:a,onDelete:i,size:l,variant:s}=n;return[{[`& .${gc.avatar}`]:t.avatar},{[`& .${gc.avatar}`]:t[`avatar${(0,Pe.A)(l)}`]},{[`& .${gc.avatar}`]:t[`avatarColor${(0,Pe.A)(r)}`]},{[`& .${gc.icon}`]:t.icon},{[`& .${gc.icon}`]:t[`icon${(0,Pe.A)(l)}`]},{[`& .${gc.icon}`]:t[`iconColor${(0,Pe.A)(o)}`]},{[`& .${gc.deleteIcon}`]:t.deleteIcon},{[`& .${gc.deleteIcon}`]:t[`deleteIcon${(0,Pe.A)(l)}`]},{[`& .${gc.deleteIcon}`]:t[`deleteIconColor${(0,Pe.A)(r)}`]},{[`& .${gc.deleteIcon}`]:t[`deleteIcon${(0,Pe.A)(s)}Color${(0,Pe.A)(r)}`]},t.root,t[`size${(0,Pe.A)(l)}`],t[`color${(0,Pe.A)(r)}`],a&&t.clickable,a&&"default"!==r&&t[`clickableColor${(0,Pe.A)(r)})`],i&&t.deletable,i&&"default"!==r&&t[`deletableColor${(0,Pe.A)(r)}`],t[s],t[`${s}${(0,Pe.A)(r)}`]]}})((e=>{let{theme:t,ownerState:n}=e;const r="light"===t.palette.mode?t.palette.grey[700]:t.palette.grey[300];return(0,o.A)({maxWidth:"100%",fontFamily:t.typography.fontFamily,fontSize:t.typography.pxToRem(13),display:"inline-flex",alignItems:"center",justifyContent:"center",height:32,color:(t.vars||t).palette.text.primary,backgroundColor:(t.vars||t).palette.action.selected,borderRadius:16,whiteSpace:"nowrap",transition:t.transitions.create(["background-color","box-shadow"]),cursor:"unset",outline:0,textDecoration:"none",border:0,padding:0,verticalAlign:"middle",boxSizing:"border-box",[`&.${gc.disabled}`]:{opacity:(t.vars||t).palette.action.disabledOpacity,pointerEvents:"none"},[`& .${gc.avatar}`]:{marginLeft:5,marginRight:-6,width:24,height:24,color:t.vars?t.vars.palette.Chip.defaultAvatarColor:r,fontSize:t.typography.pxToRem(12)},[`& .${gc.avatarColorPrimary}`]:{color:(t.vars||t).palette.primary.contrastText,backgroundColor:(t.vars||t).palette.primary.dark},[`& .${gc.avatarColorSecondary}`]:{color:(t.vars||t).palette.secondary.contrastText,backgroundColor:(t.vars||t).palette.secondary.dark},[`& .${gc.avatarSmall}`]:{marginLeft:4,marginRight:-4,width:18,height:18,fontSize:t.typography.pxToRem(10)},[`& .${gc.icon}`]:(0,o.A)({marginLeft:5,marginRight:-6},"small"===n.size&&{fontSize:18,marginLeft:4,marginRight:-4},n.iconColor===n.color&&(0,o.A)({color:t.vars?t.vars.palette.Chip.defaultIconColor:r},"default"!==n.color&&{color:"inherit"})),[`& .${gc.deleteIcon}`]:(0,o.A)({WebkitTapHighlightColor:"transparent",color:t.vars?`rgba(${t.vars.palette.text.primaryChannel} / 0.26)`:(0,X.X4)(t.palette.text.primary,.26),fontSize:22,cursor:"pointer",margin:"0 5px 0 -6px","&:hover":{color:t.vars?`rgba(${t.vars.palette.text.primaryChannel} / 0.4)`:(0,X.X4)(t.palette.text.primary,.4)}},"small"===n.size&&{fontSize:16,marginRight:4,marginLeft:-4},"default"!==n.color&&{color:t.vars?`rgba(${t.vars.palette[n.color].contrastTextChannel} / 0.7)`:(0,X.X4)(t.palette[n.color].contrastText,.7),"&:hover, &:active":{color:(t.vars||t).palette[n.color].contrastText}})},"small"===n.size&&{height:24},"default"!==n.color&&{backgroundColor:(t.vars||t).palette[n.color].main,color:(t.vars||t).palette[n.color].contrastText},n.onDelete&&{[`&.${gc.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.action.selectedChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.focusOpacity}))`:(0,X.X4)(t.palette.action.selected,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},n.onDelete&&"default"!==n.color&&{[`&.${gc.focusVisible}`]:{backgroundColor:(t.vars||t).palette[n.color].dark}})}),(e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({},n.clickable&&{userSelect:"none",WebkitTapHighlightColor:"transparent",cursor:"pointer","&:hover":{backgroundColor:t.vars?`rgba(${t.vars.palette.action.selectedChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:(0,X.X4)(t.palette.action.selected,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity)},[`&.${gc.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.action.selectedChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.focusOpacity}))`:(0,X.X4)(t.palette.action.selected,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)},"&:active":{boxShadow:(t.vars||t).shadows[1]}},n.clickable&&"default"!==n.color&&{[`&:hover, &.${gc.focusVisible}`]:{backgroundColor:(t.vars||t).palette[n.color].dark}})}),(e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({},"outlined"===n.variant&&{backgroundColor:"transparent",border:t.vars?`1px solid ${t.vars.palette.Chip.defaultBorder}`:`1px solid ${"light"===t.palette.mode?t.palette.grey[400]:t.palette.grey[700]}`,[`&.${gc.clickable}:hover`]:{backgroundColor:(t.vars||t).palette.action.hover},[`&.${gc.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},[`& .${gc.avatar}`]:{marginLeft:4},[`& .${gc.avatarSmall}`]:{marginLeft:2},[`& .${gc.icon}`]:{marginLeft:4},[`& .${gc.iconSmall}`]:{marginLeft:2},[`& .${gc.deleteIcon}`]:{marginRight:5},[`& .${gc.deleteIconSmall}`]:{marginRight:3}},"outlined"===n.variant&&"default"!==n.color&&{color:(t.vars||t).palette[n.color].main,border:`1px solid ${t.vars?`rgba(${t.vars.palette[n.color].mainChannel} / 0.7)`:(0,X.X4)(t.palette[n.color].main,.7)}`,[`&.${gc.clickable}:hover`]:{backgroundColor:t.vars?`rgba(${t.vars.palette[n.color].mainChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,X.X4)(t.palette[n.color].main,t.palette.action.hoverOpacity)},[`&.${gc.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette[n.color].mainChannel} / ${t.vars.palette.action.focusOpacity})`:(0,X.X4)(t.palette[n.color].main,t.palette.action.focusOpacity)},[`& .${gc.deleteIcon}`]:{color:t.vars?`rgba(${t.vars.palette[n.color].mainChannel} / 0.7)`:(0,X.X4)(t.palette[n.color].main,.7),"&:hover, &:active":{color:(t.vars||t).palette[n.color].main}}})})),bc=(0,K.Ay)("span",{name:"MuiChip",slot:"Label",overridesResolver:(e,t)=>{const{ownerState:n}=e,{size:r}=n;return[t.label,t[`label${(0,Pe.A)(r)}`]]}})((e=>{let{ownerState:t}=e;return(0,o.A)({overflow:"hidden",textOverflow:"ellipsis",paddingLeft:12,paddingRight:12,whiteSpace:"nowrap"},"outlined"===t.variant&&{paddingLeft:11,paddingRight:11},"small"===t.size&&{paddingLeft:8,paddingRight:8},"small"===t.size&&"outlined"===t.variant&&{paddingLeft:7,paddingRight:7})}));function xc(e){return"Backspace"===e.key||"Delete"===e.key}const wc=e.forwardRef((function(t,n){const r=(0,S.b)({props:t,name:"MuiChip"}),{avatar:i,className:l,clickable:s,color:u="default",component:d,deleteIcon:p,disabled:f=!1,icon:m,label:h,onClick:g,onDelete:v,onKeyDown:y,onKeyUp:b,size:x="medium",variant:w="filled",tabIndex:A,skipFocusWhenDisabled:k=!1}=r,C=(0,a.A)(r,vc),E=e.useRef(null),R=(0,q.A)(E,n),P=e=>{e.stopPropagation(),v&&v(e)},M=!(!1===s||!g)||s,O=M||v?Re:d||"div",T=(0,o.A)({},r,{component:O,disabled:f,size:x,color:u,iconColor:e.isValidElement(m)&&m.props.color||u,onDelete:!!v,clickable:M,variant:w}),j=(e=>{const{classes:t,disabled:n,size:r,color:o,iconColor:a,onDelete:i,clickable:l,variant:s}=e,c={root:["root",s,n&&"disabled",`size${(0,Pe.A)(r)}`,`color${(0,Pe.A)(o)}`,l&&"clickable",l&&`clickableColor${(0,Pe.A)(o)}`,i&&"deletable",i&&`deletableColor${(0,Pe.A)(o)}`,`${s}${(0,Pe.A)(o)}`],label:["label",`label${(0,Pe.A)(r)}`],avatar:["avatar",`avatar${(0,Pe.A)(r)}`,`avatarColor${(0,Pe.A)(o)}`],icon:["icon",`icon${(0,Pe.A)(r)}`,`iconColor${(0,Pe.A)(a)}`],deleteIcon:["deleteIcon",`deleteIcon${(0,Pe.A)(r)}`,`deleteIconColor${(0,Pe.A)(o)}`,`deleteIcon${(0,Pe.A)(s)}Color${(0,Pe.A)(o)}`]};return(0,U.A)(c,hc,t)})(T),I=O===Re?(0,o.A)({component:d||"div",focusVisibleClassName:j.focusVisible},v&&{disableRipple:!0}):{};let N=null;v&&(N=p&&e.isValidElement(p)?e.cloneElement(p,{className:(0,$.A)(p.props.className,j.deleteIcon),onClick:P}):(0,c.jsx)(mc,{className:(0,$.A)(j.deleteIcon),onClick:P}));let z=null;i&&e.isValidElement(i)&&(z=e.cloneElement(i,{className:(0,$.A)(j.avatar,i.props.className)}));let L=null;return m&&e.isValidElement(m)&&(L=e.cloneElement(m,{className:(0,$.A)(j.icon,m.props.className)})),(0,c.jsxs)(yc,(0,o.A)({as:O,className:(0,$.A)(j.root,l),disabled:!(!M||!f)||void 0,onClick:g,onKeyDown:e=>{e.currentTarget===e.target&&xc(e)&&e.preventDefault(),y&&y(e)},onKeyUp:e=>{e.currentTarget===e.target&&(v&&xc(e)?v(e):"Escape"===e.key&&E.current&&E.current.blur()),b&&b(e)},ref:R,tabIndex:k&&f?-1:A,ownerState:T},I,C,{children:[z||L,(0,c.jsx)(bc,{className:(0,$.A)(j.label),ownerState:T,children:h}),N]}))})),Ac=[{id:1,name:"Ava",gender:"Female",title:"The Cozy Companion",personality:"Ava is calm and comforting, making users feel at ease. She loves discussing books that warm the heart.",interests:["Heartfelt dramas","Family sagas","Inspirational stories"],tone:"Gentle, nurturing, and reassuring",avatar:"/avatars/ava.png"},{id:3,name:"Max",gender:"Male",title:"The Chill Philosopher",personality:"Max is laid-back and enjoys engaging in thoughtful discussions. He's always ready to ponder life's big questions over a good book.",interests:["Philosophy","Science fiction","Contemplative non-fiction"],tone:"Relaxed, reflective, and open-minded",avatar:"/avatars/max.png"},{id:5,name:"Maya",gender:"Female",title:"The Artistic Dreamer",personality:"Maya is creative and has a vivid imagination. She appreciates lyrical prose and evocative storytelling.",interests:["Poetry","Magical realism","Art-inspired literature"],tone:"Expressive, whimsical, and soothing",avatar:"/avatars/maya.png"},{id:8,name:"Viktor",gender:"Male",title:"The Cynical Critic",personality:"Viktor is a brilliantly cynical literary critic with a razor-sharp tongue and zero tolerance for mediocrity.",interests:["Complex literature","Savage criticism","Destroying poorly-formed arguments"],tone:"Sardonic, confrontational, and condescending",avatar:"/avatars/viktor.png"}];const kc=function(e){let{onSelectCharacter:t,selectedCharacter:n}=e;return(0,c.jsx)(Ks,{container:!0,spacing:3,children:Ac.map((e=>(0,c.jsx)(Ks,{item:!0,xs:12,sm:6,md:4,children:(0,c.jsx)(Ys,{sx:{height:"100%",border:(null===n||void 0===n?void 0:n.id)===e.id?2:0,borderColor:"primary.main",position:"relative"},children:(0,c.jsxs)(rc,{onClick:()=>(e=>{t(e)})(e),sx:{height:"100%"},children:[(0,c.jsx)(cc,{component:"img",height:"200",image:e.avatar,alt:e.name,sx:{objectFit:"contain",p:2}}),(0,c.jsxs)(fc,{children:[(0,c.jsx)(xt,{variant:"h6",gutterBottom:!0,align:"center",children:e.name}),(0,c.jsx)(xt,{variant:"subtitle1",color:"text.secondary",gutterBottom:!0,align:"center",children:e.title}),(0,c.jsx)(xt,{variant:"body2",color:"text.secondary",paragraph:!0,align:"center",children:e.personality}),(0,c.jsx)(H,{sx:{display:"flex",justifyContent:"center",flexWrap:"wrap",gap:1},children:e.interests.map(((e,t)=>(0,c.jsx)(wc,{label:e,size:"small"},t)))})]})]})})},e.id)))})};function Sc(e){return(0,Ae.Ay)("MuiCircularProgress",e)}(0,W.A)("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);const Cc=["className","color","disableShrink","size","style","thickness","value","variant"];let Ec,Rc,Pc,Mc,Oc=e=>e;const Tc=44,jc=(0,ie.i7)(Ec||(Ec=Oc`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`)),Ic=(0,ie.i7)(Rc||(Rc=Oc`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -125px;
  }
`)),Nc=(0,K.Ay)("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[n.variant],t[`color${(0,Pe.A)(n.color)}`]]}})((e=>{let{ownerState:t,theme:n}=e;return(0,o.A)({display:"inline-block"},"determinate"===t.variant&&{transition:n.transitions.create("transform")},"inherit"!==t.color&&{color:(n.vars||n).palette[t.color].main})}),(e=>{let{ownerState:t}=e;return"indeterminate"===t.variant&&(0,ie.AH)(Pc||(Pc=Oc`
      animation: ${0} 1.4s linear infinite;
    `),jc)})),$c=(0,K.Ay)("svg",{name:"MuiCircularProgress",slot:"Svg",overridesResolver:(e,t)=>t.svg})({display:"block"}),zc=(0,K.Ay)("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.circle,t[`circle${(0,Pe.A)(n.variant)}`],n.disableShrink&&t.circleDisableShrink]}})((e=>{let{ownerState:t,theme:n}=e;return(0,o.A)({stroke:"currentColor"},"determinate"===t.variant&&{transition:n.transitions.create("stroke-dashoffset")},"indeterminate"===t.variant&&{strokeDasharray:"80px, 200px",strokeDashoffset:0})}),(e=>{let{ownerState:t}=e;return"indeterminate"===t.variant&&!t.disableShrink&&(0,ie.AH)(Mc||(Mc=Oc`
      animation: ${0} 1.4s ease-in-out infinite;
    `),Ic)})),Lc=e.forwardRef((function(e,t){const n=(0,S.b)({props:e,name:"MuiCircularProgress"}),{className:r,color:i="primary",disableShrink:l=!1,size:s=40,style:u,thickness:d=3.6,value:p=0,variant:f="indeterminate"}=n,m=(0,a.A)(n,Cc),h=(0,o.A)({},n,{color:i,disableShrink:l,size:s,thickness:d,value:p,variant:f}),g=(e=>{const{classes:t,variant:n,color:r,disableShrink:o}=e,a={root:["root",n,`color${(0,Pe.A)(r)}`],svg:["svg"],circle:["circle",`circle${(0,Pe.A)(n)}`,o&&"circleDisableShrink"]};return(0,U.A)(a,Sc,t)})(h),v={},y={},b={};if("determinate"===f){const e=2*Math.PI*((Tc-d)/2);v.strokeDasharray=e.toFixed(3),b["aria-valuenow"]=Math.round(p),v.strokeDashoffset=`${((100-p)/100*e).toFixed(3)}px`,y.transform="rotate(-90deg)"}return(0,c.jsx)(Nc,(0,o.A)({className:(0,$.A)(g.root,r),style:(0,o.A)({width:s,height:s},y,u),ownerState:h,ref:t,role:"progressbar"},b,m,{children:(0,c.jsx)($c,{className:g.svg,ownerState:h,viewBox:"22 22 44 44",children:(0,c.jsx)(zc,{className:g.circle,style:v,ownerState:h,cx:Tc,cy:Tc,r:(Tc-d)/2,fill:"none",strokeWidth:d})})}))}));const _c=function(t){let{userId:n,onRefreshRequest:r}=t;const[o,a]=(0,e.useState)([]),[i,l]=(0,e.useState)(!0),[s,u]=(0,e.useState)(null),[d,p]=(0,e.useState)(!1),f=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];console.log("Fetching suggestions for userId:",n);try{l(!0);const t=await fetch(`${Os}/api/suggestions?force_refresh=${e}`,{headers:{"X-User-ID":n}});if(console.log("Suggestions API response status:",t.status),!t.ok){const e=await t.text();throw console.error("Failed to fetch suggestions. Status:",t.status,"Error:",e),new Error(`Failed to fetch suggestions: ${t.status} ${e}`)}const r=await t.json();if(console.log("Received suggestions data:",r),!r.suggestions||!Array.isArray(r.suggestions))throw console.error("Invalid suggestions data format:",r),new Error("Invalid suggestions data format");a(r.suggestions),u(null)}catch(t){console.error("Error in fetchSuggestions:",t),u(t.message||"Failed to load suggestions"),a([])}finally{l(!1),p(!1)}};return(0,e.useEffect)((()=>{console.log("AISuggestions component mounted/updated with userId:",n),n?f():(console.log("No userId provided, skipping suggestions fetch"),l(!1))}),[n]),(0,e.useEffect)((()=>{r&&r((()=>f(!0)))}),[r]),i&&!d?(0,c.jsx)(H,{sx:{display:"flex",justifyContent:"center",p:3},children:(0,c.jsx)(Lc,{})}):(0,c.jsx)(H,{sx:{p:2},children:s?(0,c.jsx)(H,{sx:{p:2},children:(0,c.jsx)(xt,{color:"error",children:s})}):o.length?(0,c.jsx)(H,{sx:{overflowY:"auto",height:"calc(100% - 64px)"},children:(0,c.jsx)(ia,{children:o.map(((e,t)=>(0,c.jsx)(mt,{elevation:1,sx:{mb:2,p:2,background:"rgba(255, 255, 255, 0.9)",backdropFilter:"blur(10px)",borderRadius:"8px",border:"1px solid rgba(0, 0, 0, 0.1)",transition:"all 0.3s ease","&:hover":{transform:"translateY(-2px)",boxShadow:3,cursor:"pointer"}},children:(0,c.jsxs)(el,{sx:{flexDirection:"column",alignItems:"flex-start",p:0},children:[(0,c.jsx)(xt,{variant:"subtitle1",sx:{fontWeight:"bold",color:"#1a237e"},children:e.title}),(0,c.jsxs)(xt,{variant:"subtitle2",color:"text.secondary",sx:{mb:1},children:["by ",e.author]}),(0,c.jsx)(xt,{variant:"body2",color:"text.secondary",sx:{fontStyle:"italic",lineHeight:1.6},children:e.description})]})},t)))})}):(0,c.jsx)(H,{sx:{p:2},children:(0,c.jsx)(xt,{children:"No suggestions available at the moment."})})})};const Fc=(0,W.A)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"]);const Dc=(0,W.A)("MuiListItemIcon",["root","alignItemsFlexStart"]);function Wc(e){return(0,Ae.Ay)("MuiMenuItem",e)}const Bc=(0,W.A)("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]),Vc=["autoFocus","component","dense","divider","disableGutters","focusVisibleClassName","role","tabIndex","className"],Hc=(0,K.Ay)(Re,{shouldForwardProp:e=>(0,$e.A)(e)||"classes"===e,name:"MuiMenuItem",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.dense&&t.dense,n.divider&&t.divider,!n.disableGutters&&t.gutters]}})((e=>{let{theme:t,ownerState:n}=e;return(0,o.A)({},t.typography.body1,{display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap"},!n.disableGutters&&{paddingLeft:16,paddingRight:16},n.divider&&{borderBottom:`1px solid ${(t.vars||t).palette.divider}`,backgroundClip:"padding-box"},{"&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${Bc.selected}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,X.X4)(t.palette.primary.main,t.palette.action.selectedOpacity),[`&.${Bc.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.focusOpacity}))`:(0,X.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},[`&.${Bc.selected}:hover`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:(0,X.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,X.X4)(t.palette.primary.main,t.palette.action.selectedOpacity)}},[`&.${Bc.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},[`&.${Bc.disabled}`]:{opacity:(t.vars||t).palette.action.disabledOpacity},[`& + .${Fc.root}`]:{marginTop:t.spacing(1),marginBottom:t.spacing(1)},[`& + .${Fc.inset}`]:{marginLeft:52},[`& .${vl.root}`]:{marginTop:0,marginBottom:0},[`& .${vl.inset}`]:{paddingLeft:36},[`& .${Dc.root}`]:{minWidth:36}},!n.dense&&{[t.breakpoints.up("sm")]:{minHeight:"auto"}},n.dense&&(0,o.A)({minHeight:32,paddingTop:4,paddingBottom:4},t.typography.body2,{[`& .${Dc.root} svg`]:{fontSize:"1.25rem"}}))})),Uc=e.forwardRef((function(t,n){const r=(0,S.b)({props:t,name:"MuiMenuItem"}),{autoFocus:i=!1,component:l="li",dense:s=!1,divider:u=!1,disableGutters:d=!1,focusVisibleClassName:p,role:f="menuitem",tabIndex:m,className:h}=r,g=(0,a.A)(r,Vc),v=e.useContext(na),y=e.useMemo((()=>({dense:s||v.dense||!1,disableGutters:d})),[v.dense,s,d]),b=e.useRef(null);(0,Gr.A)((()=>{i&&b.current&&b.current.focus()}),[i]);const x=(0,o.A)({},r,{dense:y.dense,divider:u,disableGutters:d}),w=(e=>{const{disabled:t,dense:n,divider:r,disableGutters:a,selected:i,classes:l}=e,s={root:["root",n&&"dense",t&&"disabled",!a&&"gutters",r&&"divider",i&&"selected"]},c=(0,U.A)(s,Wc,l);return(0,o.A)({},l,c)})(r),A=(0,q.A)(b,n);let k;return r.disabled||(k=void 0!==m?m:-1),(0,c.jsx)(na.Provider,{value:y,children:(0,c.jsx)(Hc,(0,o.A)({ref:A,role:f,tabIndex:k,component:l,focusVisibleClassName:(0,$.A)(w.focusVisible,p),className:(0,$.A)(w.root,h)},g,{ownerState:x,classes:w}))})}));const Xc=function(t){let{onUserChange:n}=t;const[r,o]=(0,e.useState)([]),[a,i]=(0,e.useState)(""),[l,s]=(0,e.useState)(!0);return(0,e.useEffect)((()=>{let e=!0;const t=async()=>{try{const t=await fetch(`${Os}/api/users`);if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const r=await t.json();if(console.log("Fetched users:",r),!Array.isArray(r))throw new Error("Expected users data to be an array");if(e&&(o(r),s(!1),!a&&r.length>0)){const e=localStorage.getItem("selectedUserId")||r[0].id.toString();i(e),localStorage.setItem("selectedUserId",e),n(e)}}catch(t){console.error("Error fetching users:",t),e&&(s(!1),o([]))}};t();const r=setInterval(t,3e4);return()=>{e=!1,clearInterval(r)}}),[]),l?null:(0,c.jsxs)(qo,{variant:"outlined",size:"small",sx:{minWidth:200,mb:2},children:[(0,c.jsx)(Vo,{children:"Current User"}),(0,c.jsx)(zi,{value:a,onChange:e=>{const t=e.target.value;i(t),localStorage.setItem("selectedUserId",t),n(t)},label:"Current User",children:r.map((e=>(0,c.jsx)(Uc,{value:e.id.toString(),children:e.username},e.id)))})]})};var Kc=n(8625);const qc=(0,r.A)({palette:{mode:"light",primary:{main:"#C4A68A",light:"#D9C3AE",dark:"#AB8B6E"},secondary:{main:"#E6B8A8",light:"#F2D2C7",dark:"#D19B88"},background:{default:"#FAF3E8",paper:"#FFFFFF"},text:{primary:"#4A3828",secondary:"#7D6355"}},typography:{fontFamily:'"Quicksand", sans-serif',h4:{fontFamily:'"Lora", serif',fontWeight:600},h5:{fontFamily:'"Lora", serif',fontWeight:500,color:"#000000"},h6:{fontFamily:'"Lora", serif',fontWeight:500},subtitle1:{fontFamily:'"Quicksand", sans-serif',fontWeight:500},body1:{fontFamily:'"Quicksand", sans-serif',fontWeight:400},body2:{fontFamily:'"Quicksand", sans-serif',fontWeight:400},button:{fontFamily:'"Quicksand", sans-serif',fontWeight:500}},components:{MuiButton:{styleOverrides:{root:{borderRadius:8,textTransform:"none",fontWeight:500,padding:"8px 16px"}}},MuiPaper:{styleOverrides:{root:{borderRadius:12,boxShadow:"0px 2px 8px rgba(0, 0, 0, 0.05)"}}}}});const Gc=function(){const[t,r]=(0,e.useState)([]),[o,a]=(0,e.useState)(null),[i,l]=(0,e.useState)(null),[s,u]=(0,e.useState)(null),[d,p]=(0,e.useState)([]),[f,m]=(0,e.useState)(!1),[h,g]=(0,e.useState)(null),v=(0,e.useRef)(null),y=(0,e.useRef)(null),[b,x]=(0,e.useState)(!1);(0,e.useEffect)((()=>{(async()=>{if(h)try{const e=await fetch("http://localhost:5000/api/companion/preference",{headers:{"X-User-ID":h}});if(e.ok){const t=await e.json();if(t.companion){const e=Ac.find((e=>e.name.toLowerCase()===t.companion.toLowerCase()));e&&u(e)}}}catch(e){console.error("Error loading companion preference:",e)}})()}),[h]),(0,e.useEffect)((()=>{(async()=>{if(h&&s)try{await fetch("http://localhost:5000/api/companion/preference",{method:"POST",headers:{"Content-Type":"application/json","X-User-ID":h},body:JSON.stringify({companion:s.name.toLowerCase()})})}catch(e){console.error("Error saving companion preference:",e)}})()}),[h,s]);const w=()=>{setTimeout((()=>{if(v.current){const e=20,t=v.current.getBoundingClientRect().top+window.pageYOffset-e;window.scrollTo({top:t,behavior:"smooth"})}}),100)};return(0,e.useEffect)((()=>{let e=!0;const t=async()=>{if(h)try{const t=await fetch("http://localhost:5000/api/books",{headers:{"X-User-ID":h}}),n=await t.json();e&&r(n)}catch(t){console.error("Error fetching books:",t)}};t();const n=setInterval(t,3e4);return()=>{e=!1,clearInterval(n)}}),[h]),(0,e.useEffect)((()=>{let e=!0;return(async()=>{if(h)try{const t=await fetch("http://localhost:5000/api/chat/history",{headers:{"X-User-ID":h}});if(!t.ok)throw new Error("Failed to fetch chat history");const n=await t.json();e&&console.log("Chat history:",n)}catch(t){console.error("Error fetching chat history:",t)}})(),()=>{e=!1}}),[h]),(0,e.useEffect)((()=>{let e=!0;return(async()=>{if(h)try{const t=await fetch("http://localhost:5000/api/preferences/companion",{headers:{"X-User-ID":h}});if(!t.ok)throw new Error("Failed to fetch companion preference");const n=await t.json();e&&u(n.companion)}catch(t){console.error("Error fetching companion preference:",t)}})(),()=>{e=!1}}),[h]),(0,c.jsxs)(k,{theme:qc,children:[(0,c.jsx)(N,{}),(0,c.jsxs)(H,{sx:{flexGrow:1},children:[(0,c.jsxs)(H,{sx:{position:"relative",width:"100%",height:"100vh",backgroundImage:`url(${n(4230)})`,backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[(0,c.jsx)(H,{sx:{position:"fixed",top:"20px",left:"20px",padding:f?"8px":"12px",display:"flex",flexDirection:"column",gap:1.5,background:"rgba(255, 255, 255, 0.9)",borderRadius:f?"50%":"15px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",backdropFilter:"blur(5px)",transition:"all 0.3s ease",zIndex:1e3,width:f?"40px":"auto",height:f?"40px":"auto",overflow:"hidden","&:hover":{transform:"translateY(-2px)",boxShadow:"0 6px 16px rgba(0,0,0,0.15)"}},children:(0,c.jsxs)(H,{sx:{display:"flex",flexDirection:"column",gap:1.5,position:"relative"},children:[(0,c.jsx)(Ie,{onClick:()=>m(!f),sx:{position:"absolute",top:-2,left:-4,p:0,"& .MuiSvgIcon-root":{fontSize:"1rem",color:"text.secondary"}},children:f?(0,c.jsx)(Lr.A,{}):(0,c.jsx)(zr.A,{})}),!f&&(0,c.jsxs)(H,{sx:{display:"flex",flexDirection:"column",alignItems:"stretch",gap:1.5,width:"200px",pt:.5},children:[(0,c.jsx)(Ue,{variant:"library"===o?"contained":"outlined",onClick:()=>{a("library"),w()},sx:{py:1.5,backgroundColor:"library"===o?"primary.main":"background.paper",color:"library"===o?"white":"text.primary","&:hover":{backgroundColor:"library"===o?"primary.dark":"primary.light",color:"white"}},children:"Library"}),(0,c.jsx)(Ue,{variant:"chat"===o?"contained":"outlined",onClick:()=>{a("chat"),w()},"data-section":"chat",sx:{py:1.5,backgroundColor:"chat"===o?"primary.main":"background.paper",color:"chat"===o?"white":"text.primary","&:hover":{backgroundColor:"chat"===o?"primary.dark":"primary.light",color:"white"}},children:"Chat"}),(0,c.jsx)(Ue,{variant:"characters"===o?"contained":"outlined",onClick:()=>{a("characters"),w()},sx:{py:1.5,backgroundColor:"characters"===o?"primary.main":"background.paper",color:"characters"===o?"white":"text.primary","&:hover":{backgroundColor:"characters"===o?"primary.dark":"primary.light",color:"white"}},children:"Reading Companions"})]})]})}),(0,c.jsx)(Ie,{onClick:w,sx:{position:"absolute",bottom:"20px",left:"50%",transform:"translateX(-50%)",backgroundColor:"rgba(255, 255, 255, 0.7)","&:hover":{backgroundColor:"rgba(255, 255, 255, 0.9)"},animation:"bounce 2s infinite","@keyframes bounce":{"0%, 20%, 50%, 80%, 100%":{transform:"translateX(-50%) translateY(0)"},"40%":{transform:"translateX(-50%) translateY(-20px)"},"60%":{transform:"translateX(-50%) translateY(-10px)"}}},children:(0,c.jsx)($r.A,{})})]}),(0,c.jsx)(H,{ref:v,children:(0,c.jsxs)(ct,{maxWidth:"lg",sx:{mt:4},children:[(0,c.jsx)(Xc,{onUserChange:e=>{g(e),e&&((async()=>{if(h)try{const e=await fetch("http://localhost:5000/api/books",{headers:{"X-User-ID":h}}),t=await e.json();r(t)}catch(e){console.error("Error fetching books:",e)}})(),(async()=>{if(h)try{const e=await fetch("http://localhost:5000/api/chat/history",{headers:{"X-User-ID":h}});if(!e.ok)throw new Error("Failed to fetch chat history");const t=await e.json();console.log("Chat history:",t)}catch(e){console.error("Error fetching chat history:",e)}})(),(async()=>{if(h)try{const e=await fetch("http://localhost:5000/api/preferences/companion",{headers:{"X-User-ID":h}});if(!e.ok)throw new Error("Failed to fetch companion preference");const t=await e.json();u(t.companion)}catch(e){console.error("Error fetching companion preference:",e)}})())}}),"characters"===o?(0,c.jsxs)(mt,{sx:{p:0,bgcolor:"#fafafa",borderRadius:"16px"},children:[(0,c.jsx)(H,{sx:{bgcolor:"primary.main",p:2,borderTopLeftRadius:"16px",borderTopRightRadius:"16px"},children:(0,c.jsx)(xt,{variant:"h6",align:"center",sx:{color:"black"},children:"Choose Your Reading Companion"})}),(0,c.jsx)(H,{sx:{p:3},children:(0,c.jsx)(kc,{onSelectCharacter:e=>{u(e)},selectedCharacter:s})})]}):"chat"===o?(0,c.jsxs)(mt,{sx:{p:0,bgcolor:"#fafafa",borderRadius:"16px"},children:[(0,c.jsx)(H,{sx:{bgcolor:"primary.main",p:2,borderTopLeftRadius:"16px",borderTopRightRadius:"16px"},children:(0,c.jsxs)(xt,{variant:"h6",align:"center",sx:{color:"black"},children:["Chat with ",s?s.name:"AI"]})}),(0,c.jsx)(H,{sx:{p:3},children:(0,c.jsx)(Ls,{selectedBook:i,selectedCharacter:s,userId:h})})]}):(0,c.jsx)(c.Fragment,{children:(0,c.jsxs)(H,{sx:{display:"flex",gap:4,flexWrap:"wrap"},children:[(0,c.jsxs)(H,{sx:{flex:"1 1 300px"},children:[(0,c.jsxs)(mt,{sx:{p:0,bgcolor:"#fafafa",borderRadius:"16px",mb:4},children:[(0,c.jsx)(H,{sx:{bgcolor:"primary.main",p:2,borderTopLeftRadius:"16px",borderTopRightRadius:"16px"},children:(0,c.jsx)(xt,{variant:"h6",align:"center",sx:{color:"black"},children:"Add New Book"})}),(0,c.jsx)(H,{sx:{p:3},children:(0,c.jsx)($s,{onAddBook:async e=>{try{const e=await fetch("http://localhost:5000/api/books",{headers:{"X-User-ID":h}});if(e.ok){const t=await e.json();r(t)}}catch(t){console.error("Error refreshing books:",t)}},userId:h})})]}),(0,c.jsxs)(mt,{sx:{p:0,bgcolor:"#fafafa",borderRadius:"16px"},children:[(0,c.jsxs)(H,{sx:{bgcolor:"primary.main",p:2,borderTopLeftRadius:"16px",borderTopRightRadius:"16px",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,c.jsx)(xt,{variant:"h6",sx:{color:"black"},children:"Suggestions"}),(0,c.jsx)(Nr,{title:"Refresh Suggestions",children:(0,c.jsx)(Ie,{size:"small",onClick:async()=>{y.current&&(x(!0),await y.current(),x(!1))},disabled:b,sx:{color:"black","&:hover":{bgcolor:"rgba(0, 0, 0, 0.1)"},...b&&{animation:"spin 1s linear infinite","@keyframes spin":{"0%":{transform:"rotate(0deg)"},"100%":{transform:"rotate(360deg)"}}}},children:(0,c.jsx)(Kc.A,{})})})]}),(0,c.jsx)(H,{sx:{p:3},children:(0,c.jsx)(_c,{userId:h,onRefreshRequest:e=>{y.current=e}})})]})]}),(0,c.jsxs)(mt,{sx:{p:0,flex:"2 1 400px",bgcolor:"#fafafa",borderRadius:"16px"},children:[(0,c.jsx)(H,{sx:{bgcolor:"primary.main",p:2,borderTopLeftRadius:"16px",borderTopRightRadius:"16px"},children:(0,c.jsx)(xt,{variant:"h6",align:"center",sx:{color:"black"},children:"Your Library"})}),(0,c.jsx)(H,{sx:{p:3},children:(0,c.jsx)(Ts,{books:t,onSelectBook:async e=>{if(h&&e&&e.id){l(e),p((t=>t.some((t=>t.id===e.id))?t.filter((t=>t.id!==e.id)):[...t,e]));try{if(!(await fetch(`http://localhost:5000/api/books/${e.id}/set-current`,{method:"POST",headers:{"Content-Type":"application/json","X-User-ID":h}})).ok)throw new Error("Failed to set current book")}catch(t){console.error("Error setting current book:",t)}}else console.error("Missing userId or book data:",{userId:h,book:e})},selectedBooks:d,onDeleteBook:async e=>{if(h&&e)try{await fetch(`http://localhost:5000/api/books/${e}`,{method:"DELETE",headers:{"X-User-ID":h}}),r(t.filter((t=>t.id!==e))),p(d.filter((t=>t.id!==e))),(null===i||void 0===i?void 0:i.id)===e&&l(null)}catch(n){console.error("Error deleting book:",n)}else console.error("Missing userId or bookId:",{userId:h,bookId:e})}})})]})]})})]})})]})]})};t.createRoot(document.getElementById("root")).render((0,c.jsx)(e.StrictMode,{children:(0,c.jsx)(Gc,{})}))})()})();
//# sourceMappingURL=main.fadee53d.js.map