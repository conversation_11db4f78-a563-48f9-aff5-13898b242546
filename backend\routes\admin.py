from fastapi import APIRouter, Depends, Request, HTTPException, status
from backend.utils.decorators import get_current_user
from backend.database.db import User, Book, ChatMessage, Contact, UserPreference
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/admin", tags=["admin"])

async def get_current_admin_user(user=Depends(get_current_user)):
    if not user.isAdmin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Admin privileges required")
    return user

@router.get('/check-access', status_code=status.HTTP_200_OK)
async def check_access(admin_user=Depends(get_current_admin_user)):
    return {"isAdmin": True}

@router.get('/users', status_code=status.HTTP_200_OK)
async def get_users(request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        users = session.query(User).all()
        return [{
            'id': u.id,
            'username': u.username,
            'email': u.email,
            'isAdmin': u.isAdmin,
            'created_at': u.created_at.isoformat()
        } for u in users]
    except Exception as e:
        logger.error(f"Error fetching users: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to fetch users")

@router.put('/users/{id}', status_code=status.HTTP_200_OK)
async def update_user(id: int, request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        user = session.query(User).filter_by(id=id).first()
        if not user:
            raise HTTPException(status.HTTP_404_NOT_FOUND, detail="User not found")
        data = await request.json()
        for k, v in data.items(): setattr(user, k, v)
        session.commit()
        return {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'isAdmin': user.isAdmin,
            'created_at': user.created_at.isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Error updating user: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update user")

@router.delete('/users/{id}', status_code=status.HTTP_200_OK)
async def delete_user(id: int, request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        user = session.query(User).filter_by(id=id).first()
        if not user:
            raise HTTPException(status.HTTP_404_NOT_FOUND, detail="User not found")
        if user.isAdmin and session.query(User).filter_by(isAdmin=True).count() <= 1:
            raise HTTPException(status.HTTP_400_BAD_REQUEST, detail="Cannot delete the last admin user")
        
        # Delete related UserPreference record first
        session.query(UserPreference).filter_by(user_id=id).delete()
        
        # Then delete other related records
        session.query(ChatMessage).filter_by(user_id=id).delete()
        session.query(Book).filter_by(user_id=id).delete()
        
        # Finally, delete the user
        session.delete(user)
        session.commit()
        return {"message": f"Successfully deleted user {user.username}"}
    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Error deleting user: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to delete user")

@router.get('/books', status_code=status.HTTP_200_OK)
async def get_books(request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        books = session.query(Book).all()
        return [{
            'id': b.id,
            'title': b.title,
            'author': b.author,
            'user_id': b.user_id,
            'created_at': b.created_at.isoformat() if hasattr(b, 'created_at') else None
        } for b in books]
    except Exception as e:
        logger.error(f"Error fetching books: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to fetch books")

@router.post('/books', status_code=status.HTTP_201_CREATED)
async def create_book(request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        data = await request.json()
        book = Book(**data)
        session.add(book)
        session.commit()
        return {
            'id': book.id,
            'title': book.title,
            'author': book.author,
            'user_id': book.user_id,
            'created_at': book.created_at.isoformat()
        }
    except Exception as e:
        session.rollback()
        logger.error(f"Error creating book: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create book")

@router.put('/books/{id}', status_code=status.HTTP_200_OK)
async def update_book(id: int, request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        book = session.query(Book).filter_by(id=id).first()
        if not book:
            raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Book not found")
        data = await request.json()
        for k, v in data.items(): setattr(book, k, v)
        session.commit()
        return {
            'id': book.id,
            'title': book.title,
            'author': book.author,
            'user_id': book.user_id,
            'created_at': book.created_at.isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Error updating book: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update book")

@router.delete('/books/{id}', status_code=status.HTTP_200_OK)
async def delete_book(id: int, request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        book = session.query(Book).filter_by(id=id).first()
        if not book:
            raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Book not found")
        session.delete(book)
        session.commit()
        return {"success": True}
    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Error deleting book: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to delete book")

@router.get('/chat-histories', status_code=status.HTTP_200_OK)
async def get_chat_histories(request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        msgs = session.query(ChatMessage).order_by(ChatMessage.timestamp.desc()).all()
        histories = {}
        for msg in msgs:
            # build histories as before using session
            if msg.chat_id not in histories:
                book = session.query(Book).filter_by(id=msg.book_id).first() if msg.book_id else None
                book_title = book.title if book else "Unknown Book"
                user = session.query(User).filter_by(id=msg.user_id).first() if msg.user_id else None
                username = user.username if user else "Unknown User"
                histories[msg.chat_id] = {
                    'id': msg.chat_id,
                    'bookTitle': book_title,
                    'username': username,
                    'companionName': msg.character if msg.character else "System",
                    'lastMessageAt': msg.timestamp.isoformat(),
                    'messageCount': 1,
                    'messages': [{
                        'content': msg.message,
                        'isUser': msg.is_user,
                        'character': msg.character,
                        'timestamp': msg.timestamp.isoformat()
                    }]
                }
            else:
                histories[msg.chat_id]['messageCount'] += 1
                histories[msg.chat_id]['messages'].append({
                    'content': msg.message,
                    'isUser': msg.is_user,
                    'character': msg.character,
                    'timestamp': msg.timestamp.isoformat()
                })
                if msg.timestamp.isoformat() > histories[msg.chat_id]['lastMessageAt']:
                    histories[msg.chat_id]['lastMessageAt'] = msg.timestamp.isoformat()
        return list(histories.values())
    except Exception as e:
        logger.error(f"Error fetching chat histories: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to fetch chat histories")


@router.get('/analytics', status_code=status.HTTP_200_OK)
async def get_analytics(request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        user_count = session.query(User).count()
        book_count = session.query(Book).count()
        user_message_count = session.query(ChatMessage).filter(ChatMessage.is_user == True).count()
        ai_message_count = session.query(ChatMessage).filter(ChatMessage.is_user == False).count()
        total_message_count = user_message_count + ai_message_count
        companion_usage = []
        from backend.data.characters import COMPANION_CHOICES
        for companion in COMPANION_CHOICES:
            companion_id = str(companion['id'])
            companion_name = companion['name']
            count = session.query(ChatMessage).filter(
                ChatMessage.character == companion_id,
                ChatMessage.is_user == False
            ).count()
            companion_usage.append({
                'id': companion_id,
                'name': companion_name,
                'messageCount': count
            })
        from datetime import datetime, timedelta
        week_ago = datetime.utcnow() - timedelta(days=7)
        active_users = (
            session.query(User)
            .join(ChatMessage, User.id == ChatMessage.user_id)
            .filter(ChatMessage.timestamp >= week_ago)
            .distinct(User.id)
            .all()
        )
        active_users_list = [
            {
                'id': u.id,
                'username': u.username,
                'email': u.email,
                'last_active': max((m.timestamp for m in u.chat_messages), default=None).isoformat() if u.chat_messages else None
            }
            for u in active_users
        ]
        user_growth = []
        for i in range(7):
            day = datetime.utcnow() - timedelta(days=i)
            next_day = day + timedelta(days=1)
            count = session.query(User).filter(User.created_at >= day, User.created_at < next_day).count()
            user_growth.append({
                'date': day.strftime('%Y-%m-%d'),
                'count': count
            })
        user_growth.reverse()
        return {
            'userCount': user_count,
            'bookCount': book_count,
            'messageCount': total_message_count,
            'userMessageCount': user_message_count,
            'aiMessageCount': ai_message_count,
            'activeUsers': active_users_list,
            'userGrowth': user_growth,
            'companionUsage': companion_usage
        }
    except Exception as e:
        logger.error(f"Error fetching analytics: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to fetch analytics")

@router.get('/contacts', status_code=status.HTTP_200_OK)
async def get_contacts(request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        contacts = session.query(Contact).order_by(Contact.created_at.desc()).all()
        return [{
            'id': c.id,
            'name': c.name,
            'email': c.email,
            'subject': c.subject,
            'message': c.message,
            'created_at': c.created_at.isoformat(),
            'is_read': c.is_read
        } for c in contacts]
    except Exception as e:
        logger.error(f"Error fetching contacts: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to fetch contacts")

@router.put('/contacts/{id}/mark-read', status_code=status.HTTP_200_OK)
async def mark_contact_as_read(id: int, request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        contact = session.query(Contact).filter_by(id=id).first()
        if not contact:
            raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Contact not found")
        contact.is_read = True
        session.commit()
        return {
            'id': contact.id,
            'is_read': contact.is_read
        }
    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Error marking contact as read: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to mark contact as read")

@router.delete('/contacts/{id}', status_code=status.HTTP_200_OK)
async def delete_contact(id: int, request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        contact = session.query(Contact).filter_by(id=id).first()
        if not contact:
            raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Contact not found")
        session.delete(contact)
        session.commit()
        return {"success": True, "message": "Contact deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Error deleting contact: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to delete contact")