"""Base class for AI providers in the BookWorm application."""
from typing import Dict, List, Any, Generator, Optional, Tuple


class BaseProvider:
    """Base class that defines the interface for all AI providers."""
    
    def __init__(self, model: str, logger: Any):
        """Initialize the provider with model and logger.
        
        Args:
            model: The model name to use
            logger: Logger instance for logging
        """
        self.model = model
        self.logger = logger
    
    def chat(self, messages: List[Dict[str, str]], system_prompt: str, 
             max_tokens: int, temperature: float, stream: bool) -> Any:
        """Send a chat request to the AI provider.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            system_prompt: The system prompt to use
            max_tokens: Maximum number of tokens in the response
            temperature: Temperature for response generation
            stream: Whether to stream the response
            
        Returns:
            The response from the AI provider
        """
        raise NotImplementedError("Subclasses must implement this method")
    
    def process_response(self, response: Any) -> str:
        """Process the response from the AI provider.
        
        Args:
            response: The response from the AI provider
            
        Returns:
            The processed response as a string
        """
        raise NotImplementedError("Subclasses must implement this method")
    
    def process_stream(self, response: Any, chat_id: Optional[str] = None) -> Generator[Dict[str, Any], None, None]:
        """Process a streaming response from the AI provider.
        
        Args:
            response: The streaming response from the AI provider
            chat_id: Optional chat ID to include in the final chunk
            
        Returns:
            Generator yielding chunks of the response
        """
        raise NotImplementedError("Subclasses must implement this method")
