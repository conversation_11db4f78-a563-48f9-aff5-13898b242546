import React, { useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import LoginForm from './LoginForm';
import { supabase } from '../services/supabaseService';
import { useAppContext } from '../context/AppContext';

const LoginPopup = ({ open, setOpen }) => {
  const { currentUser } = useAppContext();
  const handleClose = () => {
    setOpen(false);
  };

  // Automatically close popup when logged in
  useEffect(() => {
    if (currentUser && open) {
      setOpen(false);
    }
  }, [currentUser, open, setOpen]);

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      disableRestoreFocus
    >
      <DialogTitle sx={{ textAlign: 'center' }}>Log In to BookWorm</DialogTitle>
      <DialogContent>
        <LoginForm onSuccess={handleClose} />
      </DialogContent>
    </Dialog>
  );
};

export default LoginPopup;