import api from '../../services/axiosConfig';
import chatService from '../../services/chatService';
import RateLimiter from '../../utils/rateLimiter';

// Mock dependencies
jest.mock('../../services/axiosConfig');
jest.mock('../../utils/rateLimiter', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      throttle: jest.fn().mockResolvedValue(undefined)
    })),
    rateLimitConfig: {
      chat: {
        message: { requestsPerMinute: 10 },
        history: { requestsPerMinute: 20 }
      }
    }
  };
});

describe('chatService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('sendMessage', () => {
    const bookId = 'book123';
    const message = 'Hello world';
    const characterId = 'char456';
    const userId = 'user789';
    const chatId = 'chat101';
    const mockResponse = { 
      data: { 
        response: 'Character response',
        chatId: 'chat101'
      } 
    };

    beforeEach(() => {
      api.post.mockResolvedValue(mockResponse);
    });

    test('should send message with required parameters', async () => {
      const result = await chatService.sendMessage(bookId, message);
      
      expect(api.post).toHaveBeenCalledWith(
        '/api/chat/send',
        {
          message,
          bookId
        },
        { timeout: 60000 }
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should send message with all parameters', async () => {
      const result = await chatService.sendMessage(bookId, message, characterId, userId, chatId);
      
      expect(api.post).toHaveBeenCalledWith(
        '/api/chat/send',
        {
          message,
          bookId,
          characterId,
          userId,
          chatId
        },
        { timeout: 60000 }
      );
      expect(result).toEqual(mockResponse.data);
    });

    test('should handle errors', async () => {
      const error = new Error('Network error');
      api.post.mockRejectedValue(error);
      
      await expect(chatService.sendMessage(bookId, message)).rejects.toThrow('Network error');
    });

    test('should prevent duplicate requests for the same book', async () => {
      // Start a request that hasn't resolved yet
      const firstPromise = chatService.sendMessage(bookId, message);
      
      // Try to send another request for the same book immediately
      const secondPromise = chatService.sendMessage(bookId, 'Another message');
      
      // The second promise should equal the first one (same reference)
      expect(secondPromise).toBe(firstPromise);
      
      // Complete the first request
      await firstPromise;
      
      // After the first request completes, we should be able to send another
      api.post.mockClear();
      const thirdPromise = chatService.sendMessage(bookId, 'Third message');
      await thirdPromise;
      
      expect(api.post).toHaveBeenCalledTimes(1);
    });

    test('should apply rate limiting', async () => {
      await chatService.sendMessage(bookId, message);
      
      // Verify throttle was called
      const rateLimiterInstances = RateLimiter.mock.instances;
      expect(rateLimiterInstances[0].throttle).toHaveBeenCalledTimes(1);
    });
  });

  describe('getChatHistory', () => {
    const bookId = 'book123';
    const mockHistory = [
      { role: 'user', content: 'Hello', timestamp: '2023-01-01T12:00:00Z' },
      { role: 'assistant', content: 'Hi there!', timestamp: '2023-01-01T12:00:01Z' }
    ];

    test('should fetch chat history', async () => {
      api.get.mockResolvedValue({ data: mockHistory });
      
      const result = await chatService.getChatHistory(bookId);
      
      expect(api.get).toHaveBeenCalledWith(`/api/chat/history/${bookId}`);
      expect(result).toEqual(mockHistory);
    });

    test('should return empty array on error', async () => {
      api.get.mockRejectedValue(new Error('Network error'));
      
      const result = await chatService.getChatHistory(bookId);
      
      expect(result).toEqual([]);
    });

    test('should apply rate limiting', async () => {
      await chatService.getChatHistory(bookId);
      
      // Verify throttle was called
      const rateLimiterInstances = RateLimiter.mock.instances;
      expect(rateLimiterInstances[1].throttle).toHaveBeenCalledTimes(1);
    });
  });

  describe('clearChatHistory', () => {
    const bookId = 'book123';
    const mockResponse = { success: true };

    test('should clear chat history', async () => {
      api.delete.mockResolvedValue({ data: mockResponse });
      
      const result = await chatService.clearChatHistory(bookId);
      
      expect(api.delete).toHaveBeenCalledWith(`/api/chat/history/${bookId}`);
      expect(result).toEqual(mockResponse);
    });

    test('should handle errors', async () => {
      const error = new Error('Network error');
      api.delete.mockRejectedValue(error);
      
      await expect(chatService.clearChatHistory(bookId)).rejects.toThrow('Network error');
    });

    test('should apply rate limiting', async () => {
      await chatService.clearChatHistory(bookId);
      
      // Verify throttle was called
      const rateLimiterInstances = RateLimiter.mock.instances;
      expect(rateLimiterInstances[1].throttle).toHaveBeenCalledTimes(1);
    });
  });
});
