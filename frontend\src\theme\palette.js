// Color definitions for the BookWorm application theme

const palette = {
  mode: 'light',
  primary: {
    main: '#C4A68A', // Lighter warm brown
    light: '#D9C3AE',
    dark: '#AB8B6E'
  },
  secondary: {
    main: '#E6B8A8', // Lighter terracotta
    light: '#F2D2C7',
    dark: '#D19B88'
  },
  background: {
    default: '#FAF3E8', // Warm off-white
    paper: '#FFFFFF'
  },
  text: {
    primary: '#4A3828', // Softer deep brown
    secondary: '#7D6355' // Softer medium brown
  }
};

export default palette; 