import api from './axiosConfig';

/**
 * Contact service for handling contact form submissions
 */
const contactService = {
  /**
   * Submit a contact form
   * @param {Object} contactData - The contact form data
   * @param {string} contactData.name - The name of the person
   * @param {string} contactData.email - The email address
   * @param {string} contactData.subject - The subject of the message
   * @param {string} contactData.message - The message content
   * @returns {Promise} - The response from the API
   */
  submitContactForm: async (contactData) => {
    try {
      const response = await api.post('/api/contact', contactData);
      return response.data;
    } catch (error) {
      console.error('Error submitting contact form:', error);
      throw error;
    }
  }
};

export default contactService; 