import logging
from typing import Optional, Tu<PERSON>, Dict, Any, List, Union
from backend.database.db import db
from backend.ai.prompt_utils import format_book_context
import backend.ai.character as character

logger = logging.getLogger(__name__)

def resolve_book_id(user_id: int, book_id_raw: Any, book_title: Optional[str] = None, book_author: Optional[str] = None) -> Optional[int]:
    """
    Resolve book_id from raw input or title/author combination
    
    Args:
        user_id: The user ID
        book_id_raw: Raw book ID input (could be string, int, or None)
        book_title: Optional book title to look up
        book_author: Optional book author to look up
        
    Returns:
        Resolved book ID as int or None if not found
    """
    # Ensure book_id is treated as an integer or None
    book_id = int(book_id_raw) if book_id_raw is not None else None
    logger.info(f"Extracted book_id: {book_id} from raw value: {book_id_raw}")
    
    # Backend failsafe: If book_id is None, but book_title and book_author are present, look up the book_id
    if book_id is None and book_title and book_author:
        user_books = db.get_books(user_id)
        for b in user_books:
            if b.get('title') == book_title and b.get('author') == book_author:
                book_id = b.get('id')
                logger.info(f"Book ID resolved from title/author: {book_id}")
                break
    
    return book_id

def get_book_context(book_id: Optional[int]) -> Tuple[str, Optional[str], Optional[str]]:
    """
    Get book context, title, and author for a given book ID
    
    Args:
        book_id: The book ID to look up
        
    Returns:
        Tuple of (formatted_context, title, author)
    """
    book_context = ""
    book_title = None
    book_author = None
    
    if book_id:
        logger.info(f"Fetching book context for book_id={book_id} (type: {type(book_id)})")
        book = db.get_book(book_id)
        if book:
            # Enhanced book context with more details including content
            book_context_data = {
                'title': book.get('title', 'Unknown'),
                'author': book.get('author', 'Unknown'),
            }
            book_title = book.get('title', 'Unknown')
            book_author = book.get('author', 'Unknown')
            # Add content/summary if available
            if 'content' in book and book['content']:
                book_context_data['content'] = book['content']
            if 'summary' in book and book['summary']:
                book_context_data['summary'] = book['summary']
            # Format enhanced book context
            book_context = format_book_context([book_context_data])
            # Log the context being passed
            logger.info(f"Using book context: {book_context[:100]}...")
            logger.info(f"Book title: {book_title}, author: {book_author}")
        else:
            logger.warning(f"Book with ID {book_id} not found, no context will be provided")
    else:
        logger.warning("No book_id provided for chat, no book context will be used")
    
    return book_context, book_title, book_author

def get_character_identifier(user_id: int, character_identifier: Optional[str] = None) -> str:
    """
    Get character identifier from input or user preferences
    
    Args:
        user_id: The user ID
        character_identifier: Optional character identifier from request
        
    Returns:
        Character identifier string
    """
    # If a new character specified, update user preference
    if character_identifier:
        try:
            db.set_user_preference(user_id, character_identifier)
        except Exception as e:
            logger.warning(f"Failed to update user preference to {character_identifier}: {e}")

    # If still missing, fall back to last used companion
    if not character_identifier:
        user_pref = db.get_user_preference(user_id)
        if user_pref and user_pref.preferred_companion is not None:
            character_identifier = user_pref.preferred_companion
    
    return character_identifier

def store_user_message(user_id: int, book_id: Optional[int], message: str, character: str, chat_id: Optional[str] = None):
    """
    Store a user message in the database
    
    Args:
        user_id: The user ID
        book_id: The book ID (can be None)
        message: The message content
        character: The character identifier
        chat_id: Optional chat ID
        
    Returns:
        The stored message object
    """
    try:
        logger.info(f"About to call add_chat_message with book_id={book_id} (type: {type(book_id)})")
        user_message = db.add_chat_message(
            user_id=user_id,
            book_id=book_id,
            message=message,
            is_user=True,
            character=character,
            chat_id=chat_id
        )
        
        # Update chat_id if it was generated
        if hasattr(user_message, 'chat_id') and user_message.chat_id:
            chat_id = user_message.chat_id
            logger.info(f"Generated new chat_id: {chat_id}")
            
        return user_message, chat_id
    except Exception as e:
        logger.error(f"Error storing user message: {str(e)}")
        logger.exception("Exception details:")
        return None, chat_id

def store_ai_message(user_id: int, book_id: Optional[int], message: str, character: str, chat_id: Optional[str] = None):
    """
    Store an AI message in the database
    
    Args:
        user_id: The user ID
        book_id: The book ID (can be None)
        message: The message content
        character: The character identifier
        chat_id: Optional chat ID
        
    Returns:
        The stored message object and chat_id
    """
    try:
        logger.info(f"About to store AI response with book_id={book_id} (type: {type(book_id)}), chat_id={chat_id}")
        stored_message = db.add_chat_message(
            user_id=user_id,
            book_id=book_id,
            message=message,
            is_user=False,
            character=character,
            chat_id=chat_id
        )
        
        # Get chat_id from stored message if available
        if hasattr(stored_message, 'chat_id') and stored_message.chat_id:
            chat_id = stored_message.chat_id
            
        return stored_message, chat_id
    except Exception as db_error:
        logger.error(f"Error storing AI response: {str(db_error)}")
        logger.exception("Exception details:")
        return None, chat_id

def get_character_info(user_id: int, character_id: Optional[str] = None):
    """
    Get character information based on user preference or specified character ID
    
    Args:
        user_id: The user ID
        character_id: Optional character ID to override user preference
        
    Returns:
        Character info dictionary
    """
    # Get character info for the response
    user_pref = db.get_user_preference(user_id)
    companion = character_id or (user_pref.preferred_companion if user_pref else 1)  # Default to Ava (ID 1)
    char = character.get_character(companion)
    
    if not char:
        logger.warning(f"Character '{companion}' not found, using default")
        char = character.CHAR_AVA
    
    return char.get_info()

def format_chat_messages(chat_history):
    """
    Format chat history messages into a standard dictionary format
    
    Args:
        chat_history: List of chat history message objects
        
    Returns:
        List of formatted message dictionaries
    """
    return [{
        'id': msg.id,
        'message': msg.message,
        'is_user': msg.is_user,
        'character': msg.character,
        'timestamp': msg.timestamp.isoformat(),
        'chat_id': msg.chat_id,
        'book_id': msg.book_id
    } for msg in chat_history]
