export const formatMessageContent = (content) => {
  if (!content) return '';
  return content.trim();
};

export const prepareMessageForSending = (content, userId, bookId) => {
  return {
    content: formatMessageContent(content),
    timestamp: new Date().toISOString(),
    userId,
    bookId,
    type: 'user'
  };
};

export const formatResponseMessage = (response, bookId) => {
  return {
    content: response.content,
    timestamp: new Date().toISOString(),
    bookId,
    type: 'assistant',
    metadata: response.metadata || {}
  };
};
