from sqlalchemy import create_engine, <PERSON><PERSON>n, Integer, String, <PERSON><PERSON><PERSON>, DateTime, ForeignKey, Text, JSON, func, event, or_, and_
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship, scoped_session
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import OperationalError, SQLAlchemyError
from datetime import datetime
import json
from backend.config import config
import logging
from backend.utils.cache import chat_cache, get_chat_cache_key

logger = logging.getLogger(__name__)
Base = declarative_base()

def create_db_engine():
    try:
        if config.BW_LOCAL_MODE:
            # SQLite configuration for local development
            engine = create_engine(
                config.DATABASE_URL,
                connect_args={'check_same_thread': False}  # Required for SQLite
            )
        else:
            # Production PostgreSQL configuration with connection pooling
            engine = create_engine(
                config.DATABASE_URL,
                poolclass=QueuePool if config.DB_POOL_SIZE is not None else None,
                pool_size=config.DB_POOL_SIZE,
                max_overflow=config.DB_MAX_OVERFLOW,
                pool_timeout=config.DB_POOL_TIMEOUT,
                pool_pre_ping=True  # Enable connection health checks
            )
        return engine
    except Exception as e:
        logger.error(f"Failed to create database engine: {str(e)}")
        raise

def get_db_session():
    """Create a new database session."""
    engine = create_db_engine()
    session_factory = sessionmaker(bind=engine)
    Session = scoped_session(session_factory)
    return Session()


def init_db():
    """Initialize the database schema."""
    try:
        engine = create_db_engine()
        Base.metadata.create_all(engine)
        logger.info("Database schema created successfully")
    except SQLAlchemyError as e:
        logger.error(f"Failed to initialize database: {str(e)}")
        raise

class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(120), unique=True, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    isAdmin = Column(Boolean, default=False)  # Add isAdmin field with default False
    supabase_id = Column(String(36), unique=True, nullable=True, index=True)  # Store Supabase User UUID

    # Relationships
    preferences = relationship("UserPreference", back_populates="user", uselist=False)
    books = relationship("Book", back_populates="user")
    chat_messages = relationship("ChatMessage", back_populates="user")

# Add event listeners to ensure case consistency
@event.listens_for(User, 'before_insert')
@event.listens_for(User, 'before_update')
def lowercase_user_fields(mapper, connection, target):
    if target.username is not None:
        target.username = target.username.lower()
    if target.email is not None:
        target.email = target.email.lower()

class UserPreference(Base):
    __tablename__ = 'user_preferences'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), unique=True, nullable=False)
    preferred_companion = Column(Integer, nullable=False)

    # Relationships
    user = relationship("User", back_populates="preferences")

class Book(Base):
    __tablename__ = 'books'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    title = Column(String(200), nullable=False)
    author = Column(String(100))
    isbn = Column(String(13))
    current_page = Column(Integer, default=1)
    book_metadata = Column(JSON)
    is_current = Column(Boolean, default=False)

    # Relationships
    user = relationship("User", back_populates="books")
    chat_messages = relationship("ChatMessage", back_populates="book")

class BookSuggestion(Base):
    __tablename__ = 'book_suggestions'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    title = Column(String(200), nullable=False)
    author = Column(String(100), nullable=False)
    description = Column(Text, nullable=False)
    character = Column(String(50), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User")

class ChatMessage(Base):
    __tablename__ = 'chat_history'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    chat_id = Column(String(50), nullable=False)
    book_id = Column(Integer, ForeignKey('books.id'))
    message = Column(Text, nullable=False)
    is_user = Column(Boolean, nullable=False)
    character = Column(String(50))
    timestamp = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="chat_messages")
    book = relationship("Book", back_populates="chat_messages")

class Contact(Base):
    __tablename__ = 'contacts'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    email = Column(String(120), nullable=False)
    subject = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_read = Column(Boolean, default=False)

class Database:
    def __init__(self):
        # Configure SQLAlchemy engine with appropriate settings for production/development
        if config.BW_LOCAL_MODE:
            # SQLite configuration for local development
            self.engine = create_engine(
                config.DATABASE_URL,
                connect_args={'check_same_thread': False}  # Required for SQLite
            )
        else:
            # Production PostgreSQL configuration with optimized connection pooling
            self.engine = create_engine(
                config.DATABASE_URL,
                poolclass=QueuePool if config.DB_POOL_SIZE is not None else None,
                pool_size=config.DB_POOL_SIZE or 10,  # Default to 10 connections
                max_overflow=config.DB_MAX_OVERFLOW or 20,  # Allow 20 overflow connections
                pool_timeout=config.DB_POOL_TIMEOUT or 30,  # 30 second timeout
                pool_recycle=1800,  # Recycle connections after 30 minutes
                pool_pre_ping=True,  # Verify connections before use
                echo=False,  # Disable SQL logging in production for performance
                isolation_level="READ COMMITTED"
            )

        # Create session factory
        self.Session = sessionmaker(bind=self.engine)

    def get_session(self):
        """Get a database session."""
        return self.Session()

    def _get_session_for_operation(self, session=None):
        """Get a session for database operations.
        If a session is provided, use it. Otherwise create new.
        Returns:
            tuple: (session, should_close)
            - session: SQLAlchemy session to use
            - should_close: True if we created a new session that needs to be closed
        """
        if session is not None:
            return session, False
        return self.Session(), True

    def _cleanup_session(self, session, should_close, error=None):
        """Helper to properly cleanup a session if we created it.
        Args:
            session: The session to potentially cleanup
            should_close: Whether we should close this session
            error: Optional exception that occurred during operation
        """
        if should_close:
            if error:
                session.rollback()
            try:
                session.close()
            except Exception as e:
                logger.error(f"Error closing database session: {str(e)}")

    def initialize_database(self):
        """Initialize the database schema."""
        Base.metadata.create_all(self.engine)

    def get_user(self, username, session=None):
        """Get user by username using case-insensitive comparison"""
        session, should_close = self._get_session_for_operation(session)
        try:
            user = session.query(User).filter(
                func.lower(User.username) == username.lower()
            ).first()
            return user
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            raise
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def get_books(self, user_id, session=None):
        """Get all books for a user"""
        session, should_close = self._get_session_for_operation(session)
        try:
            books = session.query(Book).filter_by(user_id=user_id).all()
            # Convert to dictionaries before returning
            return [self._book_to_dict(book) for book in books]
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            raise
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def delete_book(self, book_id, user_id, session=None):
        """Delete a book from user's library"""
        session, should_close = self._get_session_for_operation(session)
        try:
            book = session.query(Book).filter_by(id=book_id, user_id=user_id).first()
            if book:
                session.delete(book)
                session.commit()
                return True
            return False
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            raise
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def add_user(self, username, email, session=None):
        """Add a new user and initialize preferences with case-insensitive username and email."""
        username = username.lower()  # normalize the username
        email = email.lower()        # normalize the email
        session, should_close = self._get_session_for_operation(session)
        try:
            # Check if user exists using case-insensitive comparison
            existing_user = session.query(User).filter(
                (func.lower(User.username) == username) | (func.lower(User.email) == email)
            ).first()
            if existing_user:
                return existing_user.id

            # Create new user - no need to lowercase here as event listener will handle it
            user = User(username=username, email=email)
            session.add(user)
            session.flush()  # Get the user ID

            # Create preferences
            preferences = UserPreference(user_id=user.id, preferred_companion=1)  # Default to Ava (ID 1)
            session.add(preferences)

            session.commit()
            return user.id
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            raise
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def get_user_by_id(self, user_id, session=None):
        """Get user by ID"""
        session, should_close = self._get_session_for_operation(session)
        try:
            return session.query(User).filter_by(id=user_id).first()
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            raise
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def get_user_preference(self, user_id, session=None):
        """Get user preference by user ID."""
        session, should_close = self._get_session_for_operation(session)
        try:
            preference = session.query(UserPreference).filter_by(user_id=user_id).first()
            return preference
        except SQLAlchemyError as e:
            logger.error(f"Error getting user preference: {str(e)}")
            return None
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            raise
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def set_user_preference(self, user_id, companion, session=None):
        """Set user preference by user ID."""
        session, should_close = self._get_session_for_operation(session)
        try:
            preference = session.query(UserPreference).filter(UserPreference.user_id == user_id).first()

            # Convert companion to integer if it's a string digit
            if isinstance(companion, str) and companion.isdigit():
                companion = int(companion)

            if preference:
                preference.preferred_companion = companion
            else:
                preference = UserPreference(user_id=user_id, preferred_companion=companion)
                session.add(preference)

            session.commit()
            return True
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            return False
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def store_book_suggestions(self, user_id, suggestions, character, session=None):
        """Store book suggestions for a user."""
        session, should_close = self._get_session_for_operation(session)
        try:
            # First, remove old suggestions for this user
            session.query(BookSuggestion).filter_by(user_id=user_id).delete()

            # Add new suggestions
            for suggestion in suggestions:
                book_suggestion = BookSuggestion(
                    user_id=user_id,
                    title=suggestion['title'],
                    author=suggestion['author'],
                    description=suggestion['description'],
                    character=character
                )
                session.add(book_suggestion)

            session.commit()
            return True
        except SQLAlchemyError as e:
            self._cleanup_session(session, should_close, e)
            logger.error(f"Error storing book suggestions: {str(e)}")
            raise
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            raise
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def save_book_suggestions(self, user_id, suggestions, session=None):
        """Save a list of book suggestions for a user.
        This is different from store_book_suggestions as it doesn't delete existing suggestions first.
        """
        session, should_close = self._get_session_for_operation(session)
        try:
            if not suggestions:
                logger.warning(f"Attempted to save empty suggestions list for user {user_id}")
                return False

            character = suggestions[0].get('character', 'ava')  # Use character from first suggestion

            # Save the updated suggestions
            for suggestion in suggestions:
                book_suggestion = BookSuggestion(
                    user_id=user_id,
                    title=suggestion.get('title', ''),
                    author=suggestion.get('author', ''),
                    description=suggestion.get('description', ''),
                    character=suggestion.get('character', character)
                )
                session.add(book_suggestion)

            session.commit()
            return True
        except SQLAlchemyError as e:
            self._cleanup_session(session, should_close, e)
            logger.error(f"Error saving book suggestions: {str(e)}")
            return False
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            logger.error(f"Unexpected error saving book suggestions: {str(e)}")
            return False
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def get_book_suggestions(self, user_id, session=None):
        """Get stored suggestions for a user."""
        session, should_close = self._get_session_for_operation(session)
        try:
            suggestions = session.query(BookSuggestion).filter_by(user_id=user_id).all()
            return [
                {
                    'title': s.title,
                    'author': s.author,
                    'description': s.description,
                    'character': s.character
                }
                for s in suggestions
            ]
        except SQLAlchemyError as e:
            logger.error(f"Error getting book suggestions: {str(e)}")
            return []
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            raise
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def delete_book_suggestions(self, user_id, session=None):
        """Delete all book suggestions for a user."""
        session, should_close = self._get_session_for_operation(session)
        try:
            session.query(BookSuggestion).filter_by(user_id=user_id).delete()
            session.commit()
            return True
        except SQLAlchemyError as e:
            self._cleanup_session(session, should_close, e)
            logger.error(f"Error deleting book suggestions: {str(e)}")
            return False
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            logger.error(f"Unexpected error deleting book suggestions: {str(e)}")
            return False
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def _book_to_dict(self, book):
        """Convert a Book model to a dictionary with all required fields"""
        return {
            'id': book.id,
            'user_id': book.user_id,
            'title': book.title,
            'author': book.author,
            'isbn': book.isbn,
            'current_page': book.current_page,
            'book_metadata': book.book_metadata,
            'is_current': book.is_current
        }

    def get_book(self, book_id, session=None):
        """Get a specific book by ID"""
        session, should_close = self._get_session_for_operation(session)
        try:
            book = session.query(Book).filter_by(id=book_id).first()
            return self._book_to_dict(book) if book else None
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            raise
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def add_chat_message(self, user_id, book_id, message, is_user, character=None, chat_id=None, session=None):
        """Add a chat message"""
        session, should_close = self._get_session_for_operation(session)
        try:
            # <--- Log input parameters --->
            logger.info(f"DB add_chat_message: Received params - user_id={user_id}, book_id={book_id} (type: {type(book_id)}), is_user={is_user}, character={character}, chat_id={chat_id}")
            
            chat_message = ChatMessage(
                user_id=user_id,
                book_id=book_id,
                message=message,
                is_user=is_user,
                character=character,
                chat_id=chat_id or str(datetime.utcnow().timestamp())
            )
            session.add(chat_message)
            session.commit()
            # Refresh to ensure we have all attributes populated
            session.refresh(chat_message)
            
            # Invalidate relevant cache entries
            cache_patterns = [
                get_chat_cache_key(user_id, book_id, chat_id, character),
                get_chat_cache_key(user_id, book_id, None, character),
                get_chat_cache_key(user_id, None, None, character),
                get_chat_cache_key(user_id, book_id, None, None)
            ]
            for pattern in cache_patterns:
                chat_cache.delete(pattern)
            
            return chat_message
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            raise
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def get_chat_history(self, user_id, book_id=None, chat_id=None, character=None, session=None, limit=None):
        """Get chat history for a user, optionally filtered by book_id, chat_id and character"""
        # Check cache first
        cache_key = get_chat_cache_key(user_id, book_id, chat_id, character)
        cached_result = chat_cache.get(cache_key)
        if cached_result is not None:
            logger.debug(f"Cache hit for chat history: {cache_key}")
            if limit and len(cached_result) > limit:
                return cached_result[:limit]
            return cached_result
        
        max_retries = 3
        retry_count = 0
        last_error = None

        while retry_count < max_retries:
            session, should_close = self._get_session_for_operation(session)
            try:
                logger.info(f"DB get_chat_history: Received params - user_id={user_id}, book_id={book_id}, chat_id={chat_id}, character={character}, limit={limit}")
                
                # Build optimized query using indexes
                query = session.query(ChatMessage).filter(ChatMessage.user_id == user_id)
                
                if book_id:
                    query = query.filter(ChatMessage.book_id == book_id)
                if chat_id:
                    query = query.filter(ChatMessage.chat_id == chat_id)
                if character:
                    # Optimized character filter using index
                    query = query.filter(
                        or_(ChatMessage.character == character, ChatMessage.is_user == True)
                    )
                
                # Add ordering and optional limit for performance
                query = query.order_by(ChatMessage.timestamp.asc())
                if limit:
                    query = query.limit(limit)
                
                logger.debug(f"DB get_chat_history: Executing optimized query")
                
                messages = query.all()
                
                logger.info(f"DB get_chat_history: Found {len(messages)} messages for user_id={user_id}, book_id={book_id}")
                
                # Cache the result for future requests
                chat_cache.set(cache_key, messages, ttl=180)  # 3 minutes cache
                
                return messages
            except OperationalError as e:
                last_error = e
                retry_count += 1
                logger.warning(f"Database operation failed, attempt {retry_count} of {max_retries}: {str(e)}")
                if retry_count == max_retries:
                    logger.error(f"Max retries reached for database operation: {str(e)}")
                    raise
                continue
            except SQLAlchemyError as e:
                logger.error(f"Database error in get_chat_history: {str(e)}")
                session.rollback()
                raise
            except Exception as e:
                logger.error(f"Unexpected error in get_chat_history: {str(e)}")
                raise
            finally:
                if should_close:
                    self._cleanup_session(session, should_close)

        if last_error:
            raise last_error
        return None

    def get_chat_history_paginated(self, user_id, book_id=None, chat_id=None, character=None, offset=0, limit=50, session=None):
        """Get paginated chat history with total count"""
        session, should_close = self._get_session_for_operation(session)
        try:
            # Base query for total count
            count_query = session.query(ChatMessage).filter_by(user_id=user_id)
            # Base query for messages
            query = session.query(ChatMessage).filter_by(user_id=user_id)

            # Apply filters
            if book_id:
                count_query = count_query.filter_by(book_id=book_id)
                query = query.filter_by(book_id=book_id)
            if chat_id:
                count_query = count_query.filter_by(chat_id=chat_id)
                query = query.filter_by(chat_id=chat_id)
            if character:
                # Only show user messages that belong to the same chat_id as the character's messages
                character_chat_ids = session.query(ChatMessage.chat_id)\
                    .filter_by(user_id=user_id)\
                    .filter_by(character=character)\
                    .distinct()

                count_query = count_query.filter(
                    or_(
                        and_(ChatMessage.is_user == True, ChatMessage.chat_id.in_(character_chat_ids)),
                        ChatMessage.character == character
                    )
                )
                query = query.filter(
                    or_(
                        and_(ChatMessage.is_user == True, ChatMessage.chat_id.in_(character_chat_ids)),
                        ChatMessage.character == character
                    )
                )

            # Get total count
            total_count = count_query.count()

            # Get paginated results
            messages = query.order_by(ChatMessage.timestamp.desc())\
                          .offset(offset)\
                          .limit(limit)\
                          .all()

            # Reverse messages to maintain chronological order
            messages.reverse()

            return total_count, messages
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            raise
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def clear_chat_history(self, user_id, book_id=None, chat_id=None, session=None):
        """Clear chat history for a user, optionally filtered by book_id and chat_id"""
        session, should_close = self._get_session_for_operation(session)
        try:
            # Build optimized delete query using indexes
            query = session.query(ChatMessage).filter(ChatMessage.user_id == user_id)

            if book_id:
                query = query.filter(ChatMessage.book_id == book_id)
            if chat_id:
                query = query.filter(ChatMessage.chat_id == chat_id)

            # Get count before deletion for logging
            count = query.count()
            logger.info(f"Clearing {count} chat messages for user_id={user_id}, book_id={book_id}, chat_id={chat_id}")

            # Use bulk delete with optimized synchronization
            deleted_count = query.delete(synchronize_session='fetch')
            session.commit()
            
            # Clear all cache entries for this user
            chat_cache.clear()  # Simple approach - clear all cache when history is cleared
            
            logger.info(f"Successfully cleared {deleted_count} chat messages")
            return True
        except SQLAlchemyError as e:
            self._cleanup_session(session, should_close, e)
            logger.error(f"Error clearing chat history: {str(e)}")
            raise
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            raise
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def reset_database(self):
        """Reset the entire database (development only)"""
        if config.ENV == 'development':
            Base.metadata.drop_all(self.engine)
            Base.metadata.create_all(self.engine)

    def set_current_book(self, user_id, book_id, session=None):
        """Set a book as the current book for a user"""
        session, should_close = self._get_session_for_operation(session)
        try:
            # First, unset any current book
            session.query(Book).filter_by(user_id=user_id, is_current=True).update(
                {"is_current": False}
            )

            # Set the new current book
            book = session.query(Book).filter_by(id=book_id, user_id=user_id).first()
            if book:
                book.is_current = True
                session.commit()
                return True
            return False
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            raise
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def get_user_by_email(self, email, session=None):
        """Get user by email using case-insensitive comparison"""
        session, should_close = self._get_session_for_operation(session)
        try:
            user = session.query(User).filter(
                func.lower(User.email) == email.lower()
            ).first()
            return user
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            raise
        finally:
            if should_close:
                self._cleanup_session(session, should_close)

    def add_book(self, user_id, title, author=None, isbn=None, metadata=None, set_as_current=True, session=None):
        """Add a new book to a user's library
        
        Args:
            user_id: ID of the user
            title: Book title
            author: Book author (optional)
            isbn: Book ISBN (optional)
            metadata: Additional book metadata as a dict (optional)
            set_as_current: Whether to set this book as the current book (default: True)
            session: Optional SQLAlchemy session
            
        Returns:
            The newly created Book object
        """
        session, should_close = self._get_session_for_operation(session)
        try:
            # Check if a book with the same title already exists for this user
            existing_book = session.query(Book).filter(
                Book.user_id == user_id,
                func.lower(Book.title) == title.lower()
            ).first()
            
            already_exists = False
            
            if existing_book:
                logger.info(f"Book with title '{title}' already exists for user {user_id}")
                already_exists = True
                book = existing_book
                # Update the metadata if provided
                if metadata:
                    if not book.book_metadata:
                        book.book_metadata = metadata
                    else:
                        # Update existing metadata with new values
                        existing_metadata = book.book_metadata
                        existing_metadata.update(metadata)
                        book.book_metadata = existing_metadata
            else:
                # Create new book
                book = Book(
                    user_id=user_id,
                    title=title,
                    author=author,
                    isbn=isbn,
                    book_metadata=metadata,
                    is_current=False  # Will be updated later if set_as_current is True
                )
                session.add(book)
            
            # If set_as_current is True, set this book as the current book
            if set_as_current:
                # First unset any current book
                session.query(Book).filter_by(
                    user_id=user_id, 
                    is_current=True
                ).update({"is_current": False})
                
                # Set this book as current
                book.is_current = True
            
            session.commit()
            
            # Attach the already_exists flag to the book object
            setattr(book, 'already_exists', already_exists)
            
            return book
        except Exception as e:
            self._cleanup_session(session, should_close, e)
            logger.error(f"Error adding book: {str(e)}")
            raise
        finally:
            if should_close:
                self._cleanup_session(session, should_close)
                
# Create global database instance
db = Database()
