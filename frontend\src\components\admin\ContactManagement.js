import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Button,
  TextField,
  CircularProgress,
  Alert,
  <PERSON>ltip,
  <PERSON>lapse
} from '@mui/material';
import {
  Delete as DeleteIcon,
  MarkEmailRead as MarkReadIcon,
  Visibility as ViewIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import adminService from '../../services/adminService';

// Format relative time (e.g., "2 hours ago", "3 days ago") using vanilla JavaScript
const formatRelativeTime = (date) => {
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);
  const diffInMonths = Math.floor(diffInDays / 30);
  const diffInYears = Math.floor(diffInDays / 365);

  if (diffInSeconds < 60) {
    return 'just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} ${diffInMinutes === 1 ? 'minute' : 'minutes'} ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours} ${diffInHours === 1 ? 'hour' : 'hours'} ago`;
  } else if (diffInDays < 30) {
    return `${diffInDays} ${diffInDays === 1 ? 'day' : 'days'} ago`;
  } else if (diffInMonths < 12) {
    return `${diffInMonths} ${diffInMonths === 1 ? 'month' : 'months'} ago`;
  } else {
    return `${diffInYears} ${diffInYears === 1 ? 'year' : 'years'} ago`;
  }
};

const ContactManagement = () => {
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedContact, setSelectedContact] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [expandedRows, setExpandedRows] = useState({});

  const fetchContacts = async () => {
    setLoading(true);
    try {
      const data = await adminService.getContacts();
      setContacts(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching contacts:', err);
      setError('Failed to load contacts. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchContacts();
  }, []);

  const handleMarkAsRead = async (contactId) => {
    try {
      await adminService.markContactAsRead(contactId);
      setContacts(contacts.map(contact => 
        contact.id === contactId ? { ...contact, is_read: true } : contact
      ));
    } catch (err) {
      console.error('Error marking contact as read:', err);
      setError('Failed to mark contact as read.');
    }
  };

  const handleDeleteContact = async () => {
    if (!selectedContact) return;
    
    try {
      await adminService.deleteContact(selectedContact.id);
      setContacts(contacts.filter(contact => contact.id !== selectedContact.id));
      setOpenDeleteDialog(false);
      setSelectedContact(null);
    } catch (err) {
      console.error('Error deleting contact:', err);
      setError('Failed to delete contact.');
    }
  };

  const handleViewContact = (contact) => {
    setSelectedContact(contact);
    setOpenDialog(true);
    
    // If the contact is not read, mark it as read
    if (!contact.is_read) {
      handleMarkAsRead(contact.id);
    }
  };

  const toggleRowExpansion = (contactId) => {
    setExpandedRows(prev => ({
      ...prev,
      [contactId]: !prev[contactId]
    }));
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Contact Messages</Typography>
        <Button 
          variant="outlined" 
          onClick={fetchContacts}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {contacts.length === 0 ? (
        <Alert severity="info">No contact messages found.</Alert>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Subject</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {contacts.map((contact) => (
                <React.Fragment key={contact.id}>
                  <TableRow 
                    sx={{ 
                      '&:hover': { bgcolor: 'action.hover' },
                      bgcolor: contact.is_read ? 'inherit' : 'rgba(25, 118, 210, 0.08)'
                    }}
                  >
                    <TableCell>{contact.name}</TableCell>
                    <TableCell>{contact.email}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {contact.subject.length > 40 
                          ? `${contact.subject.substring(0, 40)}...` 
                          : contact.subject}
                        <IconButton 
                          size="small" 
                          onClick={() => toggleRowExpansion(contact.id)}
                          sx={{ ml: 1 }}
                        >
                          {expandedRows[contact.id] ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        </IconButton>
                      </Box>
                    </TableCell>
                    <TableCell>
                      {formatRelativeTime(new Date(contact.created_at))}
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={contact.is_read ? "Read" : "Unread"} 
                        color={contact.is_read ? "default" : "primary"} 
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Tooltip title="View Message">
                        <IconButton 
                          size="small" 
                          onClick={() => handleViewContact(contact)}
                          color="primary"
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                      
                      {!contact.is_read && (
                        <Tooltip title="Mark as Read">
                          <IconButton 
                            size="small" 
                            onClick={() => handleMarkAsRead(contact.id)}
                            color="success"
                          >
                            <MarkReadIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                      
                      <Tooltip title="Delete">
                        <IconButton 
                          size="small" 
                          onClick={() => {
                            setSelectedContact(contact);
                            setOpenDeleteDialog(true);
                          }}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell 
                      colSpan={6} 
                      sx={{ 
                        py: 0,
                        borderBottom: expandedRows[contact.id] ? '1px solid rgba(224, 224, 224, 1)' : 'none'
                      }}
                    >
                      <Collapse in={expandedRows[contact.id]} timeout="auto" unmountOnExit>
                        <Box sx={{ py: 2, px: 1 }}>
                          <Typography variant="subtitle2" gutterBottom component="div">
                            Message Preview:
                          </Typography>
                          <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                            {contact.message.length > 200 
                              ? `${contact.message.substring(0, 200)}...` 
                              : contact.message}
                          </Typography>
                          {contact.message.length > 200 && (
                            <Button 
                              size="small" 
                              onClick={() => handleViewContact(contact)}
                              sx={{ mt: 1 }}
                            >
                              Read Full Message
                            </Button>
                          )}
                        </Box>
                      </Collapse>
                    </TableCell>
                  </TableRow>
                </React.Fragment>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* View Contact Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Typography variant="h6">
            Contact Message
          </Typography>
        </DialogTitle>
        <DialogContent>
          {selectedContact && (
            <Box>
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">From</Typography>
                <Typography variant="body1">{selectedContact.name} ({selectedContact.email})</Typography>
              </Box>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">Subject</Typography>
                <Typography variant="body1">{selectedContact.subject}</Typography>
              </Box>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">Date</Typography>
                <Typography variant="body1">
                  {new Date(selectedContact.created_at).toLocaleString()}
                </Typography>
              </Box>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">Message</Typography>
                <TextField
                  multiline
                  fullWidth
                  value={selectedContact.message}
                  InputProps={{
                    readOnly: true,
                  }}
                  variant="outlined"
                  minRows={5}
                  maxRows={15}
                  sx={{ mt: 1 }}
                />
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={openDeleteDialog} onClose={() => setOpenDeleteDialog(false)}>
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this contact message? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>Cancel</Button>
          <Button onClick={handleDeleteContact} color="error">Delete</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ContactManagement;
