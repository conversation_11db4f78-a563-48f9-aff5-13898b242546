<filetree>
BookWorm/
├── alembic.ini
├── overview.md
├── README.md
├── requirements.txt
├── run.py
├── run_local.py  # New local development runner
├── __init__.py
├── backend/
│   ├── app.py
│   ├── config.py
│   ├── ai/
│   │   ├── character_prompts.py
│   │   ├── chat_service.py
│   │   ├── prompt_utils.py
│   │   └── tts_service.py
│   ├── data/
│   │   └── characters.py
│   ├── database/
│   │   ├── db.py
│   │   └── __init__.py
│   ├── routes/
│   │   ├── admin.py         # New admin routes
│   │   ├── ai_book.py
│   │   ├── auth.py
│   │   ├── books.py
│   │   ├── chat.py
│   │   ├── companions.py
│   │   ├── preferences.py
│   │   └── suggestions.py
│   └── tests/
│       ├── __init__.py
│       ├── conftest.py
│       ├── test_chat.py
│       ├── test_auth.py
│       └── test_books.py
├── frontend/
│   ├── package.json
│   ├── package-lock.json
│   ├── public/
│   │   ├── index.html
│   │   └── avatars/
│   └── src/
│       ├── App.js
│       ├── config.js
│       ├── index.js
│       ├── components/      # UI Components
│       ├── context/         # New context directory
│       │   └── AppContext.js
│       ├── sections/        # New sections directory
│       │   ├── AdminSection.js
│       │   ├── ChatSection.js
│       │   ├── CharacterSection.js
│       │   └── LibrarySection.js
│       ├── hooks/           # Restructured hooks
│       ├── theme/           # New theme directory
│       ├── services/
│       │   ├── adminService.js
│       │   ├── authService.js
│       │   ├── bookService.js
│       │   ├── chatService.js
│       │   ├── companionsService.js
│       │   ├── preferencesService.js
│       │   ├── suggestionsService.js
│       │   └── ttsService.js
│       ├── utils/
│       ├── data/
│       └── __tests__/
├── migrations/
│   ├── env.py
│   ├── README
│   ├── script.py.mako
│   └── versions/
└── local.db            # SQLite database for local development
</filetree>

# BookWorm Project Plan

## 1. Project Overview

**BookWorm** is an AI-powered reading companion platform designed to enhance users' literary journeys. Its key objectives are:
- **User Engagement:** Allow users to build a personal library and interact with an AI reading companion.
- **AI Integration:** Provide dynamic AI responses for chat, book suggestions, title corrections, and text-to-speech (TTS) functionalities.
- **Personalization:** Enable users to choose their preferred reading companion whose personality adapts to the conversation.
- **Admin Capabilities:** Admin interface for managing users, books, and chat histories.
- **Robust Backend:** Leverage Flask with JWT authentication, SQLAlchemy ORM, and Alembic migrations.
- **Responsive Frontend:** Use a React-based SPA that offers a smooth, engaging UI with Material-UI components.

## 2. Architecture & File Structure

### Backend
- **Framework:** Flask with modular Blueprints (auth, books, chat, AI, preferences, suggestions, TTS, admin)
- **Database:** PostgreSQL for production, SQLite for development via SQLAlchemy and Alembic
- **Security:** JWT-based authentication, CORS handling, rate limiting (via flask-limiter)
- **AI Services:** Integrated with OpenAI/Anthropic for chat and TTS functionalities
- **Admin Module:** Full management capabilities for users, books, and chat histories

### Frontend
- **Framework:** React with Material-UI and Axios for API calls
- **Structure:** 
  - Core components for user management, library, and chat
  - Sections-based organization:
    - **AdminSection:** Admin dashboard and management interfaces
    - **ChatSection:** Chat interface and controls
    - **LibrarySection:** Book library and management
    - **CharacterSection:** Character/companion selection
  - **Context:** Global state management through React Context API
  - **Theme:** Centralized theming and styling
  - **Hooks:** Custom hooks for state and logic management
  - **Services:** API service modules for backend communication
- **State Management:** 
  - React Context API for global state
  - Custom hooks with efficient memoization
  - Session storage for caching
  - Optimized re-renders with proper dependency management
- **Performance:**
  - Component memoization using React.memo
  - Debounced operations for expensive tasks
  - Dynamic width calculations
  - Efficient message streaming
- **User Experience:** 
  - Fullscreen mode
  - Infinite scroll for chat history
  - Real-time message streaming
  - Responsive design
  - Admin controls for authorized users

*The above filetree section offers a quick glance at the major directories and files, enabling coding agents to quickly navigate the codebase.*

## 3. Core Functionalities

- **User Authentication:**
  - Login flow without passwords (demo mode) using JWT.
  - Persistence of user sessions via localStorage.
  - Admin role identification and authorization.

- **Library Management:**
  - Add, delete, and view books.
  - AI-assisted book verification (title correction and author generation).
  - Admin capabilities for managing all users' books.

- **Chat Interface:**
  - Interactive chat between user and AI companion
  - Modular architecture with focused components
  - Real-time message streaming with progress indicators
  - Efficient state management and caching
  - Responsive design with fullscreen support
  - Infinite scroll for message history
  - Parameter compatibility: ChatService supports both `character` and `character_id` parameters for backward compatibility

- **Companion Preferences:**
  - Users can select a reading companion from a predefined list.
  - Preference is stored on both frontend and backend.
  
- **AI Suggestions:**
  - Based on the user's library, AI generates personalized reading suggestions.
  - Suggestions are cached and rate-limited for efficiency.

- **Admin Dashboard:**
  - User management (view, edit, delete)
  - Book management across all users
  - Chat history management and moderation
  - Access control via admin role

## 4. Technology Stack

- **Backend:** Python, Flask, SQLAlchemy, Alembic, JWT, Redis (for rate limiting), Sentry (error tracking)
- **Frontend:** React, Material-UI, Axios, React Context API
- **AI Integrations:** Openrouter for Text to Speech and Chat (usually using Gemini Flash due to cost). OpenAI for STT and TTS.
- **Database:** PostgreSQL in production or SQLite for development

## 5. Development Guidelines

- **Coding Conventions:** Follow PEP8 for Python; use ESLint/Prettier for React.
- **Rate Limiting:** Ensure API calls respect the configured limits (using custom rate limiter utilities).
- **Error Handling:** Both backend and frontend have comprehensive error handling and logging.
- **Testing:** Unit tests should be added for critical functions (especially AI integrations and database operations).
- **Deployment:** Use separate containers for frontend and backend when deploying; configure CORS and environment variables appropriately.
- **Development Environment:** Use run_local.py for local development with SQLite.

## 6. Future Roadmap

- **Enhanced Chat Context:** Further refine how book context and conversation history are handled.
- **User Profiles:** Extend user management with richer profiles and more secure authentication.
- **Offline Support:** Consider local caching strategies for chat history and book library.
- **Performance Optimization:** Explore server-side rendering or code-splitting for improved load times.
- **UI/UX Enhancements:** Add animations, more themes, and accessibility improvements.
- **Admin Capabilities:** Expand admin dashboard with analytics and advanced user management.

## 7. API Routes Documentation

### Admin Routes (`admin.py`)

#### Route: `/api/admin/check-access`
- **Method**: GET
- **Authentication**: JWT required
- **Description**: Verifies if the current user has admin access.
- **Response**:
  ```json
  {
    "isAdmin": true/false
  }
  ```

#### Route: `/api/admin/users`
- **Method**: GET
- **Authentication**: JWT required (admin)
- **Description**: Retrieves all users.
- **Response**:
  ```json
  [
    {
      "id": "User ID",
      "username": "Username",
      "email": "Email",
      "isAdmin": true/false,
      "created_at": "Timestamp"
    }
  ]
  ```

#### Route: `/api/admin/users/<int:id>`
- **Method**: PUT
- **Authentication**: JWT required (admin)
- **Description**: Updates a user.
- **Request Body**:
  ```json
  {
    "username": "New Username",
    "email": "New Email",
    "isAdmin": true/false
  }
  ```
- **Response**:
  ```json
  {
    "id": "User ID",
    "username": "Username",
    "email": "Email",
    "isAdmin": true/false,
    "message": "User updated successfully"
  }
  ```

#### Route: `/api/admin/users/<int:id>`
- **Method**: DELETE
- **Authentication**: JWT required (admin)
- **Description**: Deletes a user.
- **Response**:
  ```json
  {
    "message": "User deleted successfully"
  }
  ```

#### Route: `/api/admin/books`
- **Method**: GET
- **Authentication**: JWT required (admin)
- **Description**: Retrieves all books across all users.
- **Response**:
  ```json
  [
    {
      "id": "Book ID",
      "title": "Book Title",
      "author": "Author",
      "user_id": "User ID",
      "username": "Username"
    }
  ]
  ```

#### Route: `/api/admin/chat-histories`
- **Method**: GET
- **Authentication**: JWT required (admin)
- **Description**: Retrieves all chat histories.
- **Response**:
  ```json
  [
    {
      "id": "Chat ID",
      "user_id": "User ID",
      "username": "Username",
      "message_count": "Number of Messages",
      "last_message_time": "Timestamp"
    }
  ]
  ```

### AI Book Routes (`ai_book.py`)

#### Route: `/api/ai-book/verify`
- **Method**: POST
- **Authentication**: JWT required
- **Description**: Verifies and corrects book information using OpenAI.
- **Request Body**:
  ```json
  {
    "title": "Book Title"
  }
  ```
- **Response**:
  ```json
  {
    "original_title": "Provided Title",
    "corrected_title": "Corrected Title",
    "generated_author": "Author's Name or null",
    "ai_changes": true/false
  }
  ```

### Auth Routes (`auth.py`)

#### Route: `/api/auth/login`
- **Method**: POST
- **Description**: Handles user login and returns an access token.
- **Request Body**:
  ```json
  {
    "username": "Username"
  }
  ```
- **Response**:
  ```json
  {
    "access_token": "JWT Token"
  }
  ```

#### Route: `/api/auth/me`
- **Method**: GET
- **Authentication**: JWT required
- **Description**: Retrieves the current user's information.
- **Response**:
  ```json
  {
    "id": "User ID",
    "username": "Username",
    "email": "User Email",
    "isAdmin": true/false
  }
  ```

### Books Routes (`books.py`)

#### Route: `/api/books/`
- **Method**: GET
- **Authentication**: JWT required
- **Description**: Retrieves all books for the current user.
- **Response**:
  ```json
  [
    {
      "id": "Book ID",
      "title": "Book Title",
      "author": "Author",
      "isbn": "ISBN",
      "current_page": "Current Page",
      "is_current": true/false,
      "description": "Book Description",
      "metadata": { ... }
    }
  ]
  ```

#### Route: `/api/books/<int:book_id>`
- **Method**: DELETE
- **Authentication**: JWT required
- **Description**: Deletes a specific book.
- **Response**:
  ```json
  {
    "message": "Book deleted successfully"
  }
  ```

#### Route: `/api/books/<int:book_id>/set-current`
- **Method**: POST
- **Authentication**: JWT required
- **Description**: Sets a book as the current book for the user.
- **Response**:
  ```json
  {
    "id": "Book ID",
    "title": "Book Title",
    "author": "Author",
    "isbn": "ISBN",
    "current_page": "Current Page",
    "is_current": true,
    "description": "Book Description",
    "metadata": { ... }
  }
  ```

#### Route: `/api/books/`
- **Method**: POST
- **Authentication**: JWT required
- **Description**: Adds a new book for the current user.
- **Request Body**:
  ```json
  {
    "title": "Book Title",
    "author": "Author",
    "isbn": "ISBN",
    "description": "Book Description",
    "metadata": { ... },
    "set_as_current": true/false
  }
  ```
- **Response**:
  ```json
  {
    "id": "Book ID",
    "title": "Book Title",
    "author": "Author",
    "isbn": "ISBN",
    "current_page": "Current Page",
    "is_current": true/false,
    "description": "Book Description",
    "metadata": { ... }
  }
  ```

### Chat Routes (`chat.py`)

#### Route: `/api/chat/sessions`
- **Method**: GET
- **Authentication**: JWT required
- **Description**: Retrieves chat sessions for the user.
- **Query Parameters**:
  - `book_id`: Optional, filters sessions by book ID
  - `character`: Optional, filters sessions by character
- **Response**:
  ```json
  [
    {
      "chat_id": "Chat Session ID",
      "last_message": "Last Message",
      "timestamp": "Timestamp",
      "character": "Character Name"
    }
  ]
  ```

#### Route: `/api/chat/history`
- **Method**: GET
- **Authentication**: JWT required
- **Description**: Retrieves chat history for a specific session with pagination.
- **Query Parameters**:
  - `book_id`: Optional, filters history by book ID
  - `chat_id`: Optional, filters history by chat session ID
  - `character`: Optional, filters history by character
  - `all`: Optional, if `true`, returns full history without pagination
  - `page`: Optional, specifies the page number
  - `limit`: Optional, specifies messages per page
- **Response**:
  ```json
  {
    "messages": [
      {
        "id": "Message ID",
        "message": "Message Content",
        "is_user": true/false,
        "character": "Character Name",
        "timestamp": "Timestamp",
        "chat_id": "Chat Session ID",
        "book_id": "Book ID"
      }
    ],
    "total": "Total Number of Messages",
    "page": "Current Page",
    "limit": "Messages per Page",
    "has_more": true/false
  }
  ```

#### Route: `/api/chat/`
- **Method**: POST
- **Authentication**: JWT required
- **Description**: Sends a chat message and optionally streams the response.
- **Request Body**:
  ```json
  {
    "message": "Message Content",
    "character": "Character Name",
    "book_id": "Book ID",
    "chat_id": "Chat Session ID",
    "stream": true/false
  }
  ```
- **Response**:
  If `stream` is `false`:
  ```json
  {
    "message": "Response Content",
    "character": "Character Name",
    "chat_id": "Chat Session ID",
    "book_id": "Book ID"
  }
  ```
  If `stream` is `true`: Server-Sent Events (SSE) stream

#### Route: `/api/chat/switch-book`
- **Method**: POST
- **Authentication**: JWT required
- **Description**: Handles book switching within a chat session.
- **Request Body**:
  ```json
  {
    "book_id": "New Book ID",
    "chat_id": "Chat Session ID"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Book switched successfully",
    "book_id": "New Book ID",
    "chat_id": "Chat Session ID"
  }
  ```

#### Route: `/api/chat/clear`
- **Method**: POST
- **Authentication**: JWT required
- **Description**: Clears the chat history for a specific session.
- **Request Body**:
  ```json
  {
    "chat_id": "Chat Session ID"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Chat history cleared successfully",
    "chat_id": "Chat Session ID"
  }
  ```

### Companions Routes (`companions.py`)

#### Route: `/api/companions/`
- **Method**: GET
- **Authentication**: JWT required
- **Description**: Retrieves a list of available companion characters.
- **Response**:
  ```json
  {
    "companions": {
      "1": {
        "id": 1,
        "name": "Ava",
        "gender": "Female",
        "title": "The Cozy Companion",
        "personality": "Ava is calm and comforting, making users feel at ease. She loves discussing books that warm the heart.",
        "interests": ["Heartfelt dramas", "Family sagas", "Inspirational stories"],
        "tone": "Gentle, nurturing, and reassuring",
        "voice": "nova",
        "description": "A warm and nurturing companion who loves discussing heartfelt stories and family sagas."
      }
    },
    "default_companion": {
      "id": 1,
      "name": "Ava",
      "gender": "Female",
      "title": "The Cozy Companion",
      "personality": "Ava is calm and comforting, making users feel at ease. She loves discussing books that warm the heart.",
      "interests": ["Heartfelt dramas", "Family sagas", "Inspirational stories"],
      "tone": "Gentle, nurturing, and reassuring",
      "voice": "nova",
      "description": "A warm and nurturing companion who loves discussing heartfelt stories and family sagas."
    }
  }
  ```

### Preferences Routes (`preferences.py`)

#### Route: `/api/preferences/companion`
- **Method**: GET
- **Authentication**: JWT required
- **Description**: Retrieves the user's preferred companion.
- **Response**:
  ```json
  {
    "companion": "Companion ID",
    "isDefault": true/false
  }
  ```

#### Route: `/api/preferences/companion`
- **Method**: POST
- **Authentication**: JWT required
- **Description**: Sets the user's preferred companion.
- **Request Body**:
  ```json
  {
    "companion": "Companion ID"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Companion preference updated successfully",
    "companion": "Companion ID"
  }
  ```

### Suggestions Routes (`suggestions.py`)

#### Route: `/api/suggestions/`
- **Method**: GET
- **Authentication**: JWT required
- **Description**: Retrieves book suggestions for the user.
- **Query Parameters**:
  - `force_refresh`: Optional, if `true`, generates new suggestions
- **Response**:
  ```json
  {
    "suggestions": [
      {
        "title": "Book Title",
        "author": "Author",
        "description": "Book Description",
        "reason": "Why this book was suggested"
      }
    ]
  }
  ```

## 8. Testing Strategy

### Directory Structure

```
BookWorm/
├── backend/
│   └── tests/
│       ├── __init__.py
│       ├── conftest.py       # Shared test fixtures
│       ├── test_chat.py     # Chat service tests
│       ├── test_auth.py     # Authentication tests
│       └── test_books.py    # Book management tests
└── frontend/
    └── src/
        └── __tests__/
            ├── components/
            │   ├── ChatMessage.test.js
            │   └── BookForm.test.js
            └── hooks/
                └── useChatMessages.test.js
```

### Backend Testing

#### Test Environment
- Uses in-memory SQLite database
- JWT authentication with test secrets
- Mocked external services (OpenAI, Anthropic)

#### Test Categories
1. **Unit Tests**
   - Individual component testing
   - Mocked dependencies
   - Fast execution

2. **Integration Tests**
   - API endpoint testing
   - Database interactions
   - Authentication flows

3. **Service Tests**
   - AI service integration
   - Chat functionality
   - Book management

#### Key Test Files
- `test_chat.py`: Tests chat functionality
  - Chat response generation
  - Message streaming
  - History management
  
- `test_auth.py`: Tests authentication
  - User creation
  - JWT token handling
  - Protected routes
  
- `test_books.py`: Tests book management
  - Book CRUD operations
  - Library management
  - Book suggestions

### Frontend Testing

#### Test Environment
- Jest test runner
- React Testing Library
- Mock Service Worker for API mocking

#### Test Categories
1. **Component Tests**
   - Rendering
   - User interactions
   - State management

2. **Hook Tests**
   - Custom hook behavior
   - State updates
   - Side effects

3. **Integration Tests**
   - Component interactions
   - API integration
   - Route handling

#### Key Test Files
- `ChatMessage.test.js`: Tests chat UI components
- `BookForm.test.js`: Tests book management forms
- `useChatMessages.test.js`: Tests chat hook functionality

### Test Coverage Goals
- Backend: Minimum 80% coverage
- Frontend: Minimum 70% coverage
- Critical paths: 100% coverage

### Continuous Integration
- Run tests on every pull request
- Coverage reports in CI/CD pipeline
- Automated test failure notifications