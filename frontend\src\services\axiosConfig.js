import axios from 'axios';
import { API_BASE_URL } from '../config';
import { supabase } from './supabaseService';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Track auth state for token refresh handling
let isRefreshing = false;

// Add request interceptor to update token before each request
api.interceptors.request.use(
  async (config) => {
    try {
      // Get current session from Supabase
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session?.access_token) {
        config.headers.Authorization = `Bearer ${session.access_token}`;
      } else {
        console.warn('[axiosConfig] No access token found when preparing request.');
        delete config.headers.Authorization;
      }
    } catch (error) {
      console.error('[axiosConfig] Error getting auth session:', error);
      delete config.headers.Authorization;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    const isAuthError = error.response?.status === 401;
    
    if (isAuthError && !originalRequest._retry) {
      originalRequest._retry = true;
      try {
        console.warn('[axiosConfig] 401 detected. Attempting session refresh.');
        const { error: refreshError } = await supabase.auth.refreshSession();
        if (refreshError) {
          window.dispatchEvent(new CustomEvent('auth-error'));
          await supabase.auth.signOut();
          console.error('[axiosConfig] Session refresh error:', refreshError);
          return Promise.reject(refreshError);
        }
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.access_token) {
          originalRequest.headers.Authorization = `Bearer ${session.access_token}`;
        } else {
          console.warn('[axiosConfig] No access token after refresh.');
        }
        return api(originalRequest);
      } catch (refreshError) {
        console.error('[axiosConfig] Session refresh error (catch block):', refreshError);
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }
    return Promise.reject(error);
  }
);

export default api; 