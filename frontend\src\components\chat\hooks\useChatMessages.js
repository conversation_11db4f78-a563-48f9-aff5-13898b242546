import { useState, useCallback, useEffect } from 'react';
import chatService from '../../../services/chatService';

const useChatMessages = (selectedBook, selectedCharacter, userId) => {
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentContext, setCurrentContext] = useState(null);
  const [skipEffects, setSkipEffects] = useState(false);
  const [currentLoadId, setCurrentLoadId] = useState(null);

  // Format messages from backend to frontend format
  const formatMessagesFromBackend = (backendMessages) => {
    console.log('Raw backend messages:', backendMessages); // Debug log
    
    return backendMessages.map(msg => {
      // Ensure we have a timestamp, using current time as fallback
      const timestamp = msg.timestamp || new Date().toISOString();
      
      // Extract message content, ensuring we account for all possible field names
      const content = msg.message || msg.content || msg.text || '';
      
      if (!content && typeof content !== 'string') {
        console.warn('Missing content in message:', msg);
      }
      
      return {
        id: msg.id,
        content: content,
        text: content, // Ensure both fields have the same content
        is_user: msg.is_user === true,
        type: msg.is_user ? 'user' : 'ai',
        character: msg.character,
        timestamp: timestamp,
        chat_id: msg.chat_id,
        book_id: msg.book_id
      };
    });
  };

  useEffect(() => {
    // Skip the effect if skipEffects is true
    if (skipEffects) return;
    
    const loadInitialMessages = async () => {
      if (!selectedBook || !selectedCharacter || !userId) return;

      try {
        setIsLoading(true);
        const response = await chatService.getChatHistory({
          userId,
          bookId: selectedBook.id,
          characterId: selectedCharacter.id
        });

        if (response && response.length > 0) {
          // Format messages to ensure consistent structure
          const formattedMessages = formatMessagesFromBackend(response);
          console.log('Formatted messages for UI:', formattedMessages); // Debug log
          setMessages(formattedMessages);
          
          // Extract chat context from the most recent message
          const lastMessage = response[response.length - 1];
          setCurrentContext({
            chatId: lastMessage.chat_id,
            bookId: lastMessage.book_id,
            characterId: lastMessage.character
          });
        } else {
          setMessages([]);
          setCurrentContext(null);
        }
        
        setHasMore(response && response.length >= 50);
      } catch (err) {
        console.error('Error loading messages:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialMessages();
  }, [selectedBook, selectedCharacter, userId, skipEffects]);

  const loadMoreMessages = useCallback(async () => {
    // Skip if skipEffects is true
    if (skipEffects) return;
    
    if (!selectedBook || !selectedCharacter || !userId || !hasMore || isLoadingMore) return;

    try {
      setIsLoadingMore(true);
      const oldestMessageId = messages[0]?.id;
      
      const response = await chatService.getChatHistory({
        userId,
        bookId: selectedBook.id,
        characterId: selectedCharacter.id,
        before: oldestMessageId
      });

      if (response && response.length > 0) {
        // Format messages to ensure consistent structure
        const formattedMessages = formatMessagesFromBackend(response);
        setMessages(prev => [...formattedMessages, ...prev]);
        setHasMore(response.length >= 50);
      } else {
        setHasMore(false);
      }
    } catch (err) {
      console.error('Error loading more messages:', err);
      setError(err.message);
    } finally {
      setIsLoadingMore(false);
    }
  }, [messages, selectedBook, selectedCharacter, userId, hasMore, isLoadingMore, skipEffects]);

  const onSend = useCallback(async (message, context) => {
    // Skip if skipEffects is true
    if (skipEffects) return;
    
    if (!message || !userId) return;

    const messageObj = {
      id: Date.now(),
      content: message,
      text: message,
      is_user: true,
      type: 'user',
      timestamp: new Date().toISOString()
    };

    try {
      setMessages(prev => [...prev, messageObj]);
      setIsLoading(true);

      const response = await chatService.sendMessage({
        message,
        userId,
        bookId: selectedBook?.id || context?.book?.id,
        characterId: selectedCharacter?.id || context?.character?.id,
        chatId: context?.chatId
      });

      if (response) {
        console.log('Response from chatService.sendMessage:', response); // Debug log
        
        // Ensure we have content from the response
        const responseText = response.text || '';
        
        if (!responseText) {
          console.warn('Empty response text from chat API:', response);
        }
        
        const aiResponse = {
          id: response.chatId || Date.now() + 1,
          content: responseText,
          message: responseText, // Add message field to match backend format
          text: responseText,
          is_user: false,
          type: 'ai',
          timestamp: new Date().toISOString(),
          characterInfo: response.characterInfo
        };

        console.log('Formatted AI response for UI:', aiResponse); // Debug log
        
        setMessages(prev => [...prev.slice(0, -1), messageObj, aiResponse]);
        
        // Update context with the new chat ID
        if (response.chatId) {
          setCurrentContext(prevContext => ({
            ...prevContext,
            chatId: response.chatId,
            bookId: selectedBook?.id || context?.book?.id,
            characterId: selectedCharacter?.id || context?.character?.id
          }));
        }
      }
    } catch (err) {
      console.error('Error sending message:', err);
      setError(err.message);
      // Remove the user message if the AI response failed
      setMessages(prev => prev.slice(0, -1));
    } finally {
      setIsLoading(false);
    }
  }, [userId, selectedBook, selectedCharacter, skipEffects]);

  const clearChat = useCallback(async () => {
    // Skip if skipEffects is true
    if (skipEffects) return;
    
    try {
      await chatService.clearChatHistory({
        userId,
        bookId: selectedBook?.id,
        chatId: currentContext?.chatId
      });
      setMessages([]);
      setCurrentContext(null);
    } catch (err) {
      console.error('Error clearing chat:', err);
      setError(err.message);
    }
  }, [userId, selectedBook, currentContext, skipEffects]);

  return {
    messages,
    isLoading,
    error,
    onSend,
    isLoadingMore,
    hasMore,
    loadMoreMessages,
    clearChat,
    currentContext,
    setSkipEffects
  };
};

export default useChatMessages;
