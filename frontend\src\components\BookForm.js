import React, { useState } from 'react';
import {
  <PERSON>Field,
  Button,
  Box,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Alert,
  IconButton,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import CheckIcon from '@mui/icons-material/Check';
import EditIcon from '@mui/icons-material/Edit';
import bookService from '../services/bookService';

function BookForm({ onAddBook, userId }) {
  const [formData, setFormData] = useState({
    title: '',
    author: '',
    description: '',
  });
  const [confirmationData, setConfirmationData] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [manualEdit, setManualEdit] = useState({
    title: '',
    author: '',
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!userId) {
      alert('Please select a user first before adding a book');
      return;
    }
    
    try {
      const data = await bookService.addBook({
        ...formData,
        user_id: userId,
        use_ai_suggestions: true // Initially try with AI suggestions
      });
      
      // Check if we got AI suggestions
      if (data.ai_changes) {
        // Show confirmation dialog with AI changes
        setConfirmationData({
          ...data,
          originalData: { ...formData }
        });
        setManualEdit({
          title: data.ai_changes.corrected_title || formData.title,
          author: data.ai_changes.generated_author || formData.author,
        });
        setShowConfirmation(true);
        setIsEditing(false);
      } else {
        // No AI changes or AI was skipped, book was added successfully
        onAddBook(data);
        resetForm();
      }
    } catch (error) {
      console.error('Error adding book:', error);
      // Show error to user
      alert(error.message || 'Failed to add book. Please try again.');
    }
  };

  const handleConfirmChanges = async () => {
    if (confirmationData) {
      try {
        // Use the manually edited values if they exist, otherwise use AI suggestions
        // Validate to ensure "No match found" isn't used
        let suggestedTitle = confirmationData.ai_changes.corrected_title;
        let suggestedAuthor = confirmationData.ai_changes.generated_author;
        
        // If AI returned "No match found", use the original user input instead
        if (suggestedTitle === "No match found") {
          suggestedTitle = confirmationData.originalData.title;
        }
        
        if (suggestedAuthor === "No match found") {
          suggestedAuthor = confirmationData.originalData.author;
        }
        
        const finalData = {
          title: isEditing ? manualEdit.title : (suggestedTitle || confirmationData.originalData.title),
          author: isEditing ? manualEdit.author : (suggestedAuthor || confirmationData.originalData.author),
          description: confirmationData.originalData.description,
          user_id: userId,
          use_ai_suggestions: false // Skip AI check on confirmation
        };
        
        const data = await bookService.addBook(finalData);
        onAddBook(data);
        setShowConfirmation(false);
        resetForm();
      } catch (error) {
        console.error('Error confirming changes:', error);
        alert(error.message || 'Failed to add book. Please try again.');
      }
    }
  };

  const handleRejectChanges = async () => {
    try {
      // Use original data without AI suggestions
      const data = await bookService.addBook({
        ...confirmationData.originalData,
        user_id: userId,
        use_ai_suggestions: false
      });
      onAddBook(data);
      setShowConfirmation(false);
      resetForm();
    } catch (error) {
      console.error('Error adding book with original data:', error);
      alert(error.message || 'Failed to add book. Please try again.');
    }
  };

  const handleSaveManualEdit = async () => {
    if (confirmationData) {
      try {
        const data = await bookService.addBook({
          title: manualEdit.title,
          author: manualEdit.author,
          description: confirmationData.originalData.description,
          user_id: userId,
          use_ai_suggestions: false,
          original_id: confirmationData.id  // Pass the ID of the book to replace
        });
        onAddBook(data);
        setShowConfirmation(false);
        setIsEditing(false);
        resetForm();
      } catch (error) {
        console.error('Error adding book with manual edits:', error);
        alert(error.message || 'Failed to add book. Please try again.');
      }
    }
  };

  const handleManualEdit = () => {
    setIsEditing(true);
  };

  const handleManualEditChange = (e) => {
    const { name, value } = e.target;
    setManualEdit(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const resetForm = () => {
    setFormData({
      title: '',
      author: '',
      description: '',
    });
    setConfirmationData(null);
    setManualEdit({
      title: '',
      author: '',
    });
    setIsEditing(false);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleDialogClose = async () => {
    // Delete the temporary book if it exists
    if (confirmationData?.id) {
      try {
        await bookService.deleteBook(confirmationData.id);
      } catch (error) {
        console.error('Error deleting temporary book:', error);
      }
    }
    setShowConfirmation(false);
    setConfirmationData(null);
    setIsEditing(false);
    resetForm();
  };

  return (
    <>
      <Box component="form" onSubmit={handleSubmit} sx={{ minWidth: 340, maxWidth: 420, width: '100%', mx: 'auto', p: 2 }}>
        <Stack spacing={2}>
          <TextField
            required
            fullWidth
            label="Book Title"
            name="title"
            value={formData.title}
            onChange={handleChange}
          />
          <TextField
            fullWidth
            label="Author"
            name="author"
            value={formData.author}
            onChange={handleChange}
          />  
          <Button
            type="submit"
            variant="contained"
            color="secondary"
            startIcon={<AddIcon />}
            sx={{ mt: 2 }}
          >
            Add Book
          </Button>
        </Stack>
      </Box>

      <Dialog 
        open={showConfirmation} 
        onClose={handleDialogClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Review AI-Assisted Changes</DialogTitle>
        <DialogContent>
          {!isEditing ? (
            <>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Our AI assistant has suggested the following changes to ensure accuracy:
              </Typography>

              {confirmationData?.ai_changes?.original_title && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 'bold' }}>
                    Book Title Correction
                  </Typography>
                  <Typography variant="body1" component="div">
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Box>
                        You entered: <strong>{confirmationData.ai_changes.original_title}</strong>
                      </Box>
                      <Box>
                        Suggested correction: <strong>{confirmationData.ai_changes.corrected_title}</strong>
                      </Box>
                    </Box>
                  </Typography>
                </Alert>
              )}
              
              {confirmationData?.ai_changes?.generated_author && (
                <Alert severity="info">
                  <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 'bold' }}>
                    Author Generation
                  </Typography>
                  <Typography variant="body1" component="div">
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      {confirmationData.ai_changes.original_author === null ? (
                        <Box>
                          Since no author was provided, our AI suggests: <strong>{confirmationData.ai_changes.generated_author}</strong>
                        </Box>
                      ) : (
                        <>
                          <Box>
                            You entered: <strong>{confirmationData.ai_changes.original_author}</strong>
                          </Box>
                          <Box>
                            AI suggests: <strong>{confirmationData.ai_changes.generated_author}</strong>
                          </Box>
                        </>
                      )}
                    </Box>
                  </Typography>
                </Alert>
              )}
              
              <Typography sx={{ mt: 3, mb: 2 }}>
                Would you like to use these suggestions, keep your original input, or make manual edits?
              </Typography>
            </>
          ) : (
            <>
              <Typography variant="body1" sx={{ mb: 3 }}>
                Edit the book details manually:
              </Typography>
              <Stack spacing={3}>
                <TextField
                  fullWidth
                  label="Book Title"
                  name="title"
                  value={manualEdit.title}
                  onChange={handleManualEditChange}
                  variant="outlined"
                />
                <TextField
                  fullWidth
                  label="Author"
                  name="author"
                  value={manualEdit.author}
                  onChange={handleManualEditChange}
                  variant="outlined"
                />
              </Stack>
            </>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 2 }}>
          {!isEditing ? (
            <>
              <Button 
                onClick={handleDialogClose}
                color="error"
                variant="outlined"
              >
                Cancel
              </Button>
              <Button 
                onClick={handleRejectChanges} 
                color="secondary"
                variant="outlined"
              >
                Keep My Original Input
              </Button>
              <Button
                onClick={handleManualEdit}
                color="info"
                variant="outlined"
                startIcon={<EditIcon />}
              >
                Edit Manually
              </Button>
              <Button 
                onClick={handleConfirmChanges} 
                color="primary" 
                variant="contained"
                startIcon={<CheckIcon />}
              >
                Accept Suggestions
              </Button>
            </>
          ) : (
            <>
              <Button 
                onClick={handleDialogClose}
                color="error"
                variant="outlined"
              >
                Cancel
              </Button>
              <Button
                onClick={() => setIsEditing(false)}
                color="secondary"
                variant="outlined"
              >
                Back to Suggestions
              </Button>
              <Button
                onClick={handleSaveManualEdit}
                color="primary"
                variant="contained"
                startIcon={<CheckIcon />}
              >
                Save Changes
              </Button>
            </>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
}

export default BookForm;
