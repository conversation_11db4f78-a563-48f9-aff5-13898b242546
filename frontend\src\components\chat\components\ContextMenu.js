import React, { useCallback, useMemo } from 'react';
import { Box, Paper, Stack, IconButton, Tooltip } from '@mui/material';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import CompanionSelect from './CompanionSelect';
import BookSelect from './BookSelect';

const ContextMenu = React.memo(({
  selectedBook,
  selectedCharacter,
  companions,
  isLoadingCompanions,
  onCompanionChange,
  books,
  isLoadingBooks,
  onBookChange,
  onClearChat
}) => {
  // Memoize the companion change handler to prevent recreating on every render
  const handleCompanionChange = useCallback((companionId) => {
    if (!companionId) return;
    const companion = companions?.find(c => c.id.toString() === companionId.toString());
    if (companion) {
      onCompanionChange(companion);
    }
  }, [companions, onCompanionChange]);

  // Extract just the IDs for BookSelect and CompanionSelect to minimize props
  const selectedBookId = useMemo(() => selectedBook?.id, [selectedBook]);
  const selectedCharacterId = useMemo(() => selectedCharacter?.id, [selectedCharacter]);

  return (
    <Paper 
      elevation={1} 
      sx={{ 
        p: 2, 
        mb: 2, 
        backgroundColor: '#f5f5f5',
        borderRadius: '8px'
      }}
    >
      <Stack spacing={2}>
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'flex-start',
          gap: 2,
          flexWrap: 'wrap'
        }}>
          <Box sx={{ flex: '1 1 auto', minWidth: '400px', maxWidth: '800px' }}>
            <BookSelect
              value={selectedBookId}
              onChange={onBookChange}
              disabled={isLoadingBooks}
              books={books}
              isLoading={isLoadingBooks}
            />
          </Box>
          <Box sx={{ flex: '0 0 auto', display: 'flex', alignItems: 'center', gap: 1 }}>
            <CompanionSelect
              value={selectedCharacterId || ''}
              onChange={handleCompanionChange}
              disabled={isLoadingCompanions}
              companions={companions}
              isLoading={isLoadingCompanions}
            />
            <Tooltip title="Clear chat history">
              <IconButton color="error" onClick={onClearChat} aria-label="Clear chat history">
                <DeleteOutlineIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </Stack>
    </Paper>
  );
}, (prevProps, nextProps) => {
  // More precise equality check
  const bookIdEqual = prevProps.selectedBook?.id === nextProps.selectedBook?.id;
  const characterIdEqual = prevProps.selectedCharacter?.id === nextProps.selectedCharacter?.id;
  const loadingEqual = 
    prevProps.isLoadingCompanions === nextProps.isLoadingCompanions &&
    prevProps.isLoadingBooks === nextProps.isLoadingBooks;
  
  // Only check reference equality for arrays and functions
  const companionsEqual = prevProps.companions === nextProps.companions;
  const booksEqual = prevProps.books === nextProps.books;
  const handlersEqual = 
    prevProps.onCompanionChange === nextProps.onCompanionChange &&
    prevProps.onBookChange === nextProps.onBookChange &&
    prevProps.onClearChat === nextProps.onClearChat;
  
  return bookIdEqual && characterIdEqual && loadingEqual && 
         companionsEqual && booksEqual && handlersEqual;
});

export default ContextMenu;