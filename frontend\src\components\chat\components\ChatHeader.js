import React from 'react';
import { Box } from '@mui/material';
import BookSelect from './BookSelect';
import CompanionSelect from './CompanionSelect';

const ChatHeader = ({
  selectedBook,
  selectedCharacter,
  books,
  companions,
  onBookChange,
  onCharacterChange,
  isLoading
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        gap: 2,
        p: 2,
        alignItems: 'center',
        borderBottom: '1px solid',
        borderColor: 'divider'
      }}
    >
      <BookSelect
        value={selectedBook}
        onChange={onBookChange}
        disabled={isLoading}
        books={books}
        isLoading={isLoading}
      />
      <CompanionSelect
        value={selectedCharacter}
        onChange={onCharacterChange}
        disabled={isLoading || !selectedBook}
        companions={companions}
        isLoading={isLoading}
      />
    </Box>
  );
};

export default React.memo(ChatHeader);
