import { useState, useRef, useEffect } from 'react';
import bookService from '../services/bookService';
import chatService from '../services/chatService';

// Track error logging to prevent console spam
const errorLog = {
  timestamp: 0,
  interval: 30000, // 30 seconds between logs
  shouldLog: function() {
    const now = Date.now();
    if (now - this.timestamp > this.interval) {
      this.timestamp = now;
      return true;
    }
    return false;
  }
};

/**
 * Custom hook for managing book-related state and functions
 */
const useBookManagement = () => {
  const [books, setBooks] = useState([]);
  const [selectedBook, setSelectedBook] = useState(null);
  const [selectedBooks, setSelectedBooks] = useState([]);
  const [showBookForm, setShowBookForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const loadingRef = useRef(false);
  const [authReady, setAuthReady] = useState(false);

  // Listen for auth-ready event
  useEffect(() => {
    const handleAuthReady = () => {
      setAuthReady(true);
      // Try to load books once auth is ready, with a slight delay
      setTimeout(() => {
        loadBooks();
      }, 300); // 300ms delay to ensure auth is fully established
    };
    
    // Listen for auth-ready events
    window.addEventListener('auth-ready', handleAuthReady);
    
    // Also listen for auth errors to know when to clear books
    const handleAuthError = () => {
      setBooks([]);
      setSelectedBook(null);
      setSelectedBooks([]);
    };
    
    window.addEventListener('auth-error', handleAuthError);
    
    return () => {
      window.removeEventListener('auth-ready', handleAuthReady);
      window.removeEventListener('auth-error', handleAuthError);
    };
  }, []);

  // Load books for a user
  const loadBooks = async () => {
    // Prevent multiple simultaneous calls
    if (loadingRef.current) return;
    
    loadingRef.current = true;
    setLoading(true);
    
    try {
      const booksData = await bookService.getBooks();
      setBooks(booksData);
    } catch (error) {
      if (error.response?.status === 401) {
        // Authentication error - clear books
        setBooks([]);
      } else if (errorLog.shouldLog()) {
        console.error('Error loading books:', error);
      }
    } finally {
      setLoading(false);
      loadingRef.current = false;
    }
  };

  // Handle book selection
  const handleBookSelect = async (book) => {
    if (!book || !book.id) {
      // Only log genuine errors
      if (errorLog.shouldLog()) {
        console.error('Missing book data:', book);
      }
      return;
    }

    // Store only necessary book data to avoid passing full object to chat history
    const selectedBookData = {
      id: book.id,
      title: book.title,
      author: book.author,
      chatHistory: book.chatHistory
    };

    setSelectedBook(selectedBookData);
    setSelectedBooks(prev => {
      // Check if book is already in the array
      if (prev.some(b => b.id === book.id)) {
        return prev.filter(b => b.id !== book.id);
      }
      return [...prev, selectedBookData];
    });

    // --- Remember last selected book ---
    if (book.id) {
      localStorage.setItem('lastSelectedBookId', book.id);
    }

    // Set as current book in backend
    try {
      const result = await bookService.switchBook(book.id);

      // If we got chat history from switchBook, update the selected book
      if (result?.chatHistory) {
        setSelectedBook(prev => ({
          ...prev,
          chatHistory: result.chatHistory
        }));
      }
    } catch (error) {
      // Only log once per time interval
      if (errorLog.shouldLog()) {
        console.error('Error switching book:', error);
      }
    }
  };

  // Handle book deletion
  const handleDeleteBook = async (bookId) => {
    if (!bookId) {
      if (errorLog.shouldLog()) {
        console.error('Missing bookId:', bookId);
      }
      return;
    }

    try {
      // First update local state to give immediate feedback
      setBooks(prevBooks => prevBooks.filter(book => book.id !== bookId));
      setSelectedBooks(prevSelected => prevSelected.filter(book => book.id !== bookId));
      if (selectedBook?.id === bookId) {
        setSelectedBook(null);
      }

      // Then perform the actual deletion
      await bookService.deleteBook(bookId);
    } catch (error) {
      if (errorLog.shouldLog()) {
        console.error('Error deleting book:', error);
      }
      // Only reload books on non-auth errors
      if (error.response?.status !== 401) {
        loadBooks();
      }
    }
  };

  // Handle adding a book
  const handleAddBook = async () => {
    try {
      // Refresh the book list
      await loadBooks();
    } catch (error) {
      if (errorLog.shouldLog()) {
        console.error('Error refreshing books:', error);
      }
    }
  };

  // Load chat history for a book
  const loadChatHistory = async (bookId) => {
    if (!bookId) return;
    
    try {
      // Load chat history
      const history = await chatService.getChatHistory(bookId);
      // Update selected book with chat history
      setSelectedBook(prev => ({
        ...prev,
        chatHistory: history
      }));
    } catch (error) {
      if (errorLog.shouldLog()) {
        console.error('Error loading chat history:', error);
      }
    }
  };

  // Clear book data (used when changing users)
  const clearBookData = () => {
    setBooks([]);
    setSelectedBook(null);
    setSelectedBooks([]);
  };

  return {
    books,
    selectedBook,
    selectedBooks,
    showBookForm,
    setShowBookForm,
    loading,
    loadBooks,
    handleBookSelect,
    handleDeleteBook,
    handleAddBook,
    loadChatHistory,
    clearBookData,
    authReady
  };
};

export default useBookManagement; 