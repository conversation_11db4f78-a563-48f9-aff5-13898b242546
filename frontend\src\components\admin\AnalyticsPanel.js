import React, { useEffect, useState } from 'react';
import { Box, Typography, Paper, CircularProgress, Alert, Grid, Card, CardContent, Divider } from '@mui/material';
import adminService from '../../services/adminService';

const MetricCard = ({ title, value, color }) => (
  <Card sx={{ minWidth: 180, background: color || '#f5f5f5', m: 1 }}>
    <CardContent>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        {title}
      </Typography>
      <Typography variant="h4" color="primary">
        {value}
      </Typography>
    </CardContent>
  </Card>
);

const AnalyticsPanel = () => {
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchAnalytics = async () => {
      setLoading(true);
      try {
        const data = await adminService.getAnalytics();
        setMetrics(data);
        setError(null);
      } catch (e) {
        setError('Failed to load analytics');
      } finally {
        setLoading(false);
      }
    };
    fetchAnalytics();
  }, []);

  if (loading) {
    return <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}><CircularProgress /></Box>;
  }
  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }
  if (!metrics) {
    return null;
  }

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h5" sx={{ mb: 2 }}>
        Analytics Overview
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard title="Total Users" value={metrics.userCount} color="#e3f2fd" />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard title="Total Books" value={metrics.bookCount} color="#fce4ec" />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard title="User Messages" value={metrics.userMessageCount ?? metrics.messageCount} color="#e8f5e9" />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard title="AI Responses" value={metrics.aiMessageCount ?? metrics.messageCount} color="#fffde7" />
        </Grid>
      </Grid>
      
      {/* User Growth Section */}
      <Box sx={{ mt: 4 }}>
        <Divider sx={{ mb: 2 }} />
        <Typography variant="h5" sx={{ mb: 2 }}>
          User Growth Analytics
        </Typography>
        <Card sx={{ p: 2, bgcolor: '#f8f9fa' }}>
          <CardContent>
            <Grid container spacing={2}>
              {metrics.userGrowth && metrics.userGrowth.map((day) => (
                <Grid item xs={6} sm={3} md={1.7} key={day.date}>
                  <Box sx={{ 
                    textAlign: 'center', 
                    p: 1, 
                    borderRadius: 1,
                    bgcolor: day.count > 0 ? '#e3f2fd' : 'transparent'
                  }}>
                    <Typography variant="body2" color="text.secondary">{day.date}</Typography>
                    <Typography variant="h5" color={day.count > 0 ? 'primary' : 'text.secondary'}>
                      {day.count}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      new users
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </Box>
      
      {/* Companion Usage Section */}
      <Box sx={{ mt: 4 }}>
        <Divider sx={{ mb: 2 }} />
        <Typography variant="h5" sx={{ mb: 2 }}>
          Companion Usage Analytics
        </Typography>
        <Grid container spacing={2}>
          {metrics.companionUsage && metrics.companionUsage.map((companion) => (
            <Grid item xs={12} sm={6} md={4} key={companion.id}>
              <Card sx={{ 
                p: 1, 
                display: 'flex', 
                flexDirection: 'column',
                height: '100%',
                backgroundColor: companion.messageCount > 0 ? '#f0f7ff' : '#f5f5f5'
              }}>
                <CardContent>
                  <Typography variant="h6" color="primary">
                    {companion.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Companion ID: {companion.id}
                  </Typography>
                  <Typography variant="h4" color={companion.messageCount > 0 ? 'primary' : 'text.secondary'}>
                    {companion.messageCount}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    messages
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Paper>
  );
};

export default AnalyticsPanel;
