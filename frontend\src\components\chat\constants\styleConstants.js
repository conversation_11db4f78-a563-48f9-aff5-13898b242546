export const MENU_PROPS = {
  anchorOrigin: {
    vertical: 'bottom',
    horizontal: 'left',
  },
  transformOrigin: {
    vertical: 'top',
    horizontal: 'left',
  },
  PaperProps: {
    style: {
      maxHeight: 300,
      marginTop: 8
    }
  },
  MenuListProps: {
    dense: true,
    sx: {
      padding: 0,
      '& .MuiMenuItem-root': {
        minHeight: 40,
        padding: '8px 16px'
      }
    }
  },
  slotProps: {
    paper: {
      elevation: 8,
      sx: {
        mt: 1,
        '& .MuiList-root': {
          padding: 0
        }
      }
    }
  }
};

export const CHAT_CONTAINER_STYLES = {
  display: 'flex',
  flexDirection: 'column',
  height: '100%',
  position: 'relative'
};

export const MESSAGE_LIST_STYLES = {
  flex: 1,
  overflowY: 'auto',
  padding: 2,
  '&::-webkit-scrollbar': {
    width: '8px'
  },
  '&::-webkit-scrollbar-track': {
    background: '#f1f1f1'
  },
  '&::-webkit-scrollbar-thumb': {
    background: '#888',
    borderRadius: '4px'
  }
};
