from fastapi import APIRouter, Depends, Request, HTTPException, status
from backend.database.db import db
from backend.utils.decorators import get_current_user
from backend.ai import character
from backend.data.characters import DEFAULT_CHARACTER
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/preferences", tags=["preferences"])


@router.get("/companion", status_code=status.HTTP_200_OK)
async def get_companion_preference(user=Depends(get_current_user)):
    pref = db.get_user_preference(user.id)
    if pref:
        companion_id = pref.preferred_companion
        companion_char = character.get_character(companion_id)
        if companion_char:
            return {
                "id": companion_id,
                "info": companion_char.get_info()
            }
    # Default to configured character if no preference or character not found
    default_char = character.get_character(DEFAULT_CHARACTER) or character.CHAR_AVA
    return {
        "id": default_char.id,
        "info": default_char.get_info()
    }


@router.post("/companion", status_code=status.HTTP_201_CREATED)
async def set_companion_preference(request: Request, user=Depends(get_current_user)):
    data = await request.json()
    if not data or 'companion_id' not in data:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail="Companion ID is required")
    companion_id = data['companion_id']
    try:
        success = db.set_user_preference(user.id, companion_id)
        if success:
            # Get the character info to return
            companion_char = character.get_character(companion_id)
            if companion_char:
                return {
                    "message": "Preference updated successfully",
                    "companion": {
                        "id": companion_id,
                        "info": companion_char.get_info()
                    }
                }
            else:
                raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Character not found")
        else:
            raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update preference")
    except Exception as e:
        logger.error(f"Error setting companion preference: {str(e)}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.put("/", status_code=status.HTTP_200_OK)
async def update_preferences(request: Request, user=Depends(get_current_user)):
    """Update user preferences"""
    data = await request.json()
    if not data:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail="No data provided")
    # For now, just update companion preference if provided
    if 'companion_id' in data:
        try:
            success = db.set_user_preference(user.id, data['companion_id'])
            if not success:
                raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update companion preference")
        except Exception as e:
            logger.error(f"Error updating preferences: {str(e)}")
            raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")
    return {"message": "Preferences updated successfully"}