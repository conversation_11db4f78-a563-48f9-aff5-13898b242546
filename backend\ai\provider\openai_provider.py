"""OpenAI provider implementation for the BookWorm application."""
from openai import OpenAI
import traceback
from typing import Dict, List, Any, Generator, Optional
import logging
from backend.ai.provider.base_provider import BaseProvider


class OpenAIProvider(BaseProvider):
    """Provider implementation for OpenAI API."""
    
    def __init__(self, api_key: str, model: str, logger: Optional[logging.Logger] = None):
        """Initialize the OpenAI provider.
        
        Args:
            api_key: OpenAI API key
            model: Model name to use
            logger: Logger instance
        """
        super().__init__(model, logger or logging.getLogger(__name__))
        self.client = OpenAI(api_key=api_key)
        self.logger.info("OpenAI client initialized successfully")
    
    def chat(self, messages: List[Dict[str, str]], system_prompt: str, 
             max_tokens: int, temperature: float, stream: bool) -> Any:
        """Send a chat request to the OpenAI API.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            system_prompt: The system prompt to use
            max_tokens: Maximum number of tokens in the response
            temperature: Temperature for response generation
            stream: Whether to stream the response
            
        Returns:
            The response from the OpenAI API
        """
        try:
            # Add system prompt to messages
            full_messages = [{"role": "system", "content": system_prompt}]
            full_messages.extend(messages)
            
            self.logger.debug(f"Sending chat request to OpenAI with {len(full_messages)} messages")
            
            completion = self.client.chat.completions.create(
                model=self.model,
                messages=full_messages,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=stream
            )
            
            return completion
        except Exception as e:
            self.logger.error(f"OpenAI API call error: {str(e)}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            
            if "api key" in str(e).lower():
                return f"Invalid API key for OpenAI"
            elif "rate limit" in str(e).lower():
                return f"Rate limit exceeded for OpenAI"
            else:
                return f"Error calling OpenAI API: {str(e)}"
    
    def process_response(self, response: Any) -> str:
        """Process the response from the OpenAI API.
        
        Args:
            response: The response from the OpenAI API
            
        Returns:
            The processed response as a string
        """
        try:
            self.logger.debug(f"Processing OpenAI response of type: {type(response)}")
            
            # Handle error responses
            if isinstance(response, str) and ("error" in response.lower() or "invalid" in response.lower()):
                return response
            
            # Handle OpenAI response object
            if hasattr(response, 'choices') and len(response.choices) > 0:
                message = response.choices[0].message
                if hasattr(message, 'content'):
                    return message.content
                else:
                    self.logger.warning(f"Message missing content attribute: {message}")
                    return str(message)
            
            # Fallback for unexpected response format
            self.logger.warning(f"Unexpected response format: {response}")
            if isinstance(response, dict):
                if 'content' in response:
                    return response['content']
                elif 'message' in response:
                    return response['message']
                elif 'choices' in response and len(response['choices']) > 0:
                    choice = response['choices'][0]
                    if 'message' in choice and 'content' in choice['message']:
                        return choice['message']['content']
            
            # Last resort fallback
            return "I'm sorry, I couldn't generate a proper response. Please try again."
        
        except Exception as e:
            self.logger.error(f"Error processing OpenAI response: {str(e)}")
            self.logger.error(f"Response that caused error: {response}")
            return f"Error processing response: {str(e)}"
    
    def process_stream(self, response: Any, chat_id: Optional[str] = None) -> Generator[Dict[str, Any], None, None]:
        """Process a streaming response from the OpenAI API.
        
        Args:
            response: The streaming response from the OpenAI API
            chat_id: Optional chat ID to include in the final chunk
            
        Returns:
            Generator yielding chunks of the response
        """
        for chunk in response:
            if hasattr(chunk, 'choices') and len(chunk.choices) > 0:
                delta = chunk.choices[0].delta
                if hasattr(delta, 'content') and delta.content:
                    yield {'content': delta.content}
                elif not hasattr(delta, 'content') or delta.content == "":
                    # This is likely the final chunk
                    yield {'done': True, 'chat_id': chat_id}
            else:
                # Handle dictionary format
                if isinstance(chunk, dict):
                    if 'choices' in chunk and len(chunk['choices']) > 0:
                        delta = chunk['choices'][0].get('delta', {})
                        if 'content' in delta and delta['content']:
                            yield {'content': delta['content']}
                        elif 'content' not in delta or not delta['content']:
                            # This is likely the final chunk
                            yield {'done': True, 'chat_id': chat_id}
