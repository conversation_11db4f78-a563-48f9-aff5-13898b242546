"""Anthropic provider implementation for the BookWorm application."""
from anthropic import Anthropic
import traceback
from typing import Dict, List, Any, Generator, Optional
import logging
from backend.ai.provider.base_provider import BaseProvider


class AnthropicProvider(BaseProvider):
    """Provider implementation for Anthropic API."""
    
    def __init__(self, api_key: str, model: str, logger: Optional[logging.Logger] = None):
        """Initialize the Anthropic provider.
        
        Args:
            api_key: Anthropic API key
            model: Model name to use
            logger: Logger instance
        """
        super().__init__(model, logger or logging.getLogger(__name__))
        try:
            self.client = Anthropic(api_key=api_key)
            self.logger.info("Anthropic client initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize Anthropic client: {str(e)}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            raise ValueError(f"Failed to initialize Anthropic client: {str(e)}")
    
    def chat(self, messages: List[Dict[str, str]], max_tokens: int, temperature: float, stream: bool) -> Any:
        """Send a chat request to the Anthropic API."""
        try:
            self.logger.debug(f"Sending chat request to Anthropic with {len(messages)} messages")
            
            # Extract system prompt from messages if present
            system_prompt = None
            chat_messages = []
            
            for msg in messages:
                if msg["role"] == "system":
                    system_prompt = msg["content"]
                else:
                    chat_messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
            
            response = self.client.messages.create(
                model=self.model,
                max_tokens=max_tokens,
                system=system_prompt,
                messages=chat_messages,
                stream=stream
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"Anthropic API call error: {str(e)}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            
            if "api key" in str(e).lower():
                return f"Invalid API key for Anthropic"
            elif "rate limit" in str(e).lower():
                return f"Rate limit exceeded for Anthropic"
            else:
                return f"Error calling Anthropic API: {str(e)}"
    
    def process_response(self, response: Any) -> str:
        """Process the response from the Anthropic API.
        
        Args:
            response: The response from the Anthropic API
            
        Returns:
            The processed response as a string
        """
        try:
            self.logger.debug(f"Processing Anthropic response of type: {type(response)}")
            
            # Handle error responses
            if isinstance(response, str) and ("error" in response.lower() or "invalid" in response.lower()):
                return response
            
            # Handle Anthropic response object
            if not response.content:
                raise ValueError("Empty response from Anthropic API")
            
            content = response.content[0].text if response.content else ''
            return content
        
        except Exception as e:
            self.logger.error(f"Error processing Anthropic response: {str(e)}")
            self.logger.error(f"Response that caused error: {response}")
            return f"Error processing response: {str(e)}"
    
    def process_stream(self, response: Any, chat_id: Optional[str] = None) -> Generator[Dict[str, Any], None, None]:
        """Process a streaming response from the Anthropic API.
        
        Args:
            response: The streaming response from the Anthropic API
            chat_id: Optional chat ID to include in the final chunk
            
        Returns:
            Generator yielding chunks of the response
        """
        for chunk in response:
            if chunk.type == 'content_block_start':
                continue
            elif chunk.type == 'content_block_delta':
                if hasattr(chunk.delta, 'type') and chunk.delta.type == 'text_delta':
                    yield {'content': chunk.delta.text}
            elif chunk.type == 'content_block_stop':
                continue
            elif chunk.type == 'message_delta':
                if hasattr(chunk.delta, 'stop_reason'):
                    continue
            elif chunk.type == 'message_stop':
                yield {'done': True, 'chat_id': chat_id}
