import React, { useState, useEffect } from 'react';
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Paper,
  Alert,
  CircularProgress,
  Badge
} from '@mui/material';
import UserManagement from './UserManagement';
import ChatHistoryManagement from './ChatHistoryManagement';
import AnalyticsPanel from './AnalyticsPanel';

import ContactManagement from './ContactManagement';
import adminService from '../../services/adminService';
import supabaseAuth, { supabase } from '../../services/supabaseService';

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`admin-tabpanel-${index}`}
      aria-labelledby={`admin-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function AdminPanel() {
  const [currentTab, setCurrentTab] = useState(0);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [unreadContacts, setUnreadContacts] = useState(0);

  useEffect(() => {
    const checkAccess = async () => {
      try {
        // Check if we have a valid Supabase session
        const session = await supabaseAuth.getSession();
        if (!session) {
          setIsAdmin(false);
          setError('Please log in first');
          setIsLoading(false);
          return;
        }

        // Get user from Supabase
        const user = await supabaseAuth.getCurrentUser();
        if (!user) {
          setIsAdmin(false);
          setError('Please log in first');
          setIsLoading(false);
          return;
        }

        // Check if the user has admin access in app_metadata or via API
        const isAdminUser = user.app_metadata?.isAdmin || false;
        
        if (isAdminUser) {
          setIsAdmin(true);
          // Fetch unread contacts count
          fetchUnreadContacts();
        } else {
          // Fallback to API check
          const hasAccess = await adminService.checkAdminAccess();
          setIsAdmin(hasAccess);
          if (!hasAccess) {
            setError('You do not have admin privileges');
          } else {
            // Fetch unread contacts count
            fetchUnreadContacts();
          }
        }
      } catch (err) {
        console.error('Admin access check error:', err);
        setError('Failed to verify admin access');
        setIsAdmin(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAccess();
  }, []);

  const fetchUnreadContacts = async () => {
    try {
      const contacts = await adminService.getContacts();
      const unreadCount = contacts.filter(contact => !contact.is_read).length;
      setUnreadContacts(unreadCount);
    } catch (err) {
      console.error('Error fetching unread contacts:', err);
    }
  };

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!isAdmin) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          {error || 'You do not have permission to access the admin panel.'}
        </Alert>
      </Box>
    );
  }

  return (
    <Paper sx={{ width: '100%', height: '100vh', overflow: 'auto' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h4" sx={{ p: 2 }}>
          Admin Panel
        </Typography>
        <Tabs value={currentTab} onChange={handleTabChange}>
          <Tab label="Users" />
          <Tab label="Chat History" />
          <Tab label="Analytics" />

          <Tab 
            label={
              <Badge badgeContent={unreadContacts} color="error" max={99}>
                <Box sx={{ pr: unreadContacts ? 1 : 0 }}>Contacts</Box>
              </Badge>
            } 
          />
        </Tabs>
      </Box>

      {error && (
        <Alert severity="error" sx={{ m: 2 }}>
          {error}
        </Alert>
      )}

      <TabPanel value={currentTab} index={0}>
        <UserManagement />
      </TabPanel>
      <TabPanel value={currentTab} index={1}>
        <ChatHistoryManagement />
      </TabPanel>
      <TabPanel value={currentTab} index={2}>
        <AnalyticsPanel />
      </TabPanel>
      <TabPanel value={currentTab} index={3}>
        <ContactManagement />
      </TabPanel>
    </Paper>
  );
}

export default AdminPanel; 