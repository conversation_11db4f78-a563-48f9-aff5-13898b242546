import React, { useState, useEffect } from 'react';
import { ThemeProvider } from '@mui/material';
import defaultTheme from './index';
import mobileTheme from './mobile';

// Device detection constants
const IPHONE_REGEX = /iPhone/i;
const MOBILE_REGEX = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;

/**
 * ResponsiveThemeProvider component that provides the appropriate theme based on device
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @returns {React.ReactElement} ThemeProvider with appropriate theme
 */
const ResponsiveThemeProvider = ({ children }) => {
  // Initialize with a default state, will be updated on mount
  const [deviceInfo, setDeviceInfo] = useState({
    isMobile: false,
    isIPhone: false,
    viewportWidth: 0,
    viewportHeight: 0
  });

  // Detect device type and viewport size on mount
  useEffect(() => {
    const detectDevice = () => {
      const userAgent = navigator.userAgent;
      const isMobile = MOBILE_REGEX.test(userAgent);
      const isIPhone = IPHONE_REGEX.test(userAgent);
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      setDeviceInfo({
        isMobile,
        isIPhone,
        viewportWidth,
        viewportHeight
      });
    };

    // Initial detection
    detectDevice();

    // Re-detect on resize
    const handleResize = () => {
      detectDevice();
    };

    window.addEventListener('resize', handleResize);
    
    // Clean up event listener
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Choose the appropriate theme based on device detection
  const theme = deviceInfo.isMobile ? mobileTheme : defaultTheme;

  // Add device info to the theme for components to access
  const themeWithDeviceInfo = {
    ...theme,
    deviceInfo
  };

  return (
    <ThemeProvider theme={themeWithDeviceInfo}>
      {children}
    </ThemeProvider>
  );
};

export default ResponsiveThemeProvider;
