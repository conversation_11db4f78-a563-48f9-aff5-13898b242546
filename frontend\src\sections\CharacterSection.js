import React from 'react';
import { Box, Paper, Typography } from '@mui/material';
import { useAppContext } from '../context/AppContext';
import CharacterSelection from '../components/CharacterSelection';

/**
 * Character Selection Section component
 */
const CharacterSection = ({ selected<PERSON><PERSON>cter, setSelectedCharacter, currentUser }) => {
  // Use companions from context
  const { companions, companionsLoading, companionsError } = useAppContext();

  // Debug companions data
  React.useEffect(() => {
    console.log('CharacterSection received companions from context:', companions);
  }, [companions]);

  // Handle character selection
  const handleSelectCharacter = async (character) => {
    console.log('Selected character:', character);
    setSelectedCharacter(character);
    if (currentUser && character) {
      try {
        // Save preference via backend if needed
        // preferencesService.setCompanionPreference(character.id); // (already handled elsewhere if needed)
      } catch (error) {
        console.error('Error saving companion preference:', error);
      }
    }
  };

  if (companionsLoading) return <div>Loading companions...</div>;
  if (companionsError) return <div>{companionsError}</div>;

  return (
    <Paper sx={{ p: 0, bgcolor: '#fafafa', borderRadius: '16px' }}>
      <Box sx={{ bgcolor: 'primary.main', p: 2, borderTopLeftRadius: '16px', borderTopRightRadius: '16px' }}>
        <Typography variant="h6" align="center" sx={{ color: 'black' }}>
          Choose Your Reading Companion
        </Typography>
      </Box>
      <Box sx={{ p: 3 }}>
        <CharacterSelection
          onSelectCharacter={handleSelectCharacter}
          selectedCharacter={selectedCharacter}
          companions={companions}
          key={currentUser?.id} // Prevent unnecessary re-renders
        />
      </Box>
    </Paper>
  );
};

export default CharacterSection;