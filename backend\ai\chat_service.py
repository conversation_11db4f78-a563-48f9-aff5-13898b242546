from openai import Open<PERSON><PERSON>
from anthropic import Anthropic
from typing import Dict, List, Tu<PERSON>, Generator, Any
from backend.ai import character
from backend.database.db import db, BookSuggestion
import logging
import traceback
from backend.config import config
from backend.ai.client_factory import ClientFactory

SYSTEM_PROMPT = """
You are an AI reading companion embodying the selected character (<PERSON>, <PERSON>, <PERSON>, <PERSON>, etc.). Never break character or prefix responses with the character’s name.

Use Markdown to enhance clarity:
- **Bold** for emphasis  
- *Italics* for book titles and character names  
- > Blockquotes for excerpts  
- Bullet or numbered lists for key points  
- #, ##, ### for headings  
- ```code blocks``` for structured data

Workflow:
1. Review the provided book context thoroughly.  
2. Reference specific details accurately; if details are missing, discuss themes and broader literary elements.  
3. Tailor response length to the user’s last message:
   • Simple or one-sentence inputs → concise reply (1–2 sentences or a few bullets)  
   • Multi-part or detailed queries → a structured, in-depth answer  
4. Always spell-check and grammar-check proper nouns (titles, names, places).

Maintain conversation context and deliver an engaging, personalized reading experience."""


# SYSTEM_PROMPT = """
# You are an AI reading companion dedicated to enriching users' reading journeys. Your responses should always be formatted in markdown (utilize this to your advantage to increase readability and engagement, **BOLD**, etc.).

# Format your responses using markdown syntax:
# - Use **bold** for emphasis
# - Use *italics* for book titles and character names
# - Use > for quotes from the book
# - Use bullet points or numbered lists when appropriate
# - Use headers (#, ##, ###) to organize longer responses
# - Use code blocks (```) for structured information when relevant

# Your role is to:
# 1. Engage in meaningful discussions about books, adapting your personality to match the selected character (Lily, Max, Sophia, Viktor, etc.).
# 2. Always reference specific details from the provided book context in your responses.
# 3. If book context is provided, ensure all your responses are factually accurate to the book's content.
# 4. If unsure about specific details, focus on discussing the themes and elements you are confident about from the provided context.
# 5. Never break character; always remain in the persona of the selected character.
# 6. Actively use character-specific perspectives to analyze the book's content.
# 7. Before each response, carefully review the provided book context.
# 8. If the book context seems incomplete, focus on the available information and general literary discussion.
# 9. ALWAYS ensure you spell-check and grammar-check your responses. Especially book names, book titles, character names, place names, etc.
# 10. Tailor the length and depth of your response to the user's input:
#    - For concise or simple messages, reply with a brief, focused response (1–2 sentences or a few bullet points).
#    - For more detailed or multi-part queries, provide a thorough, structured reply with appropriate markdown formatting.
# 11. DO NOT include the character's name at the beginning of your responses. Simply respond as the character. NEVER USE "[Character Name]" at the beginning of your responses.

# You maintain context awareness across conversations and aim to create a personalized, enriching reading experience for each user. Your responses should demonstrate clear knowledge of the book's content while maintaining your character's unique perspective.
# """

MAX_TOKENS = 2500  # Maximum response length
TEMPERATURE = 0.7  # Response creativity (0.0 - 1.0)

class ChatService:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.DEBUG)

        self.api_provider = config.AI_PROVIDER.lower()
        self.model = config.AI_MODEL
        self.fallback_model_openrouter = config.FALLBACK_MODEL_OPENROUTER
        self.fallback_model_anthropic = config.FALLBACK_MODEL_ANTHROPIC

        # Use ClientFactory for all providers
        self.openrouter_client = None
        self.openai_client = None
        self.anthropic_client = None
        self.generation_client = None

        try:
            if self.api_provider == "openrouter":
                self.openrouter_client = ClientFactory.create_client("openrouter", self.model, self.logger)
                self.generation_client = self.openrouter_client
                self.logger.info("OpenRouter client configured successfully")
            elif self.api_provider == "anthropic":
                self.anthropic_client = ClientFactory.create_client("anthropic", self.model, self.logger)
                self.openai_client = ClientFactory.create_client("openai", self.model, self.logger)
                self.generation_client = self.anthropic_client
                self.logger.info("Anthropic client configured successfully")
            elif self.api_provider == "openai":
                self.openai_client = ClientFactory.create_client("openai", self.model, self.logger)
                self.generation_client = self.openai_client
                self.logger.info("OpenAI client configured successfully")
            else:
                raise ValueError(f"Invalid AI provider: {self.api_provider}. Must be one of: openai, anthropic, openrouter (case-insensitive)")
        except Exception as e:
            self.logger.error(f"Failed to initialize AI client: {str(e)}")
            self.logger.error(traceback.format_exc())
            raise

        self.logger.info(f"{self.api_provider.capitalize()} client initialized successfully")

    def get_conversation_history(self, user_id: str, book_id: str = None, chat_id: str = None, character: str = None) -> List[Dict]:
        """Load messages from the database for the specific book and chat session"""
        try:
            messages = []
            db_messages = db.get_chat_history(user_id, book_id, chat_id, character)
            
            for msg in db_messages:
                if not msg.message:
                    continue
                messages.append({
                    "role": "user" if msg.is_user else "assistant",
                    "content": msg.message
                })
            
            self.logger.debug(f"Retrieved {len(messages)} messages from history")
            for msg in messages:
                self.logger.debug(f"Message: {msg['role']} -> {msg['content'][:50]}...")
                
            return messages
            
        except Exception as e:
            self.logger.error(f"Error getting conversation history: {str(e)}")
            return []

    def clear_chat_history(self, user_id: str, book_id: str = None, chat_id: str = None) -> bool:
        """Clear chat history for a specific user and optionally for a specific book or chat session."""
        try:
            db.clear_chat_history(user_id, book_id, chat_id)
            return True
        except Exception as e:
            self.logger.error(f"Error clearing chat history: {e}")
            return False

    def _process_message_content(self, content: Any) -> str:
        """Process message content to extract text from various response formats."""
        self.logger.debug(f"Processing message content of type: {type(content)}")
        
        try:
            if isinstance(content, str):
                return content
            elif isinstance(content, dict):
                if 'content' in content:
                    return content['content']
                elif 'message' in content:
                    return content['message']
                elif 'choices' in content and len(content['choices']) > 0:
                    choice = content['choices'][0]
                    if isinstance(choice, dict):
                        if 'message' in choice:
                            message = choice['message']
                            if isinstance(message, dict) and 'content' in message:
                                return message['content']
                            return str(message)
                        elif 'text' in choice:
                            return choice['text']
            elif hasattr(content, 'choices') and len(content.choices) > 0:
                message = content.choices[0].message
                if hasattr(message, 'content'):
                    return message.content
                return str(message)
            
            # If we get here, we couldn't extract content in a standard way
            self.logger.warning(f"Couldn't extract content in a standard way: {content}")
            return str(content)
        except Exception as e:
            self.logger.error(f"Error processing message content: {str(e)}")
            self.logger.error(f"Content that caused error: {content}")
            return "I'm sorry, I couldn't generate a proper response. Please try again."

    def chat(self, message: str, character: str, conversation_id: str, book_id: str = None,
             book_context: str = "", chat_id: str = None, stream: bool = False, book_title: str = None, book_author: str = None) -> Any:
        """Main chat method that routes to the appropriate API based on the configured provider."""
        try:
            self.logger.info(f"Chat request: message='{message[:30]}...', character={character}, user_id={conversation_id}, book_id={book_id}, chat_id={chat_id}, book_title={book_title}, book_author={book_author}")
            
            history = self.get_conversation_history(conversation_id, book_id, chat_id, character)
            self.logger.debug(f"Chat history loaded: {len(history)} messages")
            
            # Pass book_title and book_author to _chat_openai/_chat_anthropic via kwargs
            if self.api_provider == "anthropic":
                self.logger.info("Using Anthropic API for chat")
                response = self._chat_anthropic(message, character, conversation_id, book_id, book_context, chat_id, stream, book_title=book_title, book_author=book_author)
            else:
                self.logger.info(f"Using {self.api_provider} API for chat")
                response = self._chat_openai(message, character, conversation_id, book_id, book_context, chat_id, stream, book_title=book_title, book_author=book_author)

            if not stream:
                # Process and validate the response
                try:
                    self.logger.debug(f"Raw response from API: {response}")
                    response_content = self._process_message_content(response)
                    self.logger.info(f"Processed response content: {response_content[:100]}...")
                    
                    if not response_content or not isinstance(response_content, str):
                        self.logger.error(f"Invalid response content: {response_content}")
                        response_content = "I'm sorry, I couldn't generate a proper response. Please try again."
                    
                    # Don't store the response here - it's stored in the route handler
                    return response_content
                except Exception as e:
                    self.logger.error(f"Error processing chat response: {str(e)}")
                    self.logger.error(f"Traceback: {traceback.format_exc()}")
                    return "I'm sorry, I encountered an error processing the response. Please try again."
            
            return response
        
        except Exception as e:
            self.logger.error(f"Error in chat: {str(e)}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            api_type = self.api_provider.capitalize()
            if "api_key" in str(e).lower():
                return f"{api_type} API key is missing or invalid."
            elif "rate limit" in str(e).lower():
                return "Rate limit exceeded. Please try again later."
            else:
                return f"Chat service error: {str(e)}"

    def _prepare_messages(self, message: str, character_identifier: str, conversation_id: str, book_id: str = None,
                      book_context: str = "", chat_id: str = None) -> Tuple[str, List[Dict]]:
        """Prepare the system prompt and message list for both APIs."""
        from backend.ai.prompt_utils import insert_context, format_chat_context
        
        # Get character by ID or name
        char = character.get_character(character_identifier)
        if not char:
            self.logger.warning(f"Character '{character_identifier}' not found, using default")
            char = character.CHAR_LILY  # Default to Lily if character not found
        
        character_prompt = char.get_prompt()
        character_prompt = insert_context(character_prompt, book_context)
        full_system_prompt = f"{SYSTEM_PROMPT}\n\n{character_prompt}"
        
        conversation_history = self.get_conversation_history(conversation_id, book_id, chat_id)
        formatted_history = []
        for msg in conversation_history:
            content = msg.get('content', '').strip()
            if content:
                formatted_history.append({
                    "role": msg["role"],
                    "content": content
                })

        chat_context = format_chat_context(formatted_history)
        full_system_prompt = insert_context(full_system_prompt, book_context, chat_context)

        return full_system_prompt, formatted_history

    def _chat_openai(self, message: str, character: str, conversation_id: str, book_id: str = None,
                      book_context: str = "", chat_id: str = None, stream: bool = False, book_title: str = None, book_author: str = None) -> Any:
        """Handle chat with OpenAI API."""
        try:
            full_system_prompt, conversation_history = self._prepare_messages(
                message, character, conversation_id, book_id, book_context, chat_id
            )

            messages = [{"role": "system", "content": full_system_prompt}]
            messages.extend(conversation_history)
            
            # Prefer explicit book_title/book_author if provided, else extract from context
            if not book_title or not book_author:
                # Extract book title and author from book_context if available
                if book_context:
                    for line in book_context.split('\n'):
                        if line.startswith("Title:") and not book_title:
                            book_title = line.replace("Title:", "").strip()
                        elif line.startswith("Author:") and not book_author:
                            book_author = line.replace("Author:", "").strip()
            
            # Modify the user message to include book information if available
            enhanced_message = message
            if book_title and book_author:
                enhanced_message = f"[Discussing book: '{book_title}' by {book_author}] {message}"
                self.logger.info(f"Enhanced user message with book information: {enhanced_message[:100]}...")
            
            messages.append({"role": "user", "content": enhanced_message})

            self.logger.debug(f"Sending chat request with {len(messages)} messages")
            self.logger.debug(f"Using API provider: {self.api_provider}, model: {self.model}")
            
            try:
                if self.api_provider == "openrouter":
                    self.logger.debug("Using OpenRouter API")
                    self.logger.debug(f"OpenRouter API key: {self.openrouter_client.client.api_key[:5]}...")
                    self.logger.debug(f"OpenRouter base URL: {self.openrouter_client.base_url}")
                    
                    # Ensure we have a valid model for OpenRouter
                    model_to_use = self.model
                    if not model_to_use or model_to_use == "default":
                        model_to_use = self.model
                        self.logger.info(f"Using default model for OpenRouter: {model_to_use}")

                    try:
                        completion = self.openrouter_client.client.chat.completions.create(
                            model=model_to_use,
                            messages=messages,
                            max_tokens=MAX_TOKENS,
                            temperature=TEMPERATURE,
                            stream=stream
                        )
                        self.logger.debug(f"OpenRouter API response received: {type(completion)}")
                    except Exception as or_error:
                        self.logger.error(f"OpenRouter API error: {str(or_error)}")
                        self.logger.error(f"Traceback: {traceback.format_exc()}")
                        # Try with a different model as fallback (from config)
                        self.logger.info("Trying fallback model for OpenRouter")
                        completion = self.openrouter_client.client.chat.completions.create(
                            model=self.fallback_model_openrouter,
                            messages=messages,
                            max_tokens=MAX_TOKENS,
                            temperature=TEMPERATURE,
                            stream=stream
                        )
                else:
                    self.logger.debug("Using OpenAI API")
                    completion = self.openai_client.chat.completions.create(
                        model=self.model,
                        messages=messages,
                        max_tokens=MAX_TOKENS,
                        temperature=TEMPERATURE,
                        stream=stream
                    )
            except Exception as api_error:
                self.logger.error(f"API call error: {str(api_error)}")
                self.logger.error(f"Traceback: {traceback.format_exc()}")
                
                if "api key" in str(api_error).lower():
                    return f"Invalid API key for {self.api_provider}"
                elif "rate limit" in str(api_error).lower():
                    return f"Rate limit exceeded for {self.api_provider}"
                else:
                    return f"Error calling {self.api_provider} API: {str(api_error)}"

            if stream:
                # For streaming, we need to handle the chat_id in the final chunk
                if chat_id:
                    def process_stream():
                        for chunk in completion:
                            if hasattr(chunk, 'choices') and len(chunk.choices) > 0:
                                delta = chunk.choices[0].delta
                                if hasattr(delta, 'content') and delta.content:
                                    yield {'content': delta.content}
                                elif not hasattr(delta, 'content') or delta.content == "":
                                    # This is likely the final chunk
                                    yield {'done': True, 'chat_id': chat_id}
                            else:
                                # Handle dictionary format
                                if isinstance(chunk, dict):
                                    if 'choices' in chunk and len(chunk['choices']) > 0:
                                        delta = chunk['choices'][0].get('delta', {})
                                        if 'content' in delta and delta['content']:
                                            yield {'content': delta['content']}
                                        elif 'content' not in delta or not delta['content']:
                                            # This is likely the final chunk
                                            yield {'done': True, 'chat_id': chat_id}
                    return process_stream()
                return completion
            else:
                self.logger.debug(f"Received response type: {type(completion)}")
                
                # Handle OpenRouter/OpenAI response
                if hasattr(completion, 'choices') and len(completion.choices) > 0:
                    message = completion.choices[0].message
                    if hasattr(message, 'content'):
                        return message.content
                    else:
                        self.logger.warning(f"Message missing content attribute: {message}")
                        return str(message)
                
                # Fallback for unexpected response format
                self.logger.warning(f"Unexpected response format: {completion}")
                if isinstance(completion, dict):
                    if 'content' in completion:
                        return completion['content']
                    elif 'message' in completion:
                        return completion['message']
                    elif 'choices' in completion and len(completion['choices']) > 0:
                        choice = completion['choices'][0]
                        if isinstance(choice, dict):
                            if 'message' in choice:
                                return choice['message'].get('content', str(choice['message']))
                elif isinstance(completion, str):
                    return completion
                
                # Last resort fallback
                return "I'm sorry, I couldn't generate a proper response. Please try again."
        
        except Exception as e:
            self.logger.error(f"Error in _chat_openai: {str(e)}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return f"Error: {str(e)}"

    def _chat_anthropic(self, message: str, character: str, conversation_id: str, book_id: str = None,
                         book_context: str = "", chat_id: str = None, stream: bool = False, book_title: str = None, book_author: str = None) -> Any:
        """Handle chat with Anthropic API."""
        try:
            system_prompt, conversation_history = self._prepare_messages(
                message, character, conversation_id, book_id, book_context, chat_id
            )
            
            formatted_messages = []
            for msg in conversation_history:
                formatted_messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
            
            # Prefer explicit book_title/book_author if provided, else extract from context
            if not book_title or not book_author:
                if book_context:
                    for line in book_context.split('\n'):
                        if line.startswith("Title:") and not book_title:
                            book_title = line.replace("Title:", "").strip()
                        elif line.startswith("Author:") and not book_author:
                            book_author = line.replace("Author:", "").strip()
            
            # Modify the user message to include book information if available
            enhanced_message = message
            if book_title and book_author:
                enhanced_message = f"[Discussing book: '{book_title}' by {book_author}] {message}"
                self.logger.info(f"Enhanced user message with book information for Anthropic: {enhanced_message[:100]}...")
            
            formatted_messages.append({"role": "user", "content": enhanced_message})
            
            self.logger.debug(f"Prepared messages for Anthropic: {formatted_messages}")
            
            response = self.anthropic_client.messages.create(
                model=self.model,
                max_tokens=1000,
                system=system_prompt,
                messages=formatted_messages,
                stream=stream
            )
            
            if stream:
                def process_stream() -> Generator[Dict, None, None]:
                    for chunk in response:
                        if chunk.type == 'content_block_start':
                            continue
                        elif chunk.type == 'content_block_delta':
                            if hasattr(chunk.delta, 'type') and chunk.delta.type == 'text_delta':
                                yield {'content': chunk.delta.text}
                        elif chunk.type == 'content_block_stop':
                            continue
                        elif chunk.type == 'message_delta':
                            if hasattr(chunk.delta, 'stop_reason'):
                                continue
                        elif chunk.type == 'message_stop':
                            yield {'done': True, 'chat_id': chat_id}
                return process_stream()
            
            if not response.content:
                raise ValueError("Empty response from Anthropic API")
            
            content = response.content[0].text if response.content else ''
            return {'role': 'assistant', 'content': content}
            
        except Exception as e:
            self.logger.error(f"Anthropic API error: {str(e)}")
            raise

    def generate_author_name(self, title: str) -> str:
        """Generate an author name for a book title using GPT-4."""
        try:
            messages = [
                {"role": "system", "content": "You are a helpful assistant that provides author names for books. Respond only with the author's name, nothing else."},
                {"role": "user", "content": f"Who is the author of the book titled '{title}'? If you don't know, make up a plausible author name."}
            ]
            
            completion = self.generation_client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=50,
                temperature=0.7
            )
            author_name = completion.choices[0].message.content.strip()
            self.logger.debug(f"Generated author name: {author_name}")
            return author_name
        except Exception as e:
            self.logger.error(f"Error generating author name: {str(e)}")
            return "Unknown Author"

    def validate_book_title(self, title: str) -> Tuple[str, bool]:
        """Validate and correct a book title using GPT-4.
        Returns a tuple of (corrected_title, was_corrected)"""
        try:
            self.logger.info(f"Validating title: '{title}'")
            messages = [
                {"role": "system", "content": "You are a helpful assistant that validates and corrects book titles. If the title has incorrect spelling, capitalization, or formatting, fix it. If the title is already correct, return it unchanged. Remove any extraneous asterisks or special characters that aren't part of the actual title. Ensure proper capitalization of the first letter of each word and the first letter after a colon. Respond only with the corrected title, nothing else."},
                {"role": "user", "content": f"Please validate and correct this book title if needed: '{title}'"}
            ]
            
            completion = self.generation_client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=100,
                temperature=0.5
            )
            
            corrected_title = completion.choices[0].message.content.strip()
            self.logger.debug(f"AI raw response for title validation: '{corrected_title}'")
            
            corrected_title = corrected_title.strip('"\'* ')
            corrected_title = corrected_title.replace('**', '').replace('* *', '').replace('*', '')
            corrected_title = ' '.join(corrected_title.split())
            corrected_title = ' '.join(word.capitalize() for word in corrected_title.split())
            corrected_title = corrected_title.replace(': ', ': ').replace(':  ', ': ')
            
            was_corrected = corrected_title != title
            self.logger.info(f"Final corrected title: '{corrected_title}' (was corrected: {was_corrected})")
            
            return corrected_title, was_corrected
            
        except Exception as e:
            self.logger.error(f"Error validating book title: {str(e)}")
            self.logger.error(traceback.format_exc())
            return title, False

    def generate_suggestions(self, user_id: str, character_id, force_refresh: bool = False) -> List[Dict]:
        """Generate book suggestions based on user's library and selected character."""
        self.logger.info(f"Generating suggestions for user {user_id} with character {character_id}")
        
        try:
            if not force_refresh:
                stored_suggestions = db.get_book_suggestions(user_id)
                if stored_suggestions:
                    self.logger.info("Returning stored suggestions")
                    return stored_suggestions

            user_books = db.get_books(user_id)
            self.logger.debug(f"Retrieved {len(user_books)} books from user's library")
            
            if not user_books:
                self.logger.info("User has no books in their library")
                return []
                
            book_titles = [book.title for book in user_books]
            book_authors = [book.author for book in user_books]
            self.logger.debug(f"Book titles: {book_titles}")
            self.logger.debug(f"Book authors: {book_authors}")
            
            # Get character information from the character module
            char = character.get_character(character_id)
            if not char:
                self.logger.warning(f"Character '{character_id}' not found, using default")
                char = character.CHAR_AVA  # Default to Ava if character not found
            
            character_name = char.name
            personality_line = next((line for line in char.get_prompt().split('\n') if line.startswith('Personality:')), '')
            personality = personality_line.replace('Personality:', '').strip()
            
            self.logger.info(f"Using character: {character_name}, Personality: {personality}")
            
            prompt = (
                f"As {character_name}, analyze the user's reading history and suggest 3 new books they might enjoy. "
                f"Their library includes: {', '.join(f'{title} by {author}' for title, author in zip(book_titles, book_authors))}. "
                f"Based on these books and your personality as {character_name} ({personality}), provide 3 recommendations. "
                f"Return the recommendations in the following format:\n\n"
                f"Title: [Book Title]\n"
                f"Author: [Author Name]\n"
                f"Description: [A brief description]\n\n"
                f"Title: [Book Title]\n"
                f"Author: [Author Name]\n"
                f"Description: [A brief description]\n\n"
                f"Title: [Book Title]\n"
                f"Author: [Author Name]\n"
                f"Description: [A brief description]\n\n"
                f"Do not include any additional text."
            )

            self.logger.debug(f"Using prompt for suggestions: {prompt}")
            
            try:
                self.logger.info(f"Generating suggestions with {self.api_provider} using prompt: {prompt}")
                
                if self.api_provider == "anthropic":
                    self.logger.info(f"Generating suggestions with {self.api_provider} using prompt: {prompt}")
                    response = self.anthropic_client.messages.create(
                        model=self.model,
                        max_tokens=1000,
                        system=SYSTEM_PROMPT,
                        messages=[{"role": "user", "content": prompt}],
                        stream=False
                    )
                    suggestions_text = response.content[0].text
                    self.logger.info(f"Anthropic API response: {suggestions_text}")
                elif self.api_provider == "openai":
                    self.logger.info(f"Generating suggestions with {self.api_provider} using prompt: {prompt}")
                    response = self.openai_client.chat.completions.create(
                        model=self.model,
                        messages=[
                            {"role": "system", "content": SYSTEM_PROMPT},
                            {"role": "user", "content": prompt}
                        ],
                        temperature=0.7,
                        max_tokens=800
                    )
                elif self.api_provider == "openrouter":
                    self.logger.info(f"Generating suggestions with {self.api_provider} using prompt: {prompt}")
                    response = self.openrouter_client.client.chat.completions.create(
                        model=self.model,
                        messages=[
                            {"role": "system", "content": SYSTEM_PROMPT},
                            {"role": "user", "content": prompt}
                            ],
                            temperature=0.7,
                            max_tokens=800
                    )
                        
                    suggestions_text = response.choices[0].message.content
                    self.logger.info(f"{self.api_provider.capitalize()} API response: {suggestions_text}")
            except Exception as ai_e:
                self.logger.error(f"Error calling AI service for suggestions: {str(ai_e)}")
                raise ValueError(f"Failed to generate suggestions: {str(ai_e)}")
            
            self.logger.info(f"Parsing suggestions from raw text...")
            import re
            pattern = r"Title:\s*(.+?)\s*Author:\s*(.+?)\s*Description:\s*(.+?)(?=Title:|$)"
            matches = re.findall(pattern, suggestions_text, re.DOTALL | re.IGNORECASE)
            self.logger.info(f"Found {len(matches)} matches in the response")
            
            suggestions = []
            for match in matches:
                title, author, description = match
                suggestion = {
                    "title": title.strip().replace('"','').replace('*',''),
                    "author": author.strip().replace('"','').replace('*',''),
                    "description": description.strip()
                }
                self.logger.info(f"Extracted suggestion: {suggestion}")
                suggestions.append(suggestion)
            
            if suggestions:
                db.store_book_suggestions(user_id, suggestions[:3], character_id)
            else:
                self.logger.info("No suggestions generated; using default suggestion")
                suggestions = [{
                    "title": "Explore New Reads",
                    "author": "Our Picks",
                    "description": "Add more books to your library to get personalized suggestions."
                }]
            
            self.logger.info(f"Final suggestions list: {suggestions[:3]}")
            return suggestions[:3]
            
        except Exception as e:
            self.logger.error(f"Error generating suggestions: {str(e)}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return []

    def get_stored_suggestions(self, user_id: str) -> List[Dict]:
        """Get stored suggestions for a user."""
        self.logger.info(f"Getting stored suggestions for user {user_id}")
        return db.get_book_suggestions(user_id)

    def remove_suggestion_from_store(self, user_id: str, suggestion: Dict) -> None:
        """Remove a specific suggestion from the stored suggestions for a user."""
        self.logger.info(f"Removing suggestion from store for user {user_id}: {suggestion.get('title', 'unknown')}")
        
        # Get all suggestions
        all_suggestions = db.get_book_suggestions(user_id)
        if not all_suggestions:
            return
            
        # Filter out the matching suggestion
        updated_suggestions = [s for s in all_suggestions if s.get('title') != suggestion.get('title')]
        
        # Save the updated list back to storage
        if len(updated_suggestions) < len(all_suggestions):
            # First delete all existing suggestions
            try:
                session = db.get_session()
                session.query(BookSuggestion).filter_by(user_id=user_id).delete()
                session.commit()
                
                # Then store the updated suggestions
                if updated_suggestions:
                    db.store_book_suggestions(user_id, updated_suggestions, suggestion.get('character', 'ava'))
                self.logger.info(f"Removed suggestion for user {user_id}, {len(updated_suggestions)} suggestions remaining")
            except Exception as e:
                self.logger.error(f"Error updating suggestions after removal: {str(e)}")
            finally:
                if session:
                    session.close()

    def generate_single_suggestion(self, user_id: str, character_id) -> Dict:
        """Generate a single book suggestion based on user's library."""
        self.logger.info(f"Generating a single suggestion for user {user_id} with character {character_id}")
        
        try:
            user_books = db.get_books(user_id)
            self.logger.debug(f"Retrieved {len(user_books)} books from user's library")
            
            if not user_books:
                self.logger.info("User has no books in their library")
                return None
                
            # Get existing books titles to avoid suggesting books the user already has
            existing_titles = [book.title.lower() for book in user_books]
            
            # Get existing suggestions to avoid duplicates
            existing_suggestions = db.get_book_suggestions(user_id)
            existing_suggestion_titles = [suggestion.get('title', '').lower() for suggestion in existing_suggestions]
            
            # Combine to have a list of all books to avoid
            all_existing_titles = existing_titles + existing_suggestion_titles
            
            book_titles = [book.title for book in user_books]
            book_authors = [book.author for book in user_books]
            
            # Get character information from the character module
            char = character.get_character(character_id)
            if not char:
                self.logger.warning(f"Character '{character_id}' not found, using default")
                char = character.CHAR_AVA  # Default to Ava if character not found
            
            character_name = char.name
            personality_line = next((line for line in char.get_prompt().split('\n') if line.startswith('Personality:')), '')
            personality = personality_line.replace('Personality:', '').strip()
            
            self.logger.info(f"Using character: {character_name}, Personality: {personality}")
            
            prompt = (
                f"As {character_name}, analyze the user's reading history and suggest exactly 1 new book they might enjoy. "
                f"Their library includes: {', '.join(f'{title} by {author}' for title, author in zip(book_titles, book_authors))}. "
                f"Based on these books and your personality as {character_name} ({personality}), provide 1 recommendation. "
                f"IMPORTANT: Do NOT suggest any of these books the user already has or was previously suggested: {', '.join(all_existing_titles[:20])}. "
                f"Return the recommendation in the following format:\n\n"
                f"Title: [Book Title]\n"
                f"Author: [Author Name]\n"
                f"Description: [A brief description]\n\n"
                f"Do not include any additional text."
            )

            self.logger.debug(f"Using prompt for single suggestion: {prompt}")
            
            response_text = self._generate_response_from_prompt(prompt)
            if not response_text:
                self.logger.warning("No response received for single suggestion generation")
                return None
                
            # Parse the response to extract book data
            book_data = self._parse_book_suggestion(response_text)
            if book_data:
                # Add character info
                book_data['character'] = character_id
                self.logger.info(f"Successfully parsed book suggestion: {book_data}")
                return book_data
            else:
                self.logger.warning("Failed to parse book suggestion from response")
                return None
                
        except Exception as e:
            self.logger.error(f"Error generating single suggestion: {str(e)}")
            return None
            
    def _generate_response_from_prompt(self, prompt: str) -> str:
        """Generate a response from the AI using the given prompt."""
        try:
            self.logger.info(f"Generating response with {self.api_provider}")
            
            if self.api_provider == "anthropic":
                response = self.anthropic_client.messages.create(
                    model=self.model,
                    max_tokens=1000,
                    system=SYSTEM_PROMPT,
                    messages=[{"role": "user", "content": prompt}],
                    stream=False
                )
                return response.content[0].text
            elif self.api_provider == "openai":
                response = self.openai_client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": SYSTEM_PROMPT},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=1000,
                    temperature=0.7,
                    stream=False
                )
                return response.choices[0].message.content
            elif self.api_provider == "openrouter":
                model_to_use = self.model
                if not model_to_use or model_to_use == "default":
                    model_to_use = self.model

                try:
                    response = self.openrouter_client.client.chat.completions.create(
                        model=model_to_use,
                        messages=[
                            {"role": "system", "content": SYSTEM_PROMPT},
                            {"role": "user", "content": prompt}
                        ],
                        max_tokens=1000,
                        temperature=0.7,
                        stream=False
                    )
                    return response.choices[0].message.content
                except Exception as or_error:
                    self.logger.error(f"OpenRouter API error: {str(or_error)}")
                    # Try with fallback model from config
                    response = self.openrouter_client.client.chat.completions.create(
                        model=self.fallback_model_openrouter,
                        messages=[
                            {"role": "system", "content": SYSTEM_PROMPT},
                            {"role": "user", "content": prompt}
                        ],
                        max_tokens=1000,
                        temperature=0.7,
                        stream=False
                    )
                    return response.choices[0].message.content

            return None
        except Exception as e:
            self.logger.error(f"Error generating response: {str(e)}")
            return None
            
    def _parse_book_suggestion(self, text: str) -> Dict:
        """Parse book suggestion from AI response text."""
        lines = text.strip().split('\n')
        book = {}
        
        for line in lines:
            line = line.strip()
            if line.startswith('Title:'):
                book['title'] = line.replace('Title:', '').strip()
            elif line.startswith('Author:'):
                book['author'] = line.replace('Author:', '').strip()
            elif line.startswith('Description:'):
                book['description'] = line.replace('Description:', '').strip()
                
        # Continue collecting description lines
        if 'description' in book:
            description_started = False
            additional_description = []
            
            for line in lines:
                line = line.strip()
                if line.startswith('Description:'):
                    description_started = True
                    continue
                    
                if description_started and line and not line.startswith('Title:') and not line.startswith('Author:'):
                    additional_description.append(line)
                    
            if additional_description:
                book['description'] = book['description'] + ' ' + ' '.join(additional_description)
                
        # Validate required fields
        if 'title' in book and 'author' in book and 'description' in book:
            # Generate a random ID for the suggestion
            book['id'] = f"suggestion_{hash(book['title'] + book['author'])}"
            return book
            
        return None
