export const primaryGradientButton = {
  py: 1.5,
  borderRadius: '8px',
  background: 'linear-gradient(45deg, #E6B8A8 30%, #D19B88 90%)',
  boxShadow: '0 3px 5px 2px rgba(3, 3, 3, .3)',
  fontWeight: 'bold',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 6px 10px 2px rgba(3, 3, 3, .3)',
  }
};

export const secondaryOutlinedButton = {
  borderRadius: '8px',
  borderColor: '#E6B8A8',
  color: '#E6B8A8',
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: 'rgba(230, 184, 168, 0.08)',
    borderColor: '#D19B88',
    transform: 'translateY(-2px)',
  }
};