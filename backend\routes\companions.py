from fastapi import APIRouter, Depends, Request, HTTPException, status
from backend.utils.decorators import get_current_user
from backend.data.characters import COMPANION_CHOICES, DEFAULT_CHARACTER
from backend.database.db import db
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/companions", tags=["companions"])

@router.get("/", status_code=status.HTTP_200_OK)
async def get_companions():
    logger.info("Fetching companions (public endpoint)")
    return {
        "companions": COMPANION_CHOICES,
        "default_companion": next((c for c in COMPANION_CHOICES if c['id'] == DEFAULT_CHARACTER), None)
    }

@router.get("/character/{character_id}", status_code=status.HTTP_200_OK)
async def get_character_by_id(character_id: int, user=Depends(get_current_user)):
    logger.info(f"Fetching character {character_id}")
    for companion in COMPANION_CHOICES:
        if companion['id'] == character_id:
            return companion
    raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Character not found")

@router.get("/preference", status_code=status.HTTP_200_OK)
async def get_preference(user=Depends(get_current_user)):
    logger.info(f"Fetching preference for user {user.id}")
    preference = db.get_user_preference(user.id)
    if preference:
        character_id = preference.preferred_companion
        companion = next((c for c in COMPANION_CHOICES if c['id'] == character_id), None)
        return {"companion_id": character_id, "companion": companion, "is_default": character_id == DEFAULT_CHARACTER}
    default_companion = next((c for c in COMPANION_CHOICES if c['id'] == DEFAULT_CHARACTER), None)
    return {"companion_id": DEFAULT_CHARACTER, "companion": default_companion, "is_default": True}

@router.post("/preference", status_code=status.HTTP_200_OK)
async def set_preference(request: Request, user=Depends(get_current_user)):
    data = await request.json()
    companion_id = data.get('companion_id')
    if companion_id is None:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail="companion_id is required")
    if not any(c['id'] == companion_id for c in COMPANION_CHOICES):
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail="Invalid companion_id")
    success = db.set_user_preference(user.id, companion_id)
    if success:
        companion = next((c for c in COMPANION_CHOICES if c['id'] == companion_id), None)
        return {"message": "Preference updated successfully", "companion_id": companion_id, "companion": companion, "is_default": companion_id == DEFAULT_CHARACTER}
    raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update preference")
