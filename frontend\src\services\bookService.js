import api from './axiosConfig';
import RateLimiter, { rateLimitConfig } from '../utils/rateLimiter';
import imageCache from '../utils/imageCache';

const bookFetchRateLimiter = new RateLimiter(rateLimitConfig.books.fetch);
const bookModifyRateLimiter = new RateLimiter(rateLimitConfig.books.modify);

// Track ongoing operations to prevent duplicates
const ongoingDeletions = new Set();
const ongoingBookSwitches = new Set();

// Track auth state to prevent repeated API calls
let isAuthenticated = true; 
let lastAuthCheckTime = 0;
const AUTH_CHECK_INTERVAL = 30000; // 30 seconds

// Cache for book data
let bookCache = new Map();

// Track ongoing cover fetch operations to prevent duplicates
const ongoingCoverFetches = new Map();

const bookService = {
  getBooks: async () => {
    // Don't make API calls if we know the user is not authenticated
    const now = Date.now();
    if (!isAuthenticated && now - lastAuthCheckTime < AUTH_CHECK_INTERVAL) {
      return [];
    }
    
    lastAuthCheckTime = now;
    
    await bookFetchRateLimiter.throttle();
    try {
      const response = await api.get('/api/books');
      isAuthenticated = true;
      return response.data;
    } catch (error) {
      if (error.response?.status === 401) {
        isAuthenticated = false;
        window.dispatchEvent(new CustomEvent('auth-error'));
      }
      throw error;
    }
  },

  deleteBook: async (bookId) => {
    if (ongoingDeletions.has(bookId) || !isAuthenticated) {
      return;
    }
    
    try {
      ongoingDeletions.add(bookId);
      await bookModifyRateLimiter.throttle();
      const response = await api.delete(`/api/books/${bookId}`);
      isAuthenticated = true;
      return response.data;
    } catch (error) {
      if (error.response?.status === 401) {
        isAuthenticated = false;
        window.dispatchEvent(new CustomEvent('auth-error'));
      }
      throw error;
    } finally {
      ongoingDeletions.delete(bookId);
    }
  },

  switchBook: async (bookId) => {
    if (ongoingBookSwitches.has(bookId) || !isAuthenticated) {
      return;
    }

    try {
      ongoingBookSwitches.add(bookId);
      await bookModifyRateLimiter.throttle();
      const response = await api.post(`/api/chat/book-switch/${bookId}`);
      isAuthenticated = true;
      return response.data;
    } catch (error) {
      if (error.response?.status === 401) {
        isAuthenticated = false;
        window.dispatchEvent(new CustomEvent('auth-error'));
      }
      throw error;
    } finally {
      ongoingBookSwitches.delete(bookId);
    }
  },

  addBook: async (bookData) => {
    if (!isAuthenticated) {
      throw new Error("Not authenticated");
    }
    
    await bookModifyRateLimiter.throttle();
    try {
      const response = await api.post('/api/books', bookData);
      isAuthenticated = true;
      return response.data;
    } catch (error) {
      if (error.response?.status === 401) {
        isAuthenticated = false;
        window.dispatchEvent(new CustomEvent('auth-error'));
      }
      throw error;
    }
  },

  // Reset auth state
  resetAuthState: () => {
    isAuthenticated = true;
    lastAuthCheckTime = 0;
    // Dispatch auth-ready to trigger a refresh
    window.dispatchEvent(new CustomEvent('auth-ready'));
  },

  // Clear cache when needed
  clearCache: () => {
    bookCache.clear();
  },

  // Get book cover image from Open Library API
  getBookCover: async (identifier, identifierType = 'isbn', size = 'M') => {
    const cacheKey = `${identifierType}-${identifier}-${size}`;
    
    // Check if there's already an ongoing fetch for this cover
    if (ongoingCoverFetches.has(cacheKey)) {
      // Return the existing promise
      return ongoingCoverFetches.get(cacheKey);
    }
    
    // Create a new promise for this fetch
    const fetchPromise = (async () => {
      try {
        // First check if we have this image cached
        // The consistent ID format helps ensure the same image is retrieved regardless of which component requests it
        const cacheId = `book-cover-${identifierType}-${identifier}-${size}`;
        
        // Try to get the image from the cache first
        const cachedImage = await imageCache.getCachedImage(cacheId);
        if (cachedImage) {
          // Create a new object URL for the cached image and return it
          const objectUrl = URL.createObjectURL(cachedImage.blob);
          return { 
            cover_url: objectUrl, 
            success: true, 
            message: 'Cover loaded from cache',
            fromCache: true
          };
        }
        
        // If not in cache, fetch from API
        await bookFetchRateLimiter.throttle();
        const response = await api.get(`/api/book-covers/${encodeURIComponent(identifier)}?identifier_type=${identifierType}&size=${size}`);
        
        // If we got a valid response with a cover URL, cache it
        if (response.data.success && response.data.cover_url) {
          // Fetch the actual image and cache it
          try {
            const imageResponse = await fetch(response.data.cover_url);
            if (imageResponse.ok) {
              const blob = await imageResponse.blob();
              // Cache the image
              await imageCache.cacheImage(cacheId, response.data.cover_url, blob);
              
              // Return the original URL since components will handle displaying it
              // We don't return an object URL here because the component might not revoke it
            }
          } catch (err) {
            console.warn('Failed to cache book cover image:', err);
            // Continue with the original URL if caching fails
          }
        }
        
        return response.data;
      } catch (error) {
        console.error('Error fetching book cover:', error);
        return { cover_url: null, success: false, message: 'Error fetching cover' };
      } finally {
        // Remove this operation from the ongoing fetches
        ongoingCoverFetches.delete(cacheKey);
      }
    })();
    
    // Store the promise in the ongoing fetches map
    ongoingCoverFetches.set(cacheKey, fetchPromise);
    
    return fetchPromise;
  }
};

export default bookService;
