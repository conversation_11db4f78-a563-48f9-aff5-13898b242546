import { useState, useCallback } from 'react';

const useChatState = () => {
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const addMessage = useCallback((message) => {
    setMessages(prev => [...prev, message]);
  }, []);

  const setMessageHistory = useCallback((history) => {
    setMessages(history);
  }, []);

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(prev => !prev);
  }, []);

  return {
    messages,
    isLoading,
    error,
    isFullscreen,
    addMessage,
    setMessageHistory,
    clearMessages,
    setIsLoading,
    setError,
    toggleFullscreen
  };
};

export default useChatState;
