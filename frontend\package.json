{"name": "bookworm", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@mui/icons-material": "^5.16.7", "@mui/material": "^5.16.7", "axios": "^1.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.6.1", "react-virtuoso": "^4.12.3", "remark-gfm": "^4.0.0", "uuid": "^11.0.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"], "plugins": ["react"], "settings": {"react": {"version": "detect"}}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}