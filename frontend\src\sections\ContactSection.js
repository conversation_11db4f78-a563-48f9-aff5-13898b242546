import React from 'react';
import { Box, Paper, Typography, Grid, Divider, useTheme, useMediaQuery } from '@mui/material';
import ContactForm from '../components/ContactForm';
import EmailIcon from '@mui/icons-material/Email';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import BookIcon from '@mui/icons-material/Book';

/**
 * Contact Section component
 */
const ContactSection = () => {
  return (
    <Box sx={{ 
      maxWidth: '1200px', 
      mx: 'auto', 
      px: { xs: 2, sm: 3, md: 4 },
      pb: 4
    }}>
      <Box sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 4
      }}>
        <Paper 
          elevation={3} 
          sx={{ 
            p: 0, 
            bgcolor: 'background.paper', 
            borderRadius: '16px',
            overflow: 'hidden',
            transition: 'transform 0.3s, box-shadow 0.3s',
            '&:hover': {
              boxShadow: '0 8px 24px rgba(0,0,0,0.12)'
            },
            maxWidth: '800px',
            width: '100%',
            mx: 'auto'
          }}
        >
          <Box sx={{ 
            bgcolor: 'primary.main', 
            p: 2.5, 
            borderTopLeftRadius: '16px', 
            borderTopRightRadius: '16px',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <Typography 
              variant="h6" 
              align="center" 
              sx={{ 
                color: 'text.primary', 
                position: 'relative', 
                zIndex: 1,
                fontWeight: 600
              }}
            >
              Contact Us
            </Typography>
            {/* Decorative pattern */}
            <Box sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              opacity: 0.1,
              backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.8) 1px, transparent 1px)',
              backgroundSize: '15px 15px',
            }} />
          </Box>
          <Box sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
            <ContactForm />
          </Box>
        </Paper>

        <Paper 
          elevation={3} 
          sx={{ 
            p: 0, 
            bgcolor: 'background.paper', 
            borderRadius: '16px',
            overflow: 'hidden',
            transition: 'transform 0.3s, box-shadow 0.3s',
            '&:hover': {
              boxShadow: '0 8px 24px rgba(0,0,0,0.12)'
            },
            maxWidth: '800px',
            width: '100%',
            mx: 'auto'
          }}
        >
          <Box sx={{ 
            bgcolor: 'primary.main', 
            p: 2.5, 
            borderTopLeftRadius: '16px', 
            borderTopRightRadius: '16px',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <Typography 
              variant="h6" 
              align="center" 
              sx={{ 
                color: 'text.primary', 
                position: 'relative', 
                zIndex: 1,
                fontWeight: 600
              }}
            >
              Our Team
            </Typography>
            {/* Decorative pattern */}
            <Box sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              opacity: 0.1,
              backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.8) 1px, transparent 1px)',
              backgroundSize: '15px 15px',
            }} />
          </Box>
          <Box sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
            <Typography variant="h6" align="center" sx={{ mb: 2, fontWeight: 500, color: 'text.primary' }}>
              BookWorm is developed by a passionate team of book lovers and technology enthusiasts.
            </Typography>
            <Typography variant="body1" align="center" color="text.secondary" sx={{ mb: 3 }}>
              We're dedicated to creating innovative reading experiences and helping you discover your next favorite book.
            </Typography>
            
            <Grid container spacing={2} justifyContent="center" sx={{ mt: 2 }}>
              <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                  <EmailIcon color="primary" />
                  <Typography variant="body1"><EMAIL></Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                  <LocationOnIcon color="primary" />
                  <Typography variant="body1">San Francisco, CA</Typography>
                </Box>
              </Grid>
            </Grid>
            
            <Divider sx={{ my: 3 }} />
            
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 2 }}>
              <BookIcon color="secondary" fontSize="large" />
              <Typography variant="h6" color="text.secondary">
                Your AI Reading Companion
              </Typography>
            </Box>
          </Box>
        </Paper>
      </Box>
    </Box>
  );
};

export default ContactSection;