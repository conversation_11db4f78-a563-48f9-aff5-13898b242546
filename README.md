# BookWorm

BookWorm is an AI driven reading companion. It combines a FastAPI backend with a React 18 + Material UI frontend. Authentication is handled through Supabase and the application can use multiple LLM providers to power conversations and book related services.

## ✨ Highlights

- **Modern stack** – React 18 frontend & FastAPI backend
- **Supabase auth** – JWT based authentication with per‑IP rate limiting
- **Pluggable AI** – Works with OpenRouter, Anthropic or OpenAI
- **Smart library** – AI assisted title validation, author generation and reading suggestions
- **Chat companions** – Pick a character and discuss your books with full conversation history
- **Admin tools** – Manage users, books and chats from the built in admin panel
- **Easy local dev** – `python run_local.py` and `npm start` spin up the app with SQLite and hot reload

---

## 📂 Repository layout

```text
BookWorm/
├── backend/               # FastAPI application
│   ├── ai/                # LLM clients and book services
│   │   ├── provider/      # Provider implementations
│   │   ├── character.py   # Companion definitions
│   │   └── ...
│   ├── data/              # Static data such as companion lists
│   ├── database/          # SQLAlchemy models and helpers
│   ├── routes/            # API routes (books, chat, admin, etc.)
│   │   └── chat/          # Chat sub‑routes
│   └── utils/             # Request helpers and auth deps
├── frontend/              # React application
│   ├── public/            # Static assets / avatars
│   └── src/               # Components, hooks and services
├── migrations/            # Alembic migrations
└── run.py / run_local.py  # Production and local entry points
```

---

## 🛠️ Tech stack

| Layer        | Libraries / tools                                   |
| ------------ | --------------------------------------------------- |
| **Backend**  | Python 3 · FastAPI · SQLAlchemy · Alembic           |
| **Frontend** | React 18 · Material UI · Axios                      |
| **Database** | PostgreSQL (prod) · SQLite (dev)                    |
| **Auth**     | Supabase JWT                                        |
| **AI**       | OpenRouter · Anthropic · OpenAI                     |
| **Ops**      | Uvicorn · Sentry                                    |

---

## ⚡ Quick start

1. Clone the repo and create an environment file with your API keys and database settings.

```bash
git clone https://github.com/your-org/bookworm.git
cd bookworm
cp frontend/.env.local.example frontend/.env.local  # edit if needed
```

2. Start the backend and frontend in development mode (SQLite with auto reload).

```bash
# Backend
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python run_local.py           # http://127.0.0.1:5000

# Frontend
cd frontend && npm install
npm start                     # http://localhost:3000
```

---

## 🔑 Environment variables

The backend reads values from `.env` or your shell. Important ones include:

| Variable                 | Description                                   |
| ------------------------ | --------------------------------------------- |
| `APP_ENV`                | `development` or `production`                 |
| `BW_LOCAL_MODE`          | Use SQLite and reload if `true`               |
| `SECRET_KEY`             | Session secret for FastAPI                    |
| `POSTGRES_*`             | Production database settings                  |
| `SUPABASE_URL`           | Supabase project URL                          |
| `SUPABASE_API_KEY`       | Supabase service role key                     |
| `OPENAI_API_KEY`         | OpenAI key (optional when using OpenRouter)   |
| `ANTHROPIC_API_KEY`      | Anthropic key (optional)                      |
| `OPENROUTER_API_KEY`     | OpenRouter key                                |
| `OPENROUTER_API_BASE`    | Base URL for OpenRouter API                   |
| `AI_PROVIDER`            | `openrouter`, `anthropic` or `openai`         |
| `AI_MODEL`               | Default model name for the chosen provider    |
| `SENTRY_DSN`             | Optional error reporting                      |

---

## ▶️ Commands

| Command                                                     | Purpose                          |
| ----------------------------------------------------------- | -------------------------------- |
| `python run_local.py`                                       | Start API in local dev mode      |
| `python run.py`                                             | Run API using configured env     |
| `npm start`                                                 | React dev server                 |
| `alembic revision --autogenerate -m "msg" && alembic upgrade head` | Create and apply migrations      |
| `pytest`                                                    | Run backend tests                |
| `npm test`                                                  | Run frontend tests               |
| `python make_admin.py <username>`                           | Promote a user to admin          |

---

## 🌐 Key API routes

| Path                       | Methods                | Purpose                                      |
| -------------------------- | ---------------------- | -------------------------------------------- |
| `/api/auth/*`              | `POST/GET`             | Authentication and user info                 |
| `/api/books/*`             | `GET/POST/DELETE`      | Manage user library                          |
| `/api/chat/*`              | `GET/POST`             | Chat endpoints                               |
| `/api/companions/*`        | `GET/POST`             | Companion info and preferences               |
| `/api/preferences/*`       | `GET/POST/PUT`         | User preferences                             |
| `/api/suggestions/*`       | `GET/POST`             | AI book suggestions                          |
| `/api/ai-book/*`           | `POST`                 | AI generated book content                    |
| `/api/book-covers/*`       | `GET`                  | Fetch cover images                           |
| `/api/contact`             | `POST`                 | Submit contact messages                      |
| `/api/admin/*`             | `GET/POST/PUT/DELETE`  | Admin dashboard                              |
| `/health`                  | `GET`                  | Health check                                 |

---

## 🧠 AI features

1. **Title validation** – check and correct book titles
2. **Author suggestion** – guess or fetch likely authors
3. **Personal companions** – chat as Lily, Max, Maya, Viktor and more
4. **Reading recommendations** – suggestions based on your library
5. **AI generated books** – create short books from prompts

---

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Run tests before submitting
4. Open a pull request with a detailed description

---

## 📄 License

MIT

---

*Image assets must exist at `docs/assets/library.png` and `docs/assets/chat.gif` for screenshots to render.*
