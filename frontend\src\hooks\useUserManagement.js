import { useState, useEffect, useRef } from 'react';
import supabaseAuth, { supabase } from '../services/supabaseService';
import preferencesService from '../services/preferencesService';
import api from '../services/axiosConfig';
import { characters } from '../data/characters';

/**
 * Custom hook for managing user state and related functions
 */
const useUserManagement = () => {
  const [currentUser, setCurrentUser] = useState(null);
  const [showDefaultCompanionAlert, setShowDefaultCompanionAlert] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authLoading, setAuthLoading] = useState(false);
  const [lastAuthError, setLastAuthError] = useState(null);
  const didInitialize = useRef(false); // Ref to track initialization

  // Move initializeUser declaration before first usage
  const initializeUser = async () => {
    let initializingPromise = null;

    initializingPromise = (async () => {
      let sessionTimeoutId = null;
      try {
        setAuthLoading(true);
        console.log('InitializeUser: Setting auth loading TRUE');

        // --- Refactored getSession with specific timeout using Promise.race ---
        const sessionPromise = supabase.auth.getSession();
        const timeoutPromise = new Promise((_, reject) => {
          sessionTimeoutId = setTimeout(() => {
            reject(new Error('getSession timed out after 10 seconds'));
          }, 10000); // 10 seconds timeout specifically for getSession
        });

        let sessionResult;
        try {
          console.log(`[${new Date().toISOString()}] BEFORE racing supabase.auth.getSession`);
          sessionResult = await Promise.race([sessionPromise, timeoutPromise]);
          console.log(`[${new Date().toISOString()}] AFTER racing supabase.auth.getSession`);
        } catch (timeoutError) {
          console.warn('InitializeUser: supabase.auth.getSession() timed out.', timeoutError.message);
          setIsAuthenticated(false);
          setCurrentUser(null);
          setLastAuthError('Authentication check timed out. Please try logging in again.');
          setAuthLoading(false);
          initializingPromise = null;
          if (sessionTimeoutId) clearTimeout(sessionTimeoutId);
          return;
        } finally {
            if (sessionTimeoutId) clearTimeout(sessionTimeoutId);
        }

        const { data: { session }, error: sessionError } = sessionResult;
        console.log('Supabase session retrieved:', session);

        if (sessionError) {
          console.error('Error explicitly returned by Supabase getSession:', sessionError);
          setIsAuthenticated(false);
          setCurrentUser(null);
          setLastAuthError(`Session retrieval error: ${sessionError.message}`);
        }

        if (!session) {
          console.log('No active session found.');
          setIsAuthenticated(false);
          setCurrentUser(null);
          setAuthLoading(false);
          console.log('InitializeUser: Setting auth loading FALSE (no session)');
          initializingPromise = null;
          return;
        }

        console.log('Session found, attempting to get user profile...');
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        console.log('Retrieved user:', user);
        if (userError) {
            console.error('Error retrieving Supabase user:', userError);
            throw userError;
        }

        if (!user) {
          console.warn('Session exists but getUser returned null.');
          setIsAuthenticated(false);
          setCurrentUser(null);
          setAuthLoading(false);
          console.log('InitializeUser: Setting auth loading FALSE (session but no user)');
          initializingPromise = null;
          return;
        }

        const username = user.user_metadata?.username || user.email?.split('@')[0];
        const email = user.email;

        const userProfile = {
          id: user.id,
          username,
          email,
          isAdmin: user.app_metadata?.isAdmin || false
        };

        setCurrentUser(userProfile);
        setIsAuthenticated(true);
        setLastAuthError(null);
        
        // Dispatch auth-ready event
        window.dispatchEvent(new CustomEvent('auth-ready'));

      } catch (error) {
        console.error('Error during user initialization process:', error);
        setIsAuthenticated(false);
        setCurrentUser(null);
        setLastAuthError(error.message || 'Authentication failed during initialization');
      } finally {
        setAuthLoading(false);
        initializingPromise = null;
        if (sessionTimeoutId) clearTimeout(sessionTimeoutId);
      }
    })();
    return initializingPromise;
  };

  // Initialize user state on mount
  useEffect(() => {
    if (!didInitialize.current) {
        didInitialize.current = true; // Set flag immediately
        console.log('useUserManagement first mount');
        console.log('useUserManagement calling initializeUser');
        initializeUser();
    } else {
        console.log('useUserManagement subsequent mount (StrictMode?), skipping initializeUser.');
    }
  }, []); // Empty dependency array ensures this runs only once on mount

  // Set up auth state listener
  useEffect(() => {
    console.log('[Auth Listener] Setting up onAuthStateChange listener.');
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log(`[Auth Listener] Event received: ${event}, Session:`, session);
      if (event === 'INITIAL_SESSION') {
        // Handled by initializeUser on mount, but good to log
        console.log('[Auth Listener] INITIAL_SESSION event.');
        // Optionally re-run initializeUser or just update state if session exists
        if (session && session.user) {
            const user = session.user;
            const username = user.user_metadata?.username || user.email?.split('@')[0];
            const userProfile = {
              id: user.id,
              username,
              email: user.email,
              isAdmin: user.app_metadata?.isAdmin || false
            };
            setCurrentUser(userProfile);
            setIsAuthenticated(true);
            setLastAuthError(null);
            console.log('[Auth Listener] State updated from INITIAL_SESSION.');
            
            // Dispatch auth-ready event
            window.dispatchEvent(new CustomEvent('auth-ready'));
        } else {
            // If INITIAL_SESSION has no user, ensure logged out state
            setCurrentUser(null);
            setIsAuthenticated(false);
        }
      } else if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED' || event === 'USER_UPDATED') {
        console.log(`[Auth Listener] ${event} event detected.`);
        if (session && session.user) {
          const user = session.user;
          console.log('[Auth Listener] User found in session object:', user);
          // Construct the same profile object as in initializeUser
          const username = user.user_metadata?.username || user.email?.split('@')[0];
          const userProfile = {
            id: user.id,
            username,
            email: user.email,
            isAdmin: user.app_metadata?.isAdmin || false
          };
          setCurrentUser(userProfile);
          setIsAuthenticated(true);
          setLastAuthError(null); // Clear any previous error
          console.log('[Auth Listener] State updated with user profile.');
          
          // Dispatch auth-ready event
          window.dispatchEvent(new CustomEvent('auth-ready'));
        } else {
          // This case might happen if the session exists but user details are missing
          // Or if TOKEN_REFRESHED happens without a user (unlikely but possible)
          console.warn('[Auth Listener] Session exists but user details missing, attempting getUser() fallback.');
          // Re-use the getUser logic from initializeUser or simplify if needed
          // For simplicity, let's just log out if user is unexpectedly null
           try {
             const { data: { user }, error: getUserError } = await supabase.auth.getUser();
             if (getUserError || !user) {
               console.error('[Auth Listener] Fallback getUser failed or returned no user:', getUserError);
               setCurrentUser(null);
               setIsAuthenticated(false);
               setLastAuthError('Failed to verify user session.');
             } else {
                const username = user.user_metadata?.username || user.email?.split('@')[0];
                const userProfile = {
                  id: user.id,
                  username,
                  email: user.email,
                  isAdmin: user.app_metadata?.isAdmin || false
                };
                setCurrentUser(userProfile);
                setIsAuthenticated(true);
                setLastAuthError(null);
                console.log('[Auth Listener] State updated via fallback getUser().');
                
                // Dispatch auth-ready event
                window.dispatchEvent(new CustomEvent('auth-ready'));
             }
           } catch(error) {
               console.error('[Auth Listener] Error during fallback getUser:', error);
               setCurrentUser(null);
               setIsAuthenticated(false);
               setLastAuthError('Error verifying user session.');
           }
        }
      } else if (event === 'SIGNED_OUT') {
        console.log('[Auth Listener] SIGNED_OUT event detected.');
        setCurrentUser(null);
        setIsAuthenticated(false);
        setLastAuthError(null); // Clear errors on logout
        console.log('[Auth Listener] State updated: currentUser=null, isAuthenticated=false');
      } else {
          console.log(`[Auth Listener] Unhandled event: ${event}`);
      }
    });

    console.log('[Auth Listener] Listener setup complete.');

    // Cleanup function
    return () => {
        console.log('[Auth Listener] Cleaning up listener.');
        if (subscription) {
            subscription.unsubscribe();
        } else {
            console.warn("[Auth Listener] Subscription object not found on cleanup.");
        }
    };
  }, []); // Keep this listener setup running for the lifetime of the hook

  // Login with email and password
  const handleEmailPasswordLogin = async (email, password) => {
    let timeoutId;
    console.log('[Login Handler] Attempting login for:', email);
    try {
      setAuthLoading(true);
      console.log('[Login Handler] AuthLoading set to true.');

      timeoutId = setTimeout(() => {
        console.warn('Login request timed out');
        setAuthLoading(false);
        setLastAuthError('Login request timed out. Please try again.');
      }, 10000);

      console.log('[Login Handler] Calling supabase.auth.signInWithPassword...');
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      console.log('[Login Handler] signInWithPassword call completed.');

      if (error) {
        console.error('[Login Handler] signInWithPassword error:', error);
        setLastAuthError(error.message || 'Login failed');
        throw error;
      }

      console.log('[Login Handler] signInWithPassword successful. Data:', data);
      setLastAuthError(null);
      console.log('[Login Handler] Login successful, returning true.');
      return true;
    } catch (error) {
      console.error('[Login Handler] Caught error during login:', error);
      return false;
    } finally {
      setAuthLoading(false);
      console.log('[Login Handler] AuthLoading set to false (finally block).');
      if (timeoutId) clearTimeout(timeoutId);
    }
  };

  // Register new user
  const handleRegister = async (email, password, username) => {
    let timeoutId;
    try {
      setAuthLoading(true);

      timeoutId = setTimeout(() => {
        console.warn('Registration request timed out');
        setAuthLoading(false);
        setLastAuthError('Registration request timed out. Please try again.');
      }, 10000);

      // Use authService to register user in both Supabase and backend
      const { default: authService } = await import('../services/authService');
      const result = await authService.register(email, password, username);

      setLastAuthError(null);
      return result;
    } catch (error) {
      setLastAuthError(error.message || 'Registration failed');
      // Return object with needsConfirmation false on error
      return { needsConfirmation: false, error: error.message || 'Registration failed' };
    } finally {
      setAuthLoading(false);
      if (timeoutId) clearTimeout(timeoutId);
    }
  };

  // Handle user change
  const handleUserChange = async (user) => {
    setCurrentUser(user);
    setIsAuthenticated(!!user);
    if (!user) setLastAuthError(null);
  };

  // Handle logout
  const handleLogout = async () => {
    let timeoutId;
    try {
      setAuthLoading(true);

      timeoutId = setTimeout(() => {
        console.warn('Logout request timed out');
        setAuthLoading(false);
      }, 5000);

      await supabase.auth.signOut();
      setLastAuthError(null);
    } catch (error) {
      console.error('Logout error:', error);
      setLastAuthError(error.message || 'Logout failed');
    } finally {
      setAuthLoading(false);
      if (timeoutId) clearTimeout(timeoutId);
    }
  };

  // Handle closing the default companion alert
  const handleDefaultCompanionAlertClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setShowDefaultCompanionAlert(false);
  };

  // Load companion preference
  const loadCompanionPreference = async (setSelectedCharacter) => {
    try {
      const prefData = await preferencesService.getCompanionPreference();

      if (prefData.companion) {
        const character = characters.find(c => c.id === prefData.companion);
        if (character) {
          setSelectedCharacter(character);
          if (prefData.isDefault) {
            setShowDefaultCompanionAlert(true);
          }
        }
      }
    } catch (error) {
      console.error('Error loading companion preference:', error);
    }
  };

  return {
    currentUser,
    isAuthenticated,
    authLoading,
    lastAuthError,
    showDefaultCompanionAlert,
    handleEmailPasswordLogin,
    handleRegister,
    handleUserChange,
    handleLogout,
    handleDefaultCompanionAlertClose,
    loadCompanionPreference,
    setShowDefaultCompanionAlert
  };
};

export default useUserManagement;