import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  TextField, 
  // Typography, 
  // Paper,
  CircularProgress,
  Link,
  Alert
} from '@mui/material';
import { useAppContext } from '../context/AppContext';
import LoginPopup from './LoginPopup';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import { primaryGradientButton, secondaryOutlinedButton } from './styled/buttonStyles';

const RegisterForm = ({ onSuccess }) => {
  console.log('[DEBUG] RegisterForm mounted. onSuccess is', typeof onSuccess);
  const { handleRegister, authLoading } = useAppContext();
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');

  const validateEmail = (email) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('[DEBUG] RegisterForm.handleSubmit called');
    
    // Form validation
    if (!username.trim()) {
      setError('Username is required');
      return;
    }
    
    if (!email.trim() || !validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }
    
    if (!password || password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }
    
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    try {
      setError('');
      const result = await handleRegister(email, password, username);

      if (result && result.user) {
        setUsername('');
        setEmail('');
        setPassword('');
        setConfirmPassword('');
        if (onSuccess) {
          onSuccess();
        }
        return;
      }
      if (result && result.needsConfirmation) {
        setUsername('');
        setEmail('');
        setPassword('');
        setConfirmPassword('');
        if (onSuccess) {
          onSuccess();
        }
        return;
      }
      if (result.error) {
        setError('Registration failed: ' + result.error);
        return;
      }
      // fallback error
      setError('Registration failed. Please try again.');
    } catch (error) {
      console.error('Registration error:', error);
      setError('Registration failed: ' + (error.message || 'Please try again.'));
    }
  };

  return (
    <Box sx={{ maxWidth: 400, width: '100%', mx: 'auto' }}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <TextField
          label="Username"
          variant="outlined"
          fullWidth
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          sx={{ mb: 2 }}
          disabled={authLoading}
        />
        <TextField
          label="Email"
          type="email"
          variant="outlined"
          fullWidth
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          sx={{ mb: 2 }}
          disabled={authLoading}
        />
        <TextField
          label="Password"
          type="password"
          variant="outlined"
          fullWidth
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          sx={{ mb: 2 }}
          disabled={authLoading}
          helperText="Password must be at least 6 characters"
        />
        <TextField
          label="Confirm Password"
          type="password"
          variant="outlined"
          fullWidth
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          sx={{ mb: 3 }}
          disabled={authLoading}
        />

        <Button
          type="submit"
          variant="contained"
          fullWidth
          disabled={authLoading}
          sx={primaryGradientButton}
          startIcon={<PersonAddIcon />}
        >
          {authLoading ? <CircularProgress size={24} /> : 'Register'}
        </Button>

        <Box sx={{ mt: 3, textAlign: 'center', display: 'flex', justifyContent: 'center' }}>
          <Button
            variant="outlined"
            sx={secondaryOutlinedButton}
            startIcon={null}
            onClick={() => {
              // Instead of reload, consider opening login popup via parent state
              // This is now a no-op; handled in parent if needed
            }}
          >
            Already have an account? Log In
          </Button>
        </Box>
      </form>
    </Box>
  );
};

export default RegisterForm; 