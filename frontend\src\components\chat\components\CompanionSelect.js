import React, { useCallback, useMemo } from 'react';
import {
  Box,
  FormControl,
  Select,
  MenuItem,
  Avatar,
  CircularProgress
} from '@mui/material';

// Preload companion avatars to prevent flicker during selection
const preloadCompanionAvatars = (companions) => {
  if (!companions || !Array.isArray(companions)) return;
  companions.forEach(companion => {
    if (companion.avatar) {
      const img = new Image();
      img.src = companion.avatar;
    }
  });
};

const CompanionSelect = React.memo(({ 
  value, 
  onChange, 
  disabled, 
  companions,
  isLoading
}) => {
  // Preload avatars when companions change
  React.useEffect(() => {
    preloadCompanionAvatars(companions);
  }, [companions]);

  const handleChange = useCallback((event) => {
    if (!event.target.value) return;
    onChange(event.target.value);
  }, [onChange]);

  // Convert value to string for comparison only once
  const currentValue = useMemo(() => 
    value?.toString() || '', 
    [value]
  );

  // Create companion MenuItem elements only when companions array changes
  const companionItems = useMemo(() => {
    if (!companions || !Array.isArray(companions)) return [];
    
    return companions.map((companion) => (
      <MenuItem 
        key={companion.id} 
        value={companion.id.toString()}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Avatar
            src={companion.avatar}
            alt={companion.name}
            sx={{ width: 24, height: 24, mr: 1 }}
          />
          {companion.name}
        </Box>
      </MenuItem>
    ));
  }, [companions]);

  // Return a stable reference if nothing changed
  return (
    <FormControl fullWidth disabled={disabled} sx={{ width: '250px' }}>
      <Select
        value={currentValue}
        onChange={handleChange}
        displayEmpty
        disabled={disabled}
      >
        <MenuItem value="">
          <em>Select a companion</em>
        </MenuItem>
        {companionItems}
      </Select>
      {isLoading && (
        <CircularProgress 
          size={24}
          sx={{
            position: 'absolute',
            right: 32,
            top: '50%',
            marginTop: '-12px'
          }}
        />
      )}
    </FormControl>
  );
}, (prevProps, nextProps) => {
  // More precise equality checking for proper memoization
  if (prevProps.disabled !== nextProps.disabled || 
      prevProps.isLoading !== nextProps.isLoading) {
    return false;
  }
  
  // Only do deep comparison on companions if reference changed
  if (prevProps.companions !== nextProps.companions) {
    // Quick length check first
    if (!prevProps.companions || !nextProps.companions || 
        prevProps.companions.length !== nextProps.companions.length) {
      return false;
    }
    // We could add deeper equality check for companions here if needed
  }
  
  // String comparison for value to handle both string and number IDs
  return String(prevProps.value) === String(nextProps.value);
});

export default CompanionSelect;
