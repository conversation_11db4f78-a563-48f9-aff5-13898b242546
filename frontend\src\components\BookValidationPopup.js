import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  CircularProgress,
  Alert
} from '@mui/material';
import suggestionsService from '../services/suggestionsService';

function BookValidationPopup({ open, title, onClose, onConfirm }) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [validatedTitle, setValidatedTitle] = useState('');
  const [suggestedAuthor, setSuggestedAuthor] = useState('');

  const handleFetchValidation = async () => {
    setLoading(true);
    setError(null);
    try {
      const verifyResult = await suggestionsService.verifyBook(title);
      const generateResult = await suggestionsService.generateAISuggestions(title);

      setValidatedTitle(verifyResult.corrected_title || title);
      setSuggestedAuthor(generateResult.generated_author || '');
    } catch (err) {
      console.error('Error fetching AI validation:', err);
      setError('Failed to fetch AI suggestions. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleConfirm = () => {
    onConfirm({
      title: validatedTitle || title,
      author: suggestedAuthor
    });
    onClose();
  };

  React.useEffect(() => {
    if (open && title) {
      handleFetchValidation();
    }
  }, [open, title]);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>AI Suggestions for Book Entry</DialogTitle>
      <DialogContent dividers>
        {loading ? (
          <CircularProgress />
        ) : error ? (
          <Alert severity="error">{error}</Alert>
        ) : (
          <>
            <Typography variant="subtitle1" gutterBottom>
              Original Title: {title}
            </Typography>
            <Typography variant="subtitle1" gutterBottom>
              Suggested Title: {validatedTitle}
            </Typography>
            <Typography variant="subtitle1" gutterBottom>
              Suggested Author: {suggestedAuthor || 'Unknown'}
            </Typography>
          </>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="secondary" disabled={loading}>
          Cancel
        </Button>
        <Button onClick={handleConfirm} color="primary" disabled={loading || !!error}>
          Accept Suggestions
        </Button>
      </DialogActions>
    </Dialog>
  );
}

BookValidationPopup.propTypes = {
  open: PropTypes.bool.isRequired,
  title: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired
};

export default BookValidationPopup;