/* Mobile-specific styles for BookWorm application */

/* Prevent text size adjustment after orientation changes */
html {
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* Fix for iOS 100vh issue (viewport height calculation) */
.full-height {
  height: 100vh;
  height: -webkit-fill-available;
  height: fill-available;
}

/* Disable pull-to-refresh on iOS */
html, body {
  overscroll-behavior-y: none;
  -webkit-overflow-scrolling: touch;
}

/* Improve tap target sizes for better mobile UX */
button, 
a, 
input[type="button"], 
input[type="submit"],
input[type="reset"],
input[type="checkbox"],
input[type="radio"] {
  min-height: 44px;
  min-width: 44px;
}

/* Prevent iOS zoom on input focus */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="search"],
input[type="tel"],
input[type="url"],
textarea {
  font-size: 16px !important;
}

/* Fix for iOS Safari bottom bar */
.has-safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Fix for iOS Safari top bar (notch area) */
.has-safe-area-top {
  padding-top: env(safe-area-inset-top);
}

/* Disable text selection on interactive elements */
.no-select {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* Improve scrolling performance */
.scroll-container {
  -webkit-overflow-scrolling: touch;
  overflow-y: scroll;
  overscroll-behavior-y: contain;
}

/* Add momentum scrolling for iOS */
.momentum-scroll {
  -webkit-overflow-scrolling: touch;
  overflow-y: scroll;
}

/* Fix for iOS Safari landscape orientation */
@media screen and (orientation: landscape) {
  .ios-landscape-fix {
    height: 100vw;
    position: relative;
  }
}

/* Fix for iOS keyboard appearance */
.ios-keyboard-fix {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

/* Prevent content from being hidden under the iOS home indicator */
.ios-home-indicator-padding {
  padding-bottom: 34px;
}

/* Prevent content from being hidden under the iOS notch */
.ios-notch-padding {
  padding-top: 44px;
}

/* Add smooth transitions for better UX */
.mobile-transition {
  transition: all 0.3s ease;
}

/* Optimize touch feedback */
.touch-feedback {
  transition: opacity 0.2s;
}
.touch-feedback:active {
  opacity: 0.7;
}

/* Optimize mobile typography */
@media (max-width: 600px) {
  h1 {
    font-size: 1.8rem !important;
  }
  h2 {
    font-size: 1.5rem !important;
  }
  h3 {
    font-size: 1.3rem !important;
  }
  h4 {
    font-size: 1.2rem !important;
  }
  h5 {
    font-size: 1.1rem !important;
  }
  h6 {
    font-size: 1rem !important;
  }
  p, li, span, button, a {
    font-size: 0.95rem !important;
  }
}
