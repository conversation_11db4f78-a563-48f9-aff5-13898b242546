import { useTheme } from '@mui/material/styles';

/**
 * Hook to detect device type and provide responsive design helpers
 * 
 * @returns {Object} Device information and helper functions
 */
const useDeviceDetection = () => {
  const theme = useTheme();
  
  // Extract device info from theme (set by ResponsiveThemeProvider)
  const { deviceInfo = {
    isMobile: false,
    isIPhone: false,
    viewportWidth: 0,
    viewportHeight: 0
  }} = theme;
  
  // Helper functions for responsive design
  const isSmallScreen = deviceInfo.viewportWidth < 600;
  const isMediumScreen = deviceInfo.viewportWidth >= 600 && deviceInfo.viewportWidth < 960;
  const isLargeScreen = deviceInfo.viewportWidth >= 960;
  
  // iOS Safari specific detection (for handling iOS quirks)
  const isIOSSafari = deviceInfo.isIPhone && /Safari/i.test(navigator.userAgent) && !/Chrome/i.test(navigator.userAgent);
  
  // iPhone model detection (approximate based on screen size)
  const getIPhoneModel = () => {
    if (!deviceInfo.isIPhone) return null;
    
    const { viewportWidth, viewportHeight } = deviceInfo;
    const screenSize = Math.max(viewportWidth, viewportHeight);
    
    if (screenSize <= 667) return 'small'; // iPhone 8 and smaller
    if (screenSize <= 812) return 'medium'; // iPhone X, 11 Pro, 12 mini, 13 mini
    if (screenSize <= 926) return 'large'; // iPhone 11, 11 Pro Max, 12, 12 Pro, 13, 13 Pro
    return 'xlarge'; // iPhone 12 Pro Max, 13 Pro Max and larger
  };
  
  // Safe area insets for notched iPhones
  const getSafeAreaInsets = () => {
    if (!deviceInfo.isIPhone) return { top: 0, right: 0, bottom: 0, left: 0 };
    
    const model = getIPhoneModel();
    
    // Default insets for notched iPhones
    if (['medium', 'large', 'xlarge'].includes(model)) {
      return {
        top: 44, // Status bar height
        right: 0,
        bottom: 34, // Home indicator area
        left: 0
      };
    }
    
    return { top: 20, right: 0, bottom: 0, left: 0 };
  };
  
  return {
    ...deviceInfo,
    isSmallScreen,
    isMediumScreen,
    isLargeScreen,
    isIOSSafari,
    getIPhoneModel,
    getSafeAreaInsets
  };
};

export default useDeviceDetection;
